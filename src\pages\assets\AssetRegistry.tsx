
import React, { useState, useMemo, useCallback } from 'react';
import { AppLayout } from '@/components/layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Filter,
  Plus,
  Download,
  Upload,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Grid3X3,
  List,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { SAMPLE_ASSETS, ASSET_CATEGORIES, DEPARTMENTS, LOCATIONS } from '@/data/assetSampleData';
import { Asset, AssetFilters, AssetStatus, AssetCondition, AssetCategory } from '@/types/asset';
import { ContextTooltip, tooltipContent } from '@/components/ui/ContextTooltip';

// Asset Registry Component
const AssetRegistry: React.FC = () => {
  const [assets] = useState<Asset[]>(SAMPLE_ASSETS);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<AssetFilters>({});
  const [sortConfig, setSortConfig] = useState<{ field: keyof Asset; direction: 'asc' | 'desc' }>({
    field: 'assetId',
    direction: 'asc'
  });
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  // Filter and search logic
  const filteredAssets = useMemo(() => {
    let filtered = assets;

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(query) ||
        asset.assetId.toLowerCase().includes(query) ||
        asset.manufacturer.toLowerCase().includes(query) ||
        asset.model.toLowerCase().includes(query) ||
        asset.serialNumber.toLowerCase().includes(query) ||
        asset.department.toLowerCase().includes(query) ||
        asset.assignedUser?.name.toLowerCase().includes(query)
      );
    }

    // Apply filters
    if (filters.categories?.length) {
      filtered = filtered.filter(asset => filters.categories!.includes(asset.category));
    }
    if (filters.statuses?.length) {
      filtered = filtered.filter(asset => filters.statuses!.includes(asset.status));
    }
    if (filters.conditions?.length) {
      filtered = filtered.filter(asset => filters.conditions!.includes(asset.condition));
    }
    if (filters.departments?.length) {
      filtered = filtered.filter(asset => filters.departments!.includes(asset.department));
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [assets, searchQuery, filters, sortConfig]);

  // Pagination
  const paginatedAssets = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredAssets.slice(startIndex, startIndex + pageSize);
  }, [filteredAssets, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredAssets.length / pageSize);

  // Statistics
  const stats = useMemo(() => {
    const totalValue = assets.reduce((sum, asset) => sum + asset.currentValue, 0);
    const activeAssets = assets.filter(asset => asset.status === 'Active').length;
    const maintenanceAssets = assets.filter(asset => asset.status === 'Maintenance').length;
    const avgUtilization = assets.reduce((sum, asset) => sum + (asset.utilizationRate || 0), 0) / assets.length;

    return {
      totalAssets: assets.length,
      totalValue,
      activeAssets,
      maintenanceAssets,
      avgUtilization: Math.round(avgUtilization)
    };
  }, [assets]);

  // Event handlers
  const handleSort = useCallback((field: keyof Asset) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  }, []);

  const handleSelectAsset = useCallback((assetId: string) => {
    setSelectedAssets(prev =>
      prev.includes(assetId)
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  }, []);

  const handleSelectAll = useCallback(() => {
    setSelectedAssets(prev =>
      prev.length === paginatedAssets.length
        ? []
        : paginatedAssets.map(asset => asset.id)
    );
  }, [paginatedAssets]);

  const getStatusColor = (status: AssetStatus) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'Inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      case 'Maintenance': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'Disposed': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getConditionColor = (condition: AssetCondition) => {
    switch (condition) {
      case 'Excellent': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'Good': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'Fair': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'Poor': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
      case 'Critical': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Registry</h1>
            <p className="text-muted-foreground">
              Comprehensive asset database with advanced management capabilities
            </p>
          </div>
          <div className="flex items-center gap-2">
            <ContextTooltip content={{
              title: 'Import Assets',
              description: 'Import assets from CSV or Excel files with data validation',
              type: 'info'
            }}>
              <Button variant="outline" className="gap-2">
                <Upload className="h-4 w-4" />
                Import
              </Button>
            </ContextTooltip>

            <ContextTooltip content={{
              title: 'Export Assets',
              description: 'Export filtered assets to various formats',
              type: 'info'
            }}>
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                Export
              </Button>
            </ContextTooltip>

            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Add Asset
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ContextTooltip content={{
            title: 'Total Assets',
            description: 'Total number of assets in the registry across all categories and locations',
            type: 'info'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalAssets.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Across {ASSET_CATEGORIES.length} categories
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Total Asset Value',
            description: 'Current market value of all assets based on depreciation calculations',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
                <p className="text-xs text-muted-foreground">
                  Current market value
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Active Assets',
            description: 'Number of assets currently in active use and operational',
            type: 'success'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Assets</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.activeAssets}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((stats.activeAssets / stats.totalAssets) * 100)}% of total
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Assets in Maintenance',
            description: 'Number of assets currently undergoing maintenance or repair',
            type: 'warning'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">In Maintenance</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{stats.maintenanceAssets}</div>
                <p className="text-xs text-muted-foreground">
                  Requires attention
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>
        </div>

        {/* Search and Filters */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search & Filter Assets
            </CardTitle>
            <CardDescription>
              Use advanced search and filtering to find specific assets
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by asset ID, name, manufacturer, model, serial number, or assigned user..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Quick Filters */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={filters.statuses?.includes('Active') ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setFilters(prev => ({
                    ...prev,
                    statuses: prev.statuses?.includes('Active')
                      ? prev.statuses.filter(s => s !== 'Active')
                      : [...(prev.statuses || []), 'Active']
                  }));
                }}
              >
                Active Assets
              </Button>
              <Button
                variant={filters.statuses?.includes('Maintenance') ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setFilters(prev => ({
                    ...prev,
                    statuses: prev.statuses?.includes('Maintenance')
                      ? prev.statuses.filter(s => s !== 'Maintenance')
                      : [...(prev.statuses || []), 'Maintenance']
                  }));
                }}
              >
                In Maintenance
              </Button>
              <Button
                variant={filters.categories?.includes('IT Equipment') ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setFilters(prev => ({
                    ...prev,
                    categories: prev.categories?.includes('IT Equipment')
                      ? prev.categories.filter(c => c !== 'IT Equipment')
                      : [...(prev.categories || []), 'IT Equipment']
                  }));
                }}
              >
                IT Equipment
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({})}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Asset List */}
        <Card className="glass-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Assets ({filteredAssets.length})</CardTitle>
                <CardDescription>
                  {selectedAssets.length > 0 && `${selectedAssets.length} selected • `}
                  Showing {paginatedAssets.length} of {filteredAssets.length} assets
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'cards' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('cards')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {viewMode === 'table' ? (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="grid grid-cols-12 gap-4 p-3 bg-muted/50 rounded-lg text-sm font-medium">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      checked={selectedAssets.length === paginatedAssets.length && paginatedAssets.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </div>
                  <div className="col-span-2 flex items-center gap-1 cursor-pointer" onClick={() => handleSort('assetId')}>
                    Asset ID
                    {sortConfig.field === 'assetId' && (
                      sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                    )}
                  </div>
                  <div className="col-span-2 flex items-center gap-1 cursor-pointer" onClick={() => handleSort('name')}>
                    Name
                    {sortConfig.field === 'name' && (
                      sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                    )}
                  </div>
                  <div className="col-span-1">Category</div>
                  <div className="col-span-1">Status</div>
                  <div className="col-span-1">Condition</div>
                  <div className="col-span-2">Assigned To</div>
                  <div className="col-span-1 flex items-center gap-1 cursor-pointer" onClick={() => handleSort('currentValue')}>
                    Value
                    {sortConfig.field === 'currentValue' && (
                      sortConfig.direction === 'asc' ? <SortAsc className="h-3 w-3" /> : <SortDesc className="h-3 w-3" />
                    )}
                  </div>
                  <div className="col-span-1">Actions</div>
                </div>

                {/* Table Rows */}
                <div className="space-y-2">
                  {paginatedAssets.map((asset) => (
                    <div key={asset.id} className="grid grid-cols-12 gap-4 p-3 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={selectedAssets.includes(asset.id)}
                          onChange={() => handleSelectAsset(asset.id)}
                          className="rounded"
                        />
                      </div>
                      <div className="col-span-2 font-mono text-sm">{asset.assetId}</div>
                      <div className="col-span-2">
                        <div className="font-medium">{asset.name}</div>
                        <div className="text-xs text-muted-foreground">{asset.manufacturer} {asset.model}</div>
                      </div>
                      <div className="col-span-1">
                        <Badge variant="outline" className="text-xs">
                          {asset.category}
                        </Badge>
                      </div>
                      <div className="col-span-1">
                        <Badge className={`text-xs ${getStatusColor(asset.status)}`}>
                          {asset.status}
                        </Badge>
                      </div>
                      <div className="col-span-1">
                        <Badge className={`text-xs ${getConditionColor(asset.condition)}`}>
                          {asset.condition}
                        </Badge>
                      </div>
                      <div className="col-span-2">
                        {asset.assignedUser ? (
                          <div>
                            <div className="font-medium text-sm">{asset.assignedUser.name}</div>
                            <div className="text-xs text-muted-foreground">{asset.assignedUser.department}</div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">Unassigned</span>
                        )}
                      </div>
                      <div className="col-span-1 font-medium">{formatCurrency(asset.currentValue)}</div>
                      <div className="col-span-1">
                        <div className="flex items-center gap-1">
                          <ContextTooltip content={{
                            title: 'View Asset Details',
                            description: 'View comprehensive asset information and history',
                            type: 'info'
                          }}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </ContextTooltip>
                          <ContextTooltip content={{
                            title: 'Edit Asset',
                            description: 'Modify asset information and properties',
                            type: 'info'
                          }}>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </ContextTooltip>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between pt-4">
                    <div className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // Cards View
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {paginatedAssets.map((asset) => (
                  <Card key={asset.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <CardTitle className="text-sm font-medium">{asset.name}</CardTitle>
                          <p className="text-xs text-muted-foreground font-mono">{asset.assetId}</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={selectedAssets.includes(asset.id)}
                          onChange={() => handleSelectAsset(asset.id)}
                          className="rounded"
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge className={`text-xs ${getStatusColor(asset.status)}`}>
                          {asset.status}
                        </Badge>
                        <Badge className={`text-xs ${getConditionColor(asset.condition)}`}>
                          {asset.condition}
                        </Badge>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Category:</span>
                          <span className="ml-1">{asset.category}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Value:</span>
                          <span className="ml-1 font-medium">{formatCurrency(asset.currentValue)}</span>
                        </div>
                        {asset.assignedUser && (
                          <div>
                            <span className="text-muted-foreground">Assigned:</span>
                            <span className="ml-1">{asset.assignedUser.name}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-1 pt-2">
                        <Button variant="ghost" size="sm" className="flex-1">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="ghost" size="sm" className="flex-1">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default AssetRegistry;
