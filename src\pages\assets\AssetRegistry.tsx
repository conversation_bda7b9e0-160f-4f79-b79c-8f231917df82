
import { AppLayout } from "@/components/layout/AppLayout";
import { StatCard } from "@/components/dashboard/StatCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Package, Plus, Search, Filter, Building, Wrench, DollarSign } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const AssetRegistry = () => {
  const assets = [
    { id: "AST-001", name: "Pump Station Alpha", category: "Pumping Equipment", location: "Site A", status: "Active", value: 250000 },
    { id: "AST-002", name: "Generator Unit 1", category: "Power Generation", location: "Site B", status: "Maintenance", value: 180000 },
    { id: "AST-003", name: "Control Panel CP-01", category: "Control Systems", location: "Site A", status: "Active", value: 45000 },
    { id: "AST-004", name: "Valve Assembly V-12", category: "Valves", location: "Site C", status: "Decommissioned", value: 12000 },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Registry</h1>
            <p className="text-muted-foreground">Manage and track all organizational assets</p>
          </div>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            Add Asset
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard 
            title="Total Assets"
            value="1,247"
            icon={<Package className="text-primary h-4 w-4" />}
            trend={{ value: 8, isPositive: true }}
          />
          <StatCard 
            title="Active Assets"
            value="1,156"
            icon={<Building className="text-green-600 h-4 w-4" />}
            description="92.7% of total assets"
          />
          <StatCard 
            title="Under Maintenance"
            value="67"
            icon={<Wrench className="text-orange-600 h-4 w-4" />}
            description="5.4% of total assets"
          />
          <StatCard 
            title="Total Asset Value"
            value="$12.4M"
            icon={<DollarSign className="text-primary h-4 w-4" />}
            trend={{ value: 15, isPositive: true }}
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Asset Search & Filter</CardTitle>
            <CardDescription>Find and filter assets by various criteria</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search assets..." className="pl-10" />
                </div>
              </div>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Asset List</CardTitle>
            <CardDescription>All registered assets in the system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {assets.map((asset) => (
                <div key={asset.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Package className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{asset.name}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{asset.id}</span>
                        <span>•</span>
                        <span>{asset.category}</span>
                        <span>•</span>
                        <span>{asset.location}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="font-semibold">${asset.value.toLocaleString()}</div>
                      <Badge variant={asset.status === 'Active' ? 'default' : asset.status === 'Maintenance' ? 'secondary' : 'destructive'}>
                        {asset.status}
                      </Badge>
                    </div>
                    <Button variant="ghost" size="sm">
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default AssetRegistry;
