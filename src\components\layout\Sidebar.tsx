import { NavLink } from "react-router-dom";
import {
  ChevronDown,
  ChevronRight,
  Search,
  Bell,
  CheckCircle,
  Clock,
  Star,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState, useEffect, useRef } from "react";
import { useDynamicColors } from "@/hooks/use-theme-colors";
import { useAdvancedResponsive } from "@/hooks/use-advanced-responsive";
import { BottomNavigation } from "./BottomNavigation";
import { menuItems, MenuItem } from "@/data/navigationData";
import { motion, AnimatePresence } from "framer-motion";

interface SidebarProps {
  open: boolean;
  toggleSidebar: () => void;
}

export function Sidebar({ open, toggleSidebar }: SidebarProps) {
  const dynamicColors = useDynamicColors();
  const responsive = useAdvancedResponsive();
  const sidebarRef = useRef<HTMLElement>(null);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    "Asset Management": true,
    "Analytics & Reports": true
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [recentItems, setRecentItems] = useState<string[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);
  const [isLoading, setIsLoading] = useState(false);
  const [predictiveSuggestions, setPredictiveSuggestions] = useState<string[]>([]);

  const toggleExpand = (title: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      setIsSearchFocused(true);
    }
  };

  // Smart search functionality with fuzzy matching
  const fuzzySearch = (query: string, text: string): boolean => {
    if (!query) return true;
    const queryLower = query.toLowerCase();
    const textLower = text.toLowerCase();

    // Exact match
    if (textLower.includes(queryLower)) return true;

    // Fuzzy match - check if all characters exist in order
    let queryIndex = 0;
    for (let i = 0; i < textLower.length && queryIndex < queryLower.length; i++) {
      if (textLower[i] === queryLower[queryIndex]) {
        queryIndex++;
      }
    }
    return queryIndex === queryLower.length;
  };

  // Filter menu items based on search
  const filteredMenuItems = menuItems.filter(item => {
    if (fuzzySearch(searchQuery, item.title)) return true;
    if (item.children) {
      return item.children.some(child => fuzzySearch(searchQuery, child.title));
    }
    return false;
  });

  // Track recent items
  const trackRecentItem = (path: string) => {
    setRecentItems(prev => {
      const filtered = prev.filter(item => item !== path);
      return [path, ...filtered].slice(0, 5);
    });
  };

  // Simulate loading state for menu items
  const simulateLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 800);
  };

  // Adaptive behavior based on usage patterns
  useEffect(() => {
    const handleResize = () => {
      if (responsive.isMobile && open) {
        toggleSidebar();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [responsive.isMobile, open, toggleSidebar]);

  // Update notification count periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setNotificationCount(Math.floor(Math.random() * 5));
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  // Predictive navigation suggestions based on usage patterns
  useEffect(() => {
    if (recentItems.length > 0) {
      setPredictiveSuggestions(recentItems.slice(0, 3));
    }
  }, [recentItems]);

  useEffect(() => {
    simulateLoading();
  }, [searchQuery]);

  // Return bottom navigation for mobile devices
  if (responsive.isMobile) {
    return <BottomNavigation onMenuToggle={toggleSidebar} />;
  }

  return (
    <motion.aside
      ref={sidebarRef}
      className={cn(
        "sidebar-2025-enhanced transition-all duration-700 ease-out z-30 relative h-full",
        "before:absolute before:inset-0 before:bg-gradient-to-b before:from-transparent before:via-primary/8 before:to-transparent before:opacity-0 before:transition-opacity before:duration-500",
        "hover:before:opacity-100",
        responsive.isTablet && "w-20",
        !responsive.prefersReducedMotion && "transform-gpu"
      )}
      style={{
        willChange: 'transform, width, box-shadow',
        transform: 'translate3d(0, 0, 0)',
        perspective: '1000px',
        transformStyle: 'preserve-3d'
      }}
      animate={{
        width: open ? (responsive.isTablet ? '5rem' : '18rem') : (responsive.isTablet ? '5rem' : '4rem')
      }}
      transition={{
        type: "spring",
        stiffness: 280,
        damping: 25,
        mass: 0.6
      }}
    // Removed whileHover motion effects for static design
    >
      {/* Professional & Dynamic Header with Modern Design */}
      <motion.div
        className="h-16 flex items-center justify-center px-4 border-b glass-theme relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg,
            hsl(var(--background) / 0.98),
            ${dynamicColors.getColorHsl('primary', 0.05)},
            ${dynamicColors.getColorHsl('subtle', 0.15)}
          )`,
          backdropFilter: 'blur(32px) saturate(180%)',
          borderBottom: `1px solid ${dynamicColors.getColorHsl('primary', 0.15)}`,
          boxShadow: `0 6px 16px -10px ${dynamicColors.getColorHsl('dark', 0.2)}`
        }}
      >
        {/* Enhanced animated background particles with professional effects */}
        <div className="absolute inset-0 opacity-40 overflow-hidden">
          <motion.div
            className="absolute top-2 left-8 w-2 h-2 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('light'),
              boxShadow: `0 0 12px ${dynamicColors.getColorHsl('light', 0.7)}`
            }}
            animate={{
              opacity: [0.5, 1, 0.5],
              scale: [1, 1.5, 1],
              x: [0, 8, 0]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute top-8 right-12 w-1 h-1 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('medium'),
              boxShadow: `0 0 6px ${dynamicColors.getColorHsl('medium', 0.6)}`
            }}
            animate={{
              opacity: [0.3, 0.9, 0.3],
              scale: [1, 1.6, 1],
              y: [0, -3, 0]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.8
            }}
          />
          <motion.div
            className="absolute bottom-4 left-16 w-1.5 h-1.5 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('dark'),
              boxShadow: `0 0 8px ${dynamicColors.getColorHsl('dark', 0.6)}`
            }}
            animate={{
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.6, 1],
              x: [0, 5, 0]
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1.5
            }}
          />
          <motion.div
            className="absolute top-3 right-24 w-1 h-1 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('primary'),
              boxShadow: `0 0 12px ${dynamicColors.getColorHsl('primary', 0.8)}`
            }}
            animate={{
              opacity: [0.4, 1, 0.4],
              scale: [1, 2, 1],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
          <motion.div
            className="absolute top-10 right-10 w-1.5 h-1.5 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('secondary'),
              boxShadow: `0 0 10px ${dynamicColors.getColorHsl('secondary', 0.7)}`
            }}
            animate={{
              opacity: [0.3, 0.9, 0.3],
              scale: [1, 1.4, 1],
              y: [0, -6, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
        </div>

        {open && (
          <motion.div
            className="flex items-center gap-3"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <motion.div
              className="w-11 h-11 rounded-xl flex items-center justify-center relative overflow-hidden"
              style={{
                background: String(dynamicColors.gradientPrimary),
                boxShadow: `0 8px 20px ${dynamicColors.getColorHsl('medium', 0.5)}, 0 2px 8px ${dynamicColors.getColorHsl('primary', 0.4)}`
              }}
              whileHover={{ scale: 1.08, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
              initial={{ rotate: -5 }}
              animate={{ rotate: 0 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <motion.div 
                className="absolute inset-0 opacity-40"
                style={{
                  background: `radial-gradient(circle at 70% 30%, ${dynamicColors.getColorHsl('light', 0.9)}, transparent 60%)`
                }}
                animate={{
                  opacity: [0.4, 0.6, 0.4]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <Zap className="h-6 w-6 text-white drop-shadow-lg" />
            </motion.div>
            <div>
              <motion.h2 
                className="font-bold text-base tracking-tight" 
                style={{
                  ...dynamicColors.textPrimary,
                  textShadow: `0 1px 3px ${dynamicColors.getColorHsl('dark', 0.15)}`
                }}
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1, type: "spring", stiffness: 300 }}
                whileHover={{ scale: 1.03 }}
              >
                Toshka Hub
              </motion.h2>
              <motion.p 
                className="text-xs font-medium" 
                style={{
                  background: dynamicColors.gradientText,
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textFillColor: "transparent"
                }}
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
              >
                Enterprise Suite
              </motion.p>
            </div>
          </motion.div>
        )}

        {/* Professional & Dynamic Header with Modern Design */}
      </motion.div>

      {/* Enhanced Smart Search Bar with Modern Design */}
      <AnimatePresence>
        {open && (
          <motion.div
            className="px-4 py-3 border-b glass-theme relative overflow-hidden"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            style={{
              borderBottom: `1px solid ${dynamicColors.getColorHsl('primary', 0.1)}`,
              background: `linear-gradient(to bottom, ${dynamicColors.getColorHsl('background', 0.5)}, ${dynamicColors.getColorHsl('background', 0.3)})`,
              backdropFilter: 'blur(16px)'
            }}
          >
            <motion.div
              className="relative search-container"
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setTimeout(() => setIsSearchFocused(false), 200)}
              whileFocus={{ scale: 1.02 }}
              initial={{ y: -5, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ type: "spring", stiffness: 400, damping: 25, delay: 0.1 }}
            >
              <motion.div
                className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                animate={{
                  scale: isSearchFocused ? 1.1 : 1,
                  color: isSearchFocused ? dynamicColors.getColorHsl('primary') : dynamicColors.getColorHsl('medium', 0.6)
                }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Search size={14} />
              </motion.div>
              <input
                type="text"
                placeholder="Search (Ctrl+K)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleSearchKeyDown}
                className={cn(
                  "w-full pl-9 pr-8 py-2.5 text-sm rounded-xl border transition-all duration-300",
                  "bg-background/60 backdrop-blur-md",
                  "focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary/40",
                  isSearchFocused ? "shadow-lg" : "shadow-md"
                )}
                style={{
                  background: `linear-gradient(135deg,
                    hsl(var(--background) / 0.9),
                    ${dynamicColors.getColorHsl('subtle', 0.05)}
                  )`,
                  backdropFilter: 'blur(16px)',
                  border: `1px solid ${isSearchFocused ? dynamicColors.getColorHsl('primary', 0.3) : dynamicColors.getColorHsl('medium', 0.15)}`,
                  boxShadow: isSearchFocused ? `0 4px 12px ${dynamicColors.getColorHsl('primary', 0.15)}` : `0 2px 6px ${dynamicColors.getColorHsl('dark', 0.05)}`
                }}
              />
              <AnimatePresence>
                {searchQuery && (
                  <motion.div 
                    className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
                    initial={{ opacity: 0, scale: 0.8, rotate: -90 }}
                    animate={{ opacity: 1, scale: 1, rotate: 0 }}
                    exit={{ opacity: 0, scale: 0.8, rotate: 90 }}
                    onClick={() => setSearchQuery('')}
                    whileHover={{ scale: 1.2, rotate: 180 }}
                    whileTap={{ scale: 0.9 }}
                    style={{
                      color: dynamicColors.getColorHsl('medium', 0.6)
                    }}
                >
                  <X className="h-4 w-4" />
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Ultra-Professional Menu Items with Enhanced Visual Effects */}
      <motion.div
        className="flex-1 overflow-y-auto px-4 py-4 space-y-4 sidebar-scrollbar h-[calc(100vh-8rem)]"
        style={{
          overflowY: 'auto',
          display: 'block',
          scrollbarWidth: 'thin',
          scrollbarColor: `${dynamicColors.getColorHsl('primary', 0.3)} transparent`,
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: dynamicColors.getColorHsl('primary', 0.2),
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: dynamicColors.getColorHsl('primary', 0.4),
          }
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <AnimatePresence>
          {filteredMenuItems.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 15, scale: 0.96 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -15, scale: 0.95 }}
              transition={{
                delay: index * 0.04,
                type: "spring",
                stiffness: 400,
                damping: 28
              }}
              whileHover={{ scale: 1.02, x: 3, transition: { duration: 0.2 } }}
              className="transform origin-left"
            >
              {item.children ? (
                <div className="space-y-1">
                  <motion.button
                    onClick={() => toggleExpand(item.title)}
                    className={cn(
                      "menu-item-2025-ultra w-full flex justify-between items-center group p-3 rounded-xl",
                      expandedItems[item.title] && "active"
                    )}
                    whileHover={{
                      backgroundColor: dynamicColors.getColorHsl('background', 0.5),
                      boxShadow: `0 4px 12px -2px ${dynamicColors.getColorHsl('dark', 0.08)}`,
                      borderColor: dynamicColors.getColorHsl('primary', 0.2),
                      scale: 1.02,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.98 }}
                    style={{
                      border: `1px solid ${expandedItems[item.title] ? dynamicColors.getColorHsl('primary', 0.2) : 'transparent'}`,
                      backgroundColor: expandedItems[item.title] ? dynamicColors.getColorHsl('background', 0.5) : 'transparent',
                      boxShadow: expandedItems[item.title] ? `0 4px 12px -2px ${dynamicColors.getColorHsl('dark', 0.08)}` : 'none',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    <span className="flex items-center gap-3">
                      <motion.span 
                        className="relative"
                        whileHover={{ rotate: [0, -8, 8, 0] }}
                        transition={{ duration: 0.5 }}
                      >
                        <motion.div
                          className="p-2 rounded-lg flex items-center justify-center"
                          style={{
                            background: expandedItems[item.title] 
                              ? `linear-gradient(135deg, ${dynamicColors.getColorHsl('primary', 0.2)}, ${dynamicColors.getColorHsl('subtle', 0.15)})` 
                              : `linear-gradient(135deg, ${dynamicColors.getColorHsl('background', 0.8)}, ${dynamicColors.getColorHsl('subtle', 0.05)})`,
                            boxShadow: expandedItems[item.title] 
                              ? `0 4px 12px ${dynamicColors.getColorHsl('primary', 0.2)}` 
                              : 'none',
                            border: `1px solid ${expandedItems[item.title] ? dynamicColors.getColorHsl('primary', 0.25) : 'transparent'}`
                          }}
                          whileHover={{ 
                            scale: 1.1, 
                            boxShadow: `0 6px 16px ${dynamicColors.getColorHsl('primary', 0.25)}`,
                            background: `linear-gradient(135deg, ${dynamicColors.getColorHsl('primary', 0.2)}, ${dynamicColors.getColorHsl('subtle', 0.15)})`
                          }}
                          whileTap={{ scale: 0.9, rotate: -10 }}
                          animate={{
                            rotate: expandedItems[item.title] ? [0, 5, 0] : 0,
                            scale: expandedItems[item.title] ? [1, 1.05, 1] : 1
                          }}
                          transition={{
                            duration: 0.5,
                            repeat: expandedItems[item.title] ? 0 : 0,
                            repeatType: "reverse"
                          }}
                        >
                          <motion.div
                            animate={{
                              rotate: expandedItems[item.title] ? 360 : 0,
                              scale: expandedItems[item.title] ? [1, 1.1, 1] : 1
                            }}
                            transition={{
                              duration: expandedItems[item.title] ? 0.5 : 0.3,
                              ease: "easeInOut"
                            }}
                          >
                            <item.icon
                            size={18}
                            className="transition-all duration-300"
                            style={{
                              ...expandedItems[item.title] ? dynamicColors.textPrimary : {},
                              filter: expandedItems[item.title] ? 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))' : 'none'
                            }}
                          />
                        </motion.div>
                        {item.available && (
                          <motion.div 
                            className="absolute -top-1 -right-1 bg-background rounded-full p-0.5"
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ type: "spring", stiffness: 500, delay: 0.1 }}
                            whileHover={{ scale: 1.2, transition: { duration: 0.2 } }}
                            style={{
                              boxShadow: `0 2px 6px ${dynamicColors.getColorHsl('primary', 0.3)}`,
                              border: `1px solid ${dynamicColors.getColorHsl('green', 0.3)}`
                            }}
                          >
                            <motion.div
                              animate={{ rotate: [0, 10, 0] }}
                              transition={{ duration: 0.5, delay: 0.2 }}
                            >
                              <CheckCircle className="h-3 w-3 text-green-500" />
                            </motion.div>
                          </motion.div>
                        )}
                        {item.comingSoon && !item.available && (
                          <motion.div 
                            className="absolute -top-1 -right-1 bg-background rounded-full p-0.5"
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ type: "spring", stiffness: 500, delay: 0.1 }}
                            whileHover={{ scale: 1.2, transition: { duration: 0.2 } }}
                            style={{
                              boxShadow: `0 2px 6px ${dynamicColors.getColorHsl('primary', 0.3)}`,
                              border: `1px solid ${dynamicColors.getColorHsl('orange', 0.3)}`
                            }}
                          >
                            <motion.div
                              animate={{ rotate: [0, 360] }}
                              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                            >
                              <Clock className="h-3 w-3 text-orange-500" />
                          </motion.div>
                        )}
                      </motion.span>
                      {open && (
                        <motion.span
                          className={cn(
                            "font-semibold flex items-center gap-2 font-variable",
                            item.comingSoon && "text-foreground/70"
                          )}
                          style={{
                            fontVariationSettings: "'wght' 600, 'wdth' 100"
                          }}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 }}
                        >
                          {item.title}
                          {item.comingSoon && (
                            <motion.span 
                              className="text-xs px-2.5 py-0.5 rounded-full font-medium flex items-center gap-1"
                              style={{
                                background: `linear-gradient(135deg, ${dynamicColors.getColorHsl('orange', 0.2)}, ${dynamicColors.getColorHsl('orange', 0.05)})`,
                                color: dynamicColors.getColorHsl('orange', 0.9),
                                border: `1px solid ${dynamicColors.getColorHsl('orange', 0.3)}`,
                                boxShadow: `0 2px 6px ${dynamicColors.getColorHsl('orange', 0.15)}`
                              }}
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ delay: 0.2, type: "spring", stiffness: 400 }}
                              whileHover={{ scale: 1.05, boxShadow: `0 3px 8px ${dynamicColors.getColorHsl('orange', 0.25)}` }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <motion.div
                                animate={{ rotate: [0, 10, 0, -10, 0] }}
                                transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 2 }}
                              >
                              <Clock className="h-2.5 w-2.5" />
                              Soon
                            </motion.span>
                          )}
                          {item.available && (
                            <motion.span
                              className="text-xs px-2.5 py-0.5 rounded-full font-medium flex items-center gap-1"
                              style={{
                                background: `linear-gradient(135deg, ${dynamicColors.getColorHsl('primary', 0.15)}, ${dynamicColors.getColorHsl('primary', 0.05)})`,
                                color: dynamicColors.getColorHsl('primary', 0.8),
                                border: `1px solid ${dynamicColors.getColorHsl('primary', 0.2)}`,
                                boxShadow: `0 2px 4px ${dynamicColors.getColorHsl('primary', 0.1)}`
                              }}
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ delay: 0.2 }}
                              whileHover={{ scale: 1.05 }}
                            >
                              <CheckCircle className="h-2.5 w-2.5" />
                              Available
                            </motion.span>
                          )}
                        </motion.span>
                      )}
                    </span>
                    {open && (
                      <motion.div
                        animate={{ rotate: expandedItems[item.title] ? 90 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ChevronRight
                          size={16}
                          className="transition-all duration-300 drop-shadow-sm"
                          style={expandedItems[item.title] ? dynamicColors.textPrimary : {}}
                        />
                      </motion.div>
                    )}
                  </button>

                  <AnimatePresence>
                    {expandedItems[item.title] && open && (
                      <motion.div
                        className="submenu-2025-enhanced ml-6 mt-2 space-y-1 pl-4"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{
                          duration: 0.4,
                          ease: [0.25, 0.46, 0.45, 0.94],
                          staggerChildren: 0.05
                        }}
                      >
                        {isLoading ? (
                          [...Array(4)].map((_, idx) => (
                            <motion.div
                              key={idx}
                              className="flex items-center gap-3 py-2"
                              initial={{ opacity: 0.3 }}
                              animate={{ opacity: 0.6 }}
                              transition={{
                                repeat: Infinity,
                                duration: 1.5,
                                ease: "easeInOut"
                              }}
                            >
                              <div className="w-4 h-4 bg-foreground/20 rounded-full"></div>
                              <div className="flex-1 h-3 bg-foreground/20 rounded-md"></div>
                            </motion.div>
                          ))
                        ) : (
                          item.children.map((child, childIdx) => (
                            <motion.div
                              key={childIdx}
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: childIdx * 0.05 }}
                            >
                              <NavLink
                                to={child.path || "#"}
                                className={({ isActive }) => cn(
                                  "flex items-center gap-3 p-2.5 rounded-lg transition-all duration-300 hover:scale-105",
                                  "glass-theme hover:shadow-md",
                                  isActive && "active shadow-lg"
                                )}
                                style={({ isActive }) => isActive ? {
                                  ...dynamicColors.bgPrimary(0.12),
                                  ...dynamicColors.borderPrimary(0.25)
                                } : {}}
                                onClick={() => trackRecentItem(child.path || "#")}
                              >
                                <span className="relative">
                                  <child.icon
                                    size={16}
                                    className="transition-all duration-300 drop-shadow-sm"
                                    style={dynamicColors.textPrimary}
                                  />
                                  {child.available && (
                                    <CheckCircle className="absolute -top-1 -right-1 h-2 w-2 text-green-500 bg-background rounded-full" />
                                  )}
                                  {child.comingSoon && (
                                    <Clock className="absolute -top-1 -right-1 h-2 w-2 text-orange-500 bg-background rounded-full" />
                                  )}
                                </span>
                                <span className={cn(
                                  "flex-1 text-sm font-medium font-variable",
                                  child.comingSoon && "text-foreground/50"
                                )}
                                  style={{
                                    fontVariationSettings: "'wght' 500, 'wdth' 100"
                                  }}>
                                  {child.title}
                                </span>
                                {child.comingSoon && (
                                  <span className="text-xs text-orange-500">Soon</span>
                                )}
                                {child.available && !child.comingSoon && (
                                  <span className="text-xs text-green-500">✓</span>
                                )}
                              </NavLink>
                            </motion.div>
                          ))
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <div>
                  <NavLink
                    to={item.path || "#"}
                    className={({ isActive }) => cn(
                      "menu-item-2025-ultra flex items-center gap-3 p-3",
                      isActive && "active"
                    )}
                    onClick={() => trackRecentItem(item.path || "#")}
                  >
                    <span className="relative">
                      <item.icon
                        size={18}
                        className="transition-all duration-300 drop-shadow-sm"
                        style={dynamicColors.textPrimary}
                      />
                      {item.available && (
                        <CheckCircle className="absolute -top-1 -right-1 h-3 w-3 text-green-500 bg-background rounded-full shadow-sm" />
                      )}
                      {item.comingSoon && !item.available && (
                        <Clock className="absolute -top-1 -right-1 h-3 w-3 text-orange-500 bg-background rounded-full shadow-sm" />
                      )}
                    </span>
                    {open && (
                      <motion.span
                        className={cn(
                          "font-semibold flex items-center gap-2 font-variable",
                          item.comingSoon && "text-foreground/70"
                        )}
                        style={{
                          fontVariationSettings: "'wght' 600, 'wdth' 100"
                        }}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        {item.title}
                        {item.comingSoon && (
                          <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 px-2 py-0.5 rounded-full">
                            Soon
                          </span>
                        )}
                        {item.available && (
                          <span
                            className="text-xs px-2 py-0.5 rounded-full shadow-sm border font-medium"
                            style={{
                              ...dynamicColors.bgPrimary(0.15),
                              ...dynamicColors.textPrimary,
                              ...dynamicColors.borderPrimary(0.3)
                            }}
                          >
                            Live
                          </span>
                        )}
                      </motion.span>
                    )}
                  </NavLink>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>
    </motion.aside>
  );
}
