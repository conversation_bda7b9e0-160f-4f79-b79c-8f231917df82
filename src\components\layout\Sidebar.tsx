
import { NavLink } from "react-router-dom";
import {
  ChevronDown,
  ChevronRight,
  LayoutDashboard,
  Settings,
  Users,
  Calendar,
  PieChart,
  Monitor,
  Shield,
  Truck,
  Home,
  Package,
  Wrench,
  Building,
  ClipboardList,
  BarChart3,
  FileText,
  UserCheck,
  DollarSign,
  Boxes,
  Zap,
  Layers,
  Clock,
  CheckCircle,
  Star
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { useDynamicColors } from "@/hooks/use-theme-colors";

interface SidebarProps {
  open: boolean;
  toggleSidebar: () => void;
}

interface MenuItem {
  title: string;
  icon: React.ElementType;
  path?: string;
  children?: MenuItem[];
  badge?: string;
  color?: string;
  available?: boolean; // New property to indicate if the feature is available
  comingSoon?: boolean; // New property to indicate if it's coming soon
}

const menuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: Home,
    path: "/",
    color: "text-blue-500",
    available: true
  },
  {
    title: "Asset Management",
    icon: Package,
    color: "text-green-500",
    available: true,
    children: [
      { title: "Asset Registry", icon: Building, path: "/assets/registry", available: true },
      { title: "Asset Tracking", icon: Monitor, path: "/assets/tracking", available: true },
      { title: "Performance", icon: PieChart, path: "/assets/performance", available: true },
      { title: "Depreciation", icon: BarChart3, path: "/assets/depreciation", available: true }
    ]
  },
  {
    title: "Maintenance (CMMS)",
    icon: Wrench,
    color: "text-orange-500",
    badge: "Soon",
    comingSoon: true,
    children: [
      { title: "Work Orders", icon: ClipboardList, path: "/maintenance/work-orders", comingSoon: true },
      { title: "Preventive", icon: Calendar, path: "/maintenance/preventive", comingSoon: true },
      { title: "Predictive", icon: Monitor, path: "/maintenance/predictive", comingSoon: true },
      { title: "Spare Parts", icon: Boxes, path: "/maintenance/spare-parts", comingSoon: true }
    ]
  },
  {
    title: "Financial (ERP)",
    icon: DollarSign,
    color: "text-purple-500",
    available: true,
    children: [
      { title: "Dashboard", icon: PieChart, path: "/financial", available: true },
      { title: "General Ledger", icon: FileText, path: "/financial/gl", comingSoon: true },
      { title: "Accounts Payable", icon: DollarSign, path: "/financial/ap", comingSoon: true },
      { title: "Accounts Receivable", icon: BarChart3, path: "/financial/ar", comingSoon: true },
      { title: "Budgeting", icon: PieChart, path: "/financial/budgeting", comingSoon: true }
    ]
  },
  {
    title: "Human Resources",
    icon: Users,
    color: "text-pink-500",
    comingSoon: true,
    children: [
      { title: "Employees", icon: Users, path: "/hr/employees", comingSoon: true },
      { title: "Payroll", icon: DollarSign, path: "/hr/payroll", comingSoon: true },
      { title: "Time & Attendance", icon: Calendar, path: "/hr/attendance", comingSoon: true },
      { title: "Performance", icon: UserCheck, path: "/hr/performance", comingSoon: true },
      { title: "Training", icon: FileText, path: "/hr/training", comingSoon: true }
    ]
  },
  {
    title: "Inventory",
    icon: Boxes,
    color: "text-cyan-500",
    comingSoon: true,
    children: [
      { title: "Stock Management", icon: Boxes, path: "/inventory/stock", comingSoon: true },
      { title: "Procurement", icon: Truck, path: "/inventory/procurement", comingSoon: true },
      { title: "Warehouses", icon: Building, path: "/inventory/warehouses", comingSoon: true },
      { title: "Suppliers", icon: Users, path: "/inventory/suppliers", comingSoon: true }
    ]
  },
  {
    title: "Projects",
    icon: ClipboardList,
    color: "text-indigo-500",
    comingSoon: true,
    children: [
      { title: "Project List", icon: ClipboardList, path: "/projects/list", comingSoon: true },
      { title: "Planning", icon: Calendar, path: "/projects/planning", comingSoon: true },
      { title: "Resources", icon: Users, path: "/projects/resources", comingSoon: true },
      { title: "Timeline", icon: BarChart3, path: "/projects/timeline", comingSoon: true }
    ]
  },
  {
    title: "Fleet Management",
    icon: Truck,
    color: "text-red-500",
    comingSoon: true,
    children: [
      { title: "Vehicles", icon: Truck, path: "/fleet/vehicles", comingSoon: true },
      { title: "Maintenance", icon: Wrench, path: "/fleet/maintenance", comingSoon: true },
      { title: "Fuel Management", icon: DollarSign, path: "/fleet/fuel", comingSoon: true },
      { title: "Driver Management", icon: Users, path: "/fleet/drivers", comingSoon: true }
    ]
  },
  {
    title: "HSE & Safety",
    icon: Shield,
    color: "text-yellow-500",
    comingSoon: true,
    children: [
      { title: "Incidents", icon: FileText, path: "/hse/incidents", comingSoon: true },
      { title: "Safety Audits", icon: ClipboardList, path: "/hse/audits", comingSoon: true },
      { title: "Compliance", icon: Shield, path: "/hse/compliance", comingSoon: true },
      { title: "Training", icon: Users, path: "/hse/training", comingSoon: true }
    ]
  },
  {
    title: "Engineering",
    icon: Settings,
    color: "text-teal-500",
    comingSoon: true,
    children: [
      { title: "Mechanical", icon: Settings, path: "/engineering/mechanical", comingSoon: true },
      { title: "Electrical", icon: Zap, path: "/engineering/electrical", comingSoon: true },
      { title: "Civil", icon: Building, path: "/engineering/civil", comingSoon: true },
      { title: "Instrumentation", icon: Monitor, path: "/engineering/instrumentation", comingSoon: true }
    ]
  },
  {
    title: "Analytics & Reports",
    icon: BarChart3,
    color: "text-violet-500",
    comingSoon: true,
    children: [
      { title: "Dashboards", icon: LayoutDashboard, path: "/analytics/dashboards", comingSoon: true },
      { title: "Financial Reports", icon: DollarSign, path: "/analytics/financial", comingSoon: true },
      { title: "Asset Reports", icon: Package, path: "/analytics/assets", comingSoon: true },
      { title: "Custom Reports", icon: FileText, path: "/analytics/custom", comingSoon: true }
    ]
  }
];

export function Sidebar({ open, toggleSidebar }: SidebarProps) {
  const dynamicColors = useDynamicColors();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    "Asset Management": true,
    "Analytics & Reports": true
  });

  const toggleExpand = (title: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const renderMenuItem = (item: MenuItem, index: number) => {
    const isExpanded = expandedItems[item.title];

    if (item.children) {
      return (
        <div key={index} className="sidebar-section">
          <button
            onClick={() => toggleExpand(item.title)}
            className={cn(
              "sidebar-link w-full flex justify-between items-center group glass-theme rounded-lg transition-all duration-300 hover:shadow-md",
              isExpanded && "shadow-sm"
            )}
            style={isExpanded ? {
              ...dynamicColors.bgPrimary(0.1),
              ...dynamicColors.borderPrimary(0.2)
            } : {}}
          >
            <span className="flex items-center gap-3">
              <span className="relative">
                <item.icon
                  size={18}
                  className="transition-all duration-300 drop-shadow-sm"
                  style={isExpanded ? dynamicColors.textPrimary : {}}
                />
                {item.badge && (
                  <span className={cn(
                    "absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold",
                    item.comingSoon ? "bg-orange-500" : "bg-red-500"
                  )}>
                    {item.badge}
                  </span>
                )}
                {item.available && (
                  <CheckCircle className="absolute -top-1 -right-1 h-3 w-3 text-green-500 bg-sidebar-background rounded-full shadow-sm" />
                )}
                {item.comingSoon && !item.badge && (
                  <Clock className="absolute -top-1 -right-1 h-3 w-3 text-orange-500 bg-sidebar-background rounded-full shadow-sm" />
                )}
              </span>
              {open && (
                <span className={cn(
                  "font-medium flex items-center gap-2",
                  item.comingSoon && "text-sidebar-foreground/70"
                )}>
                  {item.title}
                  {item.comingSoon && (
                    <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 px-2 py-0.5 rounded-full">
                      Soon
                    </span>
                  )}
                  {item.available && (
                    <span
                      className="text-xs px-2 py-0.5 rounded-full shadow-sm border"
                      style={{
                        ...dynamicColors.bgPrimary(0.15),
                        ...dynamicColors.textPrimary,
                        ...dynamicColors.borderPrimary(0.3)
                      }}
                    >
                      Live
                    </span>
                  )}
                </span>
              )}
            </span>
            {open && (
              <ChevronRight
                size={16}
                className={cn(
                  "transition-all duration-300 drop-shadow-sm",
                  isExpanded && "transform rotate-90"
                )}
                style={isExpanded ? dynamicColors.textPrimary : {}}
              />
            )}
          </button>

          {isExpanded && open && (
            <div
              className="ml-7 mt-2 space-y-1 border-l pl-4"
              style={dynamicColors.borderPrimary(0.2)}
            >
              {item.children.map((child, childIdx) => (
                <NavLink
                  key={childIdx}
                  to={child.path || "#"}
                  className={({ isActive }) => cn(
                    "sidebar-link text-sm py-2 relative glass-theme rounded-md transition-all duration-300 hover:shadow-sm",
                    isActive && "active shadow-sm"
                  )}
                  style={({ isActive }) => isActive ? {
                    ...dynamicColors.bgPrimary(0.12),
                    ...dynamicColors.borderPrimary(0.25)
                  } : {}}
                >
                  <span className="flex items-center gap-3">
                    <span className="relative">
                      <child.icon
                        size={16}
                        className="transition-all duration-300 drop-shadow-sm"
                        style={dynamicColors.textPrimary}
                      />
                      {child.available && (
                        <CheckCircle className="absolute -top-1 -right-1 h-2 w-2 text-green-500 bg-sidebar-background rounded-full" />
                      )}
                      {child.comingSoon && (
                        <Clock className="absolute -top-1 -right-1 h-2 w-2 text-orange-500 bg-sidebar-background rounded-full" />
                      )}
                    </span>
                    <span className={cn(
                      "flex-1",
                      child.comingSoon && "text-sidebar-foreground/50"
                    )}>
                      {child.title}
                    </span>
                    {child.badge && (
                      <span className={cn(
                        "ml-auto text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold",
                        child.comingSoon ? "bg-orange-500" : "bg-red-500"
                      )}>
                        {child.badge}
                      </span>
                    )}
                    {child.comingSoon && !child.badge && (
                      <span className="ml-auto text-xs text-orange-500">Soon</span>
                    )}
                    {child.available && !child.badge && (
                      <span className="ml-auto text-xs text-green-500">✓</span>
                    )}
                  </span>
                </NavLink>
              ))}
            </div>
          )}
        </div>
      );
    }

    return (
      <NavLink
        key={index}
        to={item.path || "#"}
        className={({ isActive }) => cn(
          "sidebar-link glass-theme rounded-lg transition-all duration-300 hover:shadow-md",
          isActive && "active shadow-lg"
        )}
        style={({ isActive }) => isActive ? {
          ...dynamicColors.bgPrimary(0.15),
          ...dynamicColors.borderPrimary(0.3)
        } : {}}
      >
        <span className="relative">
          <item.icon
            size={18}
            className="transition-all duration-300 drop-shadow-sm"
            style={dynamicColors.textPrimary}
          />
          {item.badge && (
            <span className={cn(
              "absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold",
              item.comingSoon ? "bg-orange-500" : "bg-red-500"
            )}>
              {item.badge}
            </span>
          )}
          {item.available && (
            <CheckCircle className="absolute -top-1 -right-1 h-3 w-3 text-green-500 bg-sidebar-background rounded-full shadow-sm" />
          )}
          {item.comingSoon && !item.badge && (
            <Clock className="absolute -top-1 -right-1 h-3 w-3 text-orange-500 bg-sidebar-background rounded-full shadow-sm" />
          )}
        </span>
        {open && (
          <span className={cn(
            "font-medium flex items-center gap-2",
            item.comingSoon && "text-sidebar-foreground/70"
          )}>
            {item.title}
            {item.comingSoon && (
              <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 px-2 py-0.5 rounded-full">
                Soon
              </span>
            )}
            {item.available && (
              <span
                className="text-xs px-2 py-0.5 rounded-full shadow-sm border"
                style={{
                  ...dynamicColors.bgPrimary(0.15),
                  ...dynamicColors.textPrimary,
                  ...dynamicColors.borderPrimary(0.3)
                }}
              >
                Live
              </span>
            )}
          </span>
        )}
      </NavLink>
    );
  };

  return (
    <aside
      className={cn(
        "sidebar-glass-enhanced transition-all duration-300 ease-in-out z-30 relative",
        open ? "w-72" : "w-16"
      )}
    >
      {/* Header */}
      <div className="h-16 flex items-center justify-between px-4 border-b border-sidebar-border/50 glass-theme">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center shadow-lg"
              style={dynamicColors.gradientPrimary}
            >
              <Layers className="w-4 h-4 text-white drop-shadow-sm" />
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full pulse-dot shadow-sm"></div>
          </div>
          {open && (
            <div className="flex flex-col">
              <span
                className="font-bold text-lg gradient-text"
                style={dynamicColors.textPrimary}
              >
                TEMS
              </span>
              <span className="text-xs text-sidebar-foreground/60 -mt-1">
                Enterprise Management
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Menu Items */}
      <div className="p-3 space-y-1 overflow-y-auto h-[calc(100vh-4rem)] scrollbar-thin">
        {/* Quick Stats */}
        {open && (
          <div
            className="mb-6 p-4 rounded-lg border glass-theme shadow-sm"
            style={{
              ...dynamicColors.bgPrimary(0.08),
              ...dynamicColors.borderPrimary(0.15)
            }}
          >
            <div className="text-xs text-sidebar-foreground/60 mb-2">System Status</div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse shadow-sm"></div>
              <span className="text-sm text-sidebar-foreground">All Systems Operational</span>
            </div>
          </div>
        )}

        {menuItems.map(renderMenuItem)}

        {/* Bottom Section */}
        {open && (
          <div className="pt-4 mt-auto">
            <div
              className="p-3 rounded-lg glass-theme shadow-sm border"
              style={{
                ...dynamicColors.bgPrimary(0.05),
                ...dynamicColors.borderPrimary(0.1)
              }}
            >
              <div className="text-xs text-sidebar-foreground/60 mb-1">Version 2.1.0</div>
              <div className="text-xs text-sidebar-foreground/40">Last updated today</div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
}
