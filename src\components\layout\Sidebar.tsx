import { NavLink } from "react-router-dom";
import {
  ChevronDown,
  ChevronRight,
  Search,
  Bell,
  CheckCircle,
  Clock,
  Star,
  Zap
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState, useEffect, useRef } from "react";
import { useDynamicColors } from "@/hooks/use-theme-colors";
import { useAdvancedResponsive } from "@/hooks/use-advanced-responsive";
import { BottomNavigation } from "./BottomNavigation";
import { menuItems, MenuItem } from "@/data/navigationData";
// import { motion, AnimatePresence } from "framer-motion";

// Fallback motion components for when framer-motion is not available
const motion = {
  aside: 'aside' as any,
  div: 'div' as any,
  button: 'button' as any,
  span: 'span' as any
};

const AnimatePresence = ({ children }: { children: React.ReactNode }) => <>{children}</>;

interface SidebarProps {
  open: boolean;
  toggleSidebar: () => void;
}

export function Sidebar({ open, toggleSidebar }: SidebarProps) {
  const dynamicColors = useDynamicColors();
  const responsive = useAdvancedResponsive();
  const sidebarRef = useRef<HTMLElement>(null);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    "Asset Management": true,
    "Analytics & Reports": true
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [recentItems, setRecentItems] = useState<string[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);
  const [isLoading, setIsLoading] = useState(false);
  const [predictiveSuggestions, setPredictiveSuggestions] = useState<string[]>([]);

  const toggleExpand = (title: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      setIsSearchFocused(true);
    }
  };

  // Smart search functionality with fuzzy matching
  const fuzzySearch = (query: string, text: string): boolean => {
    if (!query) return true;
    const queryLower = query.toLowerCase();
    const textLower = text.toLowerCase();

    // Exact match
    if (textLower.includes(queryLower)) return true;

    // Fuzzy match - check if all characters exist in order
    let queryIndex = 0;
    for (let i = 0; i < textLower.length && queryIndex < queryLower.length; i++) {
      if (textLower[i] === queryLower[queryIndex]) {
        queryIndex++;
      }
    }
    return queryIndex === queryLower.length;
  };

  // Filter menu items based on search
  const filteredMenuItems = menuItems.filter(item => {
    if (fuzzySearch(searchQuery, item.title)) return true;
    if (item.children) {
      return item.children.some(child => fuzzySearch(searchQuery, child.title));
    }
    return false;
  });

  // Track recent items
  const trackRecentItem = (path: string) => {
    setRecentItems(prev => {
      const filtered = prev.filter(item => item !== path);
      return [path, ...filtered].slice(0, 5);
    });
  };

  // Simulate loading state for menu items
  const simulateLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 800);
  };

  // Adaptive behavior based on usage patterns
  useEffect(() => {
    const handleResize = () => {
      if (responsive.isMobile && open) {
        toggleSidebar();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [responsive.isMobile, open, toggleSidebar]);

  // Update notification count periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setNotificationCount(Math.floor(Math.random() * 5));
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  // Predictive navigation suggestions based on usage patterns
  useEffect(() => {
    if (recentItems.length > 0) {
      setPredictiveSuggestions(recentItems.slice(0, 3));
    }
  }, [recentItems]);

  useEffect(() => {
    simulateLoading();
  }, [searchQuery]);

  // Return bottom navigation for mobile devices
  if (responsive.isMobile) {
    return <BottomNavigation onMenuToggle={toggleSidebar} />;
  }

  return (
    <motion.aside
      ref={sidebarRef}
      className={cn(
        "sidebar-2025-enhanced transition-all duration-700 ease-out z-30 relative",
        "before:absolute before:inset-0 before:bg-gradient-to-b before:from-transparent before:via-primary/8 before:to-transparent before:opacity-0 before:transition-opacity before:duration-500",
        "hover:before:opacity-100",
        responsive.isTablet && "w-20",
        !responsive.prefersReducedMotion && "transform-gpu"
      )}
      style={{
        willChange: 'transform, width, box-shadow',
        transform: 'translate3d(0, 0, 0)',
        perspective: '1000px',
        transformStyle: 'preserve-3d'
      }}
      animate={{
        width: open ? (responsive.isTablet ? '5rem' : '18rem') : (responsive.isTablet ? '5rem' : '4rem')
      }}
      transition={{
        type: "spring",
        stiffness: 280,
        damping: 25,
        mass: 0.6
      }}
    // Removed whileHover motion effects for static design
    >
      {/* Enhanced Header with Smart Features */}
      <motion.div
        className="h-16 flex items-center justify-between px-4 border-b glass-theme relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg,
            hsl(var(--background) / 0.95),
            ${dynamicColors.getColorHsl('subtle', 0.05)}
          )`,
          backdropFilter: 'blur(24px) saturate(180%)',
          borderBottom: `1px solid ${dynamicColors.getColorHsl('medium', 0.15)}`
        }}
      >
        {/* Animated background particles with enhanced effects */}
        <div className="absolute inset-0 opacity-20 overflow-hidden">
          <motion.div
            className="absolute top-2 left-8 w-1 h-1 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('light'),
              boxShadow: `0 0 5px ${dynamicColors.getColorHsl('light', 0.5)}`
            }}
            animate={{
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute top-8 right-12 w-0.5 h-0.5 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('medium'),
              boxShadow: `0 0 3px ${dynamicColors.getColorHsl('medium', 0.4)}`
            }}
            animate={{
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
          <motion.div
            className="absolute bottom-4 left-16 w-0.5 h-0.5 rounded-full"
            style={{
              backgroundColor: dynamicColors.getColorHsl('dark'),
              boxShadow: `0 0 2px ${dynamicColors.getColorHsl('dark', 0.3)}`
            }}
            animate={{
              opacity: [0.1, 0.6, 0.1],
              scale: [1, 1.3, 1]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
        </div>

        {open && (
          <motion.div
            className="flex items-center gap-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center"
              style={{
                background: dynamicColors.gradientPrimary,
                boxShadow: `0 4px 12px ${dynamicColors.getColorHsl('medium', 0.3)}`
              }}
            >
              <Zap className="h-4 w-4 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-sm" style={dynamicColors.textPrimary}>
                Toshka Hub
              </h2>
              <p className="text-xs text-muted-foreground">Enterprise Suite</p>
            </div>
          </motion.div>
        )}

        <motion.div
          className="flex items-center gap-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {open && (
            <div className="relative">
              <Bell className="h-4 w-4 text-muted-foreground" />
              {notificationCount > 0 && (
                <motion.span
                  className="absolute -top-1 -right-1 h-3 w-3 rounded-full text-[8px] font-bold text-white flex items-center justify-center"
                  style={{
                    background: dynamicColors.gradientPrimary,
                    boxShadow: `0 0 8px ${dynamicColors.getColorHsl('medium', 0.4)}`
                  }}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                >
                  {notificationCount}
                </motion.span>
              )}
            </div>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="h-8 w-8 p-0 hover:bg-primary/10"
          >
            <motion.div
              animate={{ rotate: open ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronRight className="h-4 w-4" />
            </motion.div>
          </Button>
        </motion.div>
      </motion.div>

      {/* Smart Search Bar with Contextual Results */}
      <AnimatePresence>
        {open && (
          <motion.div
            className="px-4 py-3 border-b glass-theme relative overflow-hidden"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            style={{
              borderBottom: `1px solid ${dynamicColors.getColorHsl('medium', 0.1)}`
            }}
          >
            <motion.div
              className="relative search-container"
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setTimeout(() => setIsSearchFocused(false), 200)}
              whileFocus={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <Search
                size={14}
                className="absolute left-3 top-1/2 -translate-y-1/2 opacity-60"
              />
              <input
                type="text"
                placeholder="Search (Ctrl+K)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleSearchKeyDown}
                className={cn(
                  "w-full pl-9 pr-3 py-2 text-sm rounded-lg border transition-all duration-300",
                  "bg-background/50 backdrop-blur-sm",
                  "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                  isSearchFocused && "shadow-lg"
                )}
                style={{
                  background: `linear-gradient(135deg,
                    hsl(var(--background) / 0.8),
                    ${dynamicColors.getColorHsl('subtle', 0.02)}
                  )`,
                  backdropFilter: 'blur(12px)',
                  border: `1px solid ${dynamicColors.getColorHsl('medium', 0.15)}`
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Ultra-Professional Menu Items */}
      <motion.div
        className="flex-1 overflow-y-auto px-4 py-4 space-y-2 sidebar-scrollbar"
      >
        <AnimatePresence>
          {filteredMenuItems.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                delay: index * 0.05,
                type: "spring",
                stiffness: 300,
                damping: 30
              }}
            >
              {item.children ? (
                <div className="space-y-1">
                  <button
                    onClick={() => toggleExpand(item.title)}
                    className={cn(
                      "menu-item-2025-ultra w-full flex justify-between items-center group p-3",
                      expandedItems[item.title] && "active"
                    )}
                  >
                    <span className="flex items-center gap-3">
                      <span className="relative">
                        <item.icon
                          size={18}
                          className="transition-all duration-300 drop-shadow-sm"
                          style={expandedItems[item.title] ? dynamicColors.textPrimary : {}}
                        />
                        {item.available && (
                          <CheckCircle className="absolute -top-1 -right-1 h-3 w-3 text-green-500 bg-background rounded-full shadow-sm" />
                        )}
                        {item.comingSoon && !item.available && (
                          <Clock className="absolute -top-1 -right-1 h-3 w-3 text-orange-500 bg-background rounded-full shadow-sm" />
                        )}
                      </span>
                      {open && (
                        <motion.span
                          className={cn(
                            "font-semibold flex items-center gap-2 font-variable",
                            item.comingSoon && "text-foreground/70"
                          )}
                          style={{
                            fontVariationSettings: "'wght' 600, 'wdth' 100"
                          }}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 }}
                        >
                          {item.title}
                          {item.comingSoon && (
                            <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 px-2 py-0.5 rounded-full">
                              Soon
                            </span>
                          )}
                          {item.available && (
                            <span
                              className="text-xs px-2 py-0.5 rounded-full shadow-sm border font-medium"
                              style={{
                                ...dynamicColors.bgPrimary(0.15),
                                ...dynamicColors.textPrimary,
                                ...dynamicColors.borderPrimary(0.3)
                              }}
                            >
                              Live
                            </span>
                          )}
                        </motion.span>
                      )}
                    </span>
                    {open && (
                      <motion.div
                        animate={{ rotate: expandedItems[item.title] ? 90 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ChevronRight
                          size={16}
                          className="transition-all duration-300 drop-shadow-sm"
                          style={expandedItems[item.title] ? dynamicColors.textPrimary : {}}
                        />
                      </motion.div>
                    )}
                  </button>

                  <AnimatePresence>
                    {expandedItems[item.title] && open && (
                      <motion.div
                        className="submenu-2025-enhanced ml-6 mt-2 space-y-1 pl-4"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{
                          duration: 0.4,
                          ease: [0.25, 0.46, 0.45, 0.94],
                          staggerChildren: 0.05
                        }}
                      >
                        {isLoading ? (
                          [...Array(4)].map((_, idx) => (
                            <motion.div
                              key={idx}
                              className="flex items-center gap-3 py-2"
                              initial={{ opacity: 0.3 }}
                              animate={{ opacity: 0.6 }}
                              transition={{
                                repeat: Infinity,
                                duration: 1.5,
                                ease: "easeInOut"
                              }}
                            >
                              <div className="w-4 h-4 bg-foreground/20 rounded-full"></div>
                              <div className="flex-1 h-3 bg-foreground/20 rounded-md"></div>
                            </motion.div>
                          ))
                        ) : (
                          item.children.map((child, childIdx) => (
                            <motion.div
                              key={childIdx}
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: childIdx * 0.05 }}
                            >
                              <NavLink
                                to={child.path || "#"}
                                className={({ isActive }) => cn(
                                  "flex items-center gap-3 p-2.5 rounded-lg transition-all duration-300 hover:scale-105",
                                  "glass-theme hover:shadow-md",
                                  isActive && "active shadow-lg"
                                )}
                                style={({ isActive }) => isActive ? {
                                  ...dynamicColors.bgPrimary(0.12),
                                  ...dynamicColors.borderPrimary(0.25)
                                } : {}}
                                onClick={() => trackRecentItem(child.path || "#")}
                              >
                                <span className="relative">
                                  <child.icon
                                    size={16}
                                    className="transition-all duration-300 drop-shadow-sm"
                                    style={dynamicColors.textPrimary}
                                  />
                                  {child.available && (
                                    <CheckCircle className="absolute -top-1 -right-1 h-2 w-2 text-green-500 bg-background rounded-full" />
                                  )}
                                  {child.comingSoon && (
                                    <Clock className="absolute -top-1 -right-1 h-2 w-2 text-orange-500 bg-background rounded-full" />
                                  )}
                                </span>
                                <span className={cn(
                                  "flex-1 text-sm font-medium font-variable",
                                  child.comingSoon && "text-foreground/50"
                                )}
                                  style={{
                                    fontVariationSettings: "'wght' 500, 'wdth' 100"
                                  }}>
                                  {child.title}
                                </span>
                                {child.comingSoon && (
                                  <span className="text-xs text-orange-500">Soon</span>
                                )}
                                {child.available && !child.comingSoon && (
                                  <span className="text-xs text-green-500">✓</span>
                                )}
                              </NavLink>
                            </motion.div>
                          ))
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <div>
                  <NavLink
                    to={item.path || "#"}
                    className={({ isActive }) => cn(
                      "menu-item-2025-ultra flex items-center gap-3 p-3",
                      isActive && "active"
                    )}
                    onClick={() => trackRecentItem(item.path || "#")}
                  >
                    <span className="relative">
                      <item.icon
                        size={18}
                        className="transition-all duration-300 drop-shadow-sm"
                        style={dynamicColors.textPrimary}
                      />
                      {item.available && (
                        <CheckCircle className="absolute -top-1 -right-1 h-3 w-3 text-green-500 bg-background rounded-full shadow-sm" />
                      )}
                      {item.comingSoon && !item.available && (
                        <Clock className="absolute -top-1 -right-1 h-3 w-3 text-orange-500 bg-background rounded-full shadow-sm" />
                      )}
                    </span>
                    {open && (
                      <motion.span
                        className={cn(
                          "font-semibold flex items-center gap-2 font-variable",
                          item.comingSoon && "text-foreground/70"
                        )}
                        style={{
                          fontVariationSettings: "'wght' 600, 'wdth' 100"
                        }}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        {item.title}
                        {item.comingSoon && (
                          <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 px-2 py-0.5 rounded-full">
                            Soon
                          </span>
                        )}
                        {item.available && (
                          <span
                            className="text-xs px-2 py-0.5 rounded-full shadow-sm border font-medium"
                            style={{
                              ...dynamicColors.bgPrimary(0.15),
                              ...dynamicColors.textPrimary,
                              ...dynamicColors.borderPrimary(0.3)
                            }}
                          >
                            Live
                          </span>
                        )}
                      </motion.span>
                    )}
                  </NavLink>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>
    </motion.aside >
  );
}
