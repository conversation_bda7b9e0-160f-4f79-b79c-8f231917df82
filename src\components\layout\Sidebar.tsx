
import { NavLink } from "react-router-dom";
import { 
  ChevronDown, 
  ChevronRight,
  LayoutDashboard, 
  Settings, 
  Users, 
  Calendar,
  PieChart,
  Monitor,
  Shield,
  Truck,
  Home,
  Package,
  Wrench,
  Building,
  ClipboardList,
  BarChart3,
  FileText,
  UserCheck,
  DollarSign,
  Boxes,
  Zap,
  Layers
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface SidebarProps {
  open: boolean;
  toggleSidebar: () => void;
}

interface MenuItem {
  title: string;
  icon: React.ElementType;
  path?: string;
  children?: MenuItem[];
  badge?: string;
  color?: string;
}

const menuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: Home,
    path: "/",
    color: "text-blue-500"
  },
  {
    title: "Asset Management",
    icon: Package,
    color: "text-green-500",
    children: [
      { title: "Asset Registry", icon: Building, path: "/assets/registry" },
      { title: "Asset Tracking", icon: Monitor, path: "/assets/tracking" },
      { title: "Depreciation", icon: BarChart3, path: "/assets/depreciation" },
      { title: "Performance", icon: PieChart, path: "/assets/performance" }
    ]
  },
  {
    title: "Maintenance (CMMS)",
    icon: Wrench,
    color: "text-orange-500",
    badge: "12",
    children: [
      { title: "Work Orders", icon: ClipboardList, path: "/maintenance/work-orders", badge: "5" },
      { title: "Preventive", icon: Calendar, path: "/maintenance/preventive" },
      { title: "Predictive", icon: Monitor, path: "/maintenance/predictive" },
      { title: "Spare Parts", icon: Boxes, path: "/maintenance/spare-parts" }
    ]
  },
  {
    title: "Financial (ERP)",
    icon: DollarSign,
    color: "text-purple-500",
    children: [
      { title: "Dashboard", icon: PieChart, path: "/financial" },
      { title: "General Ledger", icon: FileText, path: "/financial/gl" },
      { title: "Accounts Payable", icon: DollarSign, path: "/financial/ap" },
      { title: "Accounts Receivable", icon: BarChart3, path: "/financial/ar" },
      { title: "Budgeting", icon: PieChart, path: "/financial/budgeting" }
    ]
  },
  {
    title: "Human Resources",
    icon: Users,
    color: "text-pink-500",
    children: [
      { title: "Employees", icon: Users, path: "/hr/employees" },
      { title: "Payroll", icon: DollarSign, path: "/hr/payroll" },
      { title: "Time & Attendance", icon: Calendar, path: "/hr/attendance" },
      { title: "Performance", icon: UserCheck, path: "/hr/performance" },
      { title: "Training", icon: FileText, path: "/hr/training" }
    ]
  },
  {
    title: "Inventory",
    icon: Boxes,
    color: "text-cyan-500",
    children: [
      { title: "Stock Management", icon: Boxes, path: "/inventory/stock" },
      { title: "Procurement", icon: Truck, path: "/inventory/procurement" },
      { title: "Warehouses", icon: Building, path: "/inventory/warehouses" },
      { title: "Suppliers", icon: Users, path: "/inventory/suppliers" }
    ]
  },
  {
    title: "Projects",
    icon: ClipboardList,
    color: "text-indigo-500",
    children: [
      { title: "Project List", icon: ClipboardList, path: "/projects/list" },
      { title: "Planning", icon: Calendar, path: "/projects/planning" },
      { title: "Resources", icon: Users, path: "/projects/resources" },
      { title: "Timeline", icon: BarChart3, path: "/projects/timeline" }
    ]
  },
  {
    title: "Fleet Management",
    icon: Truck,
    color: "text-red-500",
    children: [
      { title: "Vehicles", icon: Truck, path: "/fleet/vehicles" },
      { title: "Maintenance", icon: Wrench, path: "/fleet/maintenance" },
      { title: "Fuel Management", icon: DollarSign, path: "/fleet/fuel" },
      { title: "Driver Management", icon: Users, path: "/fleet/drivers" }
    ]
  },
  {
    title: "HSE & Safety",
    icon: Shield,
    color: "text-yellow-500",
    children: [
      { title: "Incidents", icon: FileText, path: "/hse/incidents" },
      { title: "Safety Audits", icon: ClipboardList, path: "/hse/audits" },
      { title: "Compliance", icon: Shield, path: "/hse/compliance" },
      { title: "Training", icon: Users, path: "/hse/training" }
    ]
  },
  {
    title: "Engineering",
    icon: Settings,
    color: "text-teal-500",
    children: [
      { title: "Mechanical", icon: Settings, path: "/engineering/mechanical" },
      { title: "Electrical", icon: Zap, path: "/engineering/electrical" },
      { title: "Civil", icon: Building, path: "/engineering/civil" },
      { title: "Instrumentation", icon: Monitor, path: "/engineering/instrumentation" }
    ]
  },
  {
    title: "Analytics & Reports",
    icon: BarChart3,
    color: "text-violet-500",
    children: [
      { title: "Dashboards", icon: LayoutDashboard, path: "/analytics/dashboards" },
      { title: "Financial Reports", icon: DollarSign, path: "/analytics/financial" },
      { title: "Asset Reports", icon: Package, path: "/analytics/assets" },
      { title: "Custom Reports", icon: FileText, path: "/analytics/custom" }
    ]
  }
];

export function Sidebar({ open, toggleSidebar }: SidebarProps) {
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    "Asset Management": true,
    "Analytics & Reports": true
  });

  const toggleExpand = (title: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const renderMenuItem = (item: MenuItem, index: number) => {
    const isExpanded = expandedItems[item.title];
    
    if (item.children) {
      return (
        <div key={index} className="sidebar-section">
          <button 
            onClick={() => toggleExpand(item.title)}
            className={cn(
              "sidebar-link w-full flex justify-between items-center group",
              isExpanded && "text-sidebar-primary"
            )}
          >
            <span className="flex items-center gap-3">
              <span className="relative">
                <item.icon size={18} className={cn("transition-colors", item.color)} />
                {item.badge && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold">
                    {item.badge}
                  </span>
                )}
              </span>
              {open && (
                <span className="font-medium">{item.title}</span>
              )}
            </span>
            {open && (
              <ChevronRight 
                size={16} 
                className={cn(
                  "transition-transform duration-300", 
                  isExpanded && "transform rotate-90"
                )} 
              />
            )}
          </button>
          
          {isExpanded && open && (
            <div className="ml-7 mt-2 space-y-1 border-l border-sidebar-border/30 pl-4">
              {item.children.map((child, childIdx) => (
                <NavLink 
                  key={childIdx}
                  to={child.path || "#"} 
                  className={({ isActive }) => cn(
                    "sidebar-link text-sm py-2 relative",
                    isActive && "active"
                  )}
                >
                  <span className="flex items-center gap-3">
                    <child.icon size={16} className="text-sidebar-foreground/70" />
                    <span>{child.title}</span>
                    {child.badge && (
                      <span className="ml-auto bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold">
                        {child.badge}
                      </span>
                    )}
                  </span>
                </NavLink>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    return (
      <NavLink 
        key={index}
        to={item.path || "#"}
        className={({ isActive }) => cn(
          "sidebar-link",
          isActive && "active"
        )}
      >
        <span className="relative">
          <item.icon size={18} className={cn("transition-colors", item.color)} />
          {item.badge && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center text-[10px] font-bold">
              {item.badge}
            </span>
          )}
        </span>
        {open && <span className="font-medium">{item.title}</span>}
      </NavLink>
    );
  };
  
  return (
    <aside 
      className={cn(
        "bg-sidebar border-r border-sidebar-border transition-all duration-300 ease-in-out z-30 relative",
        open ? "w-72" : "w-16"
      )}
    >
      {/* Header */}
      <div className="h-16 flex items-center justify-between px-4 border-b border-sidebar-border/50 bg-gradient-to-r from-sidebar-background to-sidebar-background/95">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Layers className="w-4 h-4 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full pulse-dot"></div>
          </div>
          {open && (
            <div className="flex flex-col">
              <span className="font-bold text-lg text-sidebar-foreground gradient-text">
                TEMS
              </span>
              <span className="text-xs text-sidebar-foreground/60 -mt-1">
                Enterprise Management
              </span>
            </div>
          )}
        </div>
      </div>
      
      {/* Menu Items */}
      <div className="p-3 space-y-1 overflow-y-auto h-[calc(100vh-4rem)] scrollbar-thin">
        {/* Quick Stats */}
        {open && (
          <div className="mb-6 p-4 bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg border border-primary/20">
            <div className="text-xs text-sidebar-foreground/60 mb-2">System Status</div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-sidebar-foreground">All Systems Operational</span>
            </div>
          </div>
        )}
        
        {menuItems.map(renderMenuItem)}
        
        {/* Bottom Section */}
        {open && (
          <div className="pt-4 mt-auto">
            <div className="p-3 bg-gradient-to-r from-sidebar-accent/30 to-transparent rounded-lg">
              <div className="text-xs text-sidebar-foreground/60 mb-1">Version 2.1.0</div>
              <div className="text-xs text-sidebar-foreground/40">Last updated today</div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
}
