import { useState, useEffect } from 'react';

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  ultrawide: number;
}

export interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isUltrawide: boolean;
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  devicePixelRatio: number;
  isTouch: boolean;
  prefersReducedMotion: boolean;
  colorScheme: 'light' | 'dark';
}

const defaultBreakpoints: ResponsiveBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1440,
  ultrawide: 1920
};

export const useAdvancedResponsive = (customBreakpoints?: Partial<ResponsiveBreakpoints>) => {
  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints };
  
  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isUltrawide: false,
        width: 1920,
        height: 1080,
        orientation: 'landscape',
        devicePixelRatio: 1,
        isTouch: false,
        prefersReducedMotion: false,
        colorScheme: 'light'
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      isMobile: width < breakpoints.mobile,
      isTablet: width >= breakpoints.mobile && width < breakpoints.tablet,
      isDesktop: width >= breakpoints.tablet && width < breakpoints.desktop,
      isUltrawide: width >= breakpoints.desktop,
      width,
      height,
      orientation: width > height ? 'landscape' : 'portrait',
      devicePixelRatio: window.devicePixelRatio || 1,
      isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      colorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    };
  });

  useEffect(() => {
    const updateState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setState({
        isMobile: width < breakpoints.mobile,
        isTablet: width >= breakpoints.mobile && width < breakpoints.tablet,
        isDesktop: width >= breakpoints.tablet && width < breakpoints.desktop,
        isUltrawide: width >= breakpoints.desktop,
        width,
        height,
        orientation: width > height ? 'landscape' : 'portrait',
        devicePixelRatio: window.devicePixelRatio || 1,
        isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        colorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      });
    };

    // Debounced resize handler
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateState, 100);
    };

    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);
    
    // Listen for media query changes
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const colorQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    motionQuery.addEventListener('change', updateState);
    colorQuery.addEventListener('change', updateState);

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      motionQuery.removeEventListener('change', updateState);
      colorQuery.removeEventListener('change', updateState);
      clearTimeout(timeoutId);
    };
  }, [breakpoints]);

  return state;
};

// Utility functions for responsive design
export const getResponsiveValue = <T,>(
  values: {
    mobile?: T;
    tablet?: T;
    desktop?: T;
    ultrawide?: T;
    default: T;
  },
  state: ResponsiveState
): T => {
  if (state.isMobile && values.mobile !== undefined) return values.mobile;
  if (state.isTablet && values.tablet !== undefined) return values.tablet;
  if (state.isDesktop && values.desktop !== undefined) return values.desktop;
  if (state.isUltrawide && values.ultrawide !== undefined) return values.ultrawide;
  return values.default;
};

export const useResponsiveValue = <T,>(values: {
  mobile?: T;
  tablet?: T;
  desktop?: T;
  ultrawide?: T;
  default: T;
}) => {
  const state = useAdvancedResponsive();
  return getResponsiveValue(values, state);
};
