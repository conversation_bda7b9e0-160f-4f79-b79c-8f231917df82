import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  Save,
  ArrowLeft,
  ArrowRight,
  Plus,
  FileText,
  DollarSign,
  MapPin,
  Wrench,
  Target,
  Upload,
  QrCode,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { Asset } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';
import { useDynamicColors } from '@/hooks/use-theme-colors';

interface AssetFormModalProps {
  asset?: Asset | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (asset: Partial<Asset>) => void;
  mode: 'create' | 'edit';
}

interface FormData {
  // Basic Information
  name: string;
  assetId: string;
  category: string;
  subCategory: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  condition: string;

  // Financial Details
  purchaseCost: number;
  purchaseDate: string;
  depreciationMethod: string;
  depreciationRate: number;
  usefulLife: number;
  salvageValue: number;
  warrantyExpiration: string;

  // Location & Assignment
  locationSite: string;
  locationBuilding: string;
  locationFloor: string;
  locationRoom: string;
  department: string;
  assignedUserName: string;
  assignedUserEmail: string;

  // Maintenance & Performance
  maintenanceFrequency: string;
  maintenanceType: string;
  maintenanceCost: number;
  utilizationRate: number;

  // Additional Details
  tags: string[];
  notes: string;
  qrCode: string;
}

const FORM_STEPS = [
  { id: 'basic', title: 'Basic Information', icon: FileText, description: 'Asset identification and classification' },
  { id: 'financial', title: 'Financial Details', icon: DollarSign, description: 'Purchase and depreciation information' },
  { id: 'location', title: 'Location & Assignment', icon: MapPin, description: 'Physical location and user assignment' },
  { id: 'maintenance', title: 'Maintenance & Performance', icon: Wrench, description: 'Maintenance schedule and targets' },
  { id: 'additional', title: 'Additional Details', icon: Target, description: 'Tags, notes, and attachments' }
];

const ASSET_CATEGORIES = [
  'IT Equipment',
  'Manufacturing Equipment',
  'Office Equipment',
  'Vehicles',
  'Furniture',
  'Tools',
  'Infrastructure'
];

const DEPRECIATION_METHODS = [
  'Straight Line',
  'Declining Balance',
  'Sum of Years Digits',
  'Double Declining Balance',
  'Units of Production'
];

const CONDITION_OPTIONS = [
  'Excellent',
  'Good',
  'Fair',
  'Poor',
  'Critical'
];

const MAINTENANCE_FREQUENCIES = [
  'Weekly',
  'Monthly',
  'Quarterly',
  'Semi-Annual',
  'Annual'
];

const MAINTENANCE_TYPES = [
  'Preventive',
  'Corrective',
  'Predictive',
  'Emergency'
];

export const AssetFormModal: React.FC<AssetFormModalProps> = ({
  asset,
  isOpen,
  onClose,
  onSave,
  mode
}) => {
  const dynamicColors = useDynamicColors();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    // Basic Information
    name: '',
    assetId: '',
    category: '',
    subCategory: '',
    manufacturer: '',
    model: '',
    serialNumber: '',
    condition: 'Good',

    // Financial Details
    purchaseCost: 0,
    purchaseDate: new Date().toISOString().split('T')[0],
    depreciationMethod: 'Straight Line',
    depreciationRate: 10,
    usefulLife: 5,
    salvageValue: 0,
    warrantyExpiration: '',

    // Location & Assignment
    locationSite: '',
    locationBuilding: '',
    locationFloor: '',
    locationRoom: '',
    department: '',
    assignedUserName: '',
    assignedUserEmail: '',

    // Maintenance & Performance
    maintenanceFrequency: 'Monthly',
    maintenanceType: 'Preventive',
    maintenanceCost: 0,
    utilizationRate: 85,

    // Additional Details
    tags: [],
    notes: '',
    qrCode: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [newTag, setNewTag] = useState('');

  // Initialize form data when modal opens or asset changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && asset) {
        // Pre-populate form with existing asset data
        setFormData({
          name: asset.name,
          assetId: asset.assetId,
          category: asset.category,
          subCategory: asset.subCategory,
          manufacturer: asset.manufacturer,
          model: asset.model,
          serialNumber: asset.serialNumber,
          condition: asset.condition,

          purchaseCost: asset.purchaseCost,
          purchaseDate: asset.purchaseDate.toISOString().split('T')[0],
          depreciationMethod: asset.depreciationMethod,
          depreciationRate: asset.depreciationRate,
          usefulLife: asset.usefulLife,
          salvageValue: asset.salvageValue,
          warrantyExpiration: asset.warrantyExpiration?.toISOString().split('T')[0] || '',

          locationSite: asset.location.site,
          locationBuilding: asset.location.building,
          locationFloor: asset.location.floor,
          locationRoom: asset.location.room,
          department: asset.department,
          assignedUserName: asset.assignedUser?.name || '',
          assignedUserEmail: asset.assignedUser?.email || '',

          maintenanceFrequency: asset.maintenanceSchedule?.frequency || 'Monthly',
          maintenanceType: asset.maintenanceSchedule?.maintenanceType || 'Preventive',
          maintenanceCost: asset.maintenanceSchedule?.maintenanceCost || 0,
          utilizationRate: asset.utilizationRate || 85,

          tags: asset.tags,
          notes: asset.notes || '',
          qrCode: asset.qrCode
        });
      } else {
        // Generate new asset ID for create mode
        const newAssetId = `AST-${Date.now().toString().slice(-6)}`;
        const newQrCode = `QR-${newAssetId}`;
        setFormData(prev => ({
          ...prev,
          assetId: newAssetId,
          qrCode: newQrCode
        }));
      }
      setCurrentStep(0);
      setErrors({});
    }
  }, [isOpen, mode, asset]);

  // Generate asset ID when category changes
  useEffect(() => {
    if (mode === 'create' && formData.category) {
      const categoryPrefix = formData.category.split(' ').map(word => word.charAt(0)).join('').toUpperCase();
      const timestamp = Date.now().toString().slice(-6);
      const newAssetId = `${categoryPrefix}-${timestamp}`;
      const newQrCode = `QR-${newAssetId}`;

      setFormData(prev => ({
        ...prev,
        assetId: newAssetId,
        qrCode: newQrCode
      }));
    }
  }, [formData.category, mode]);

  const validateCurrentStep = useCallback(() => {
    const stepErrors: Record<string, string> = {};

    switch (currentStep) {
      case 0: // Basic Information
        if (!formData.name.trim()) stepErrors.name = 'Asset name is required';
        if (!formData.category) stepErrors.category = 'Category is required';
        if (!formData.manufacturer.trim()) stepErrors.manufacturer = 'Manufacturer is required';
        if (!formData.model.trim()) stepErrors.model = 'Model is required';
        if (!formData.serialNumber.trim()) stepErrors.serialNumber = 'Serial number is required';
        break;

      case 1: // Financial Details
        if (formData.purchaseCost <= 0) stepErrors.purchaseCost = 'Purchase cost must be greater than 0';
        if (!formData.purchaseDate) stepErrors.purchaseDate = 'Purchase date is required';
        if (formData.usefulLife <= 0) stepErrors.usefulLife = 'Useful life must be greater than 0';
        if (formData.salvageValue < 0) stepErrors.salvageValue = 'Salvage value cannot be negative';
        if (formData.salvageValue >= formData.purchaseCost) stepErrors.salvageValue = 'Salvage value must be less than purchase cost';
        break;

      case 2: // Location & Assignment
        if (!formData.locationSite.trim()) stepErrors.locationSite = 'Site is required';
        if (!formData.locationBuilding.trim()) stepErrors.locationBuilding = 'Building is required';
        if (!formData.department.trim()) stepErrors.department = 'Department is required';
        break;

      case 3: // Maintenance & Performance
        if (formData.maintenanceCost < 0) stepErrors.maintenanceCost = 'Maintenance cost cannot be negative';
        if (formData.utilizationRate < 0 || formData.utilizationRate > 100) {
          stepErrors.utilizationRate = 'Utilization rate must be between 0 and 100';
        }
        break;
    }

    setErrors(stepErrors);
    return Object.keys(stepErrors).length === 0;
  }, [currentStep, formData]);

  const handleNext = useCallback(() => {
    if (validateCurrentStep() && currentStep < FORM_STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  }, [validateCurrentStep, currentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  const handleInputChange = useCallback((field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  }, [errors]);

  const handleAddTag = useCallback(() => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  }, [newTag, formData.tags]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  }, []);

  const handleSave = useCallback(() => {
    if (!validateCurrentStep()) return;

    // Convert form data to Asset format
    const assetData: Partial<Asset> = {
      name: formData.name,
      assetId: formData.assetId,
      category: formData.category,
      subCategory: formData.subCategory,
      manufacturer: formData.manufacturer,
      model: formData.model,
      serialNumber: formData.serialNumber,
      condition: formData.condition as Asset['condition'],

      purchaseCost: formData.purchaseCost,
      purchaseDate: new Date(formData.purchaseDate),
      currentValue: formData.purchaseCost, // Initial value equals purchase cost
      depreciationMethod: formData.depreciationMethod as Asset['depreciationMethod'],
      depreciationRate: formData.depreciationRate,
      usefulLife: formData.usefulLife,
      salvageValue: formData.salvageValue,
      warrantyExpiration: formData.warrantyExpiration ? new Date(formData.warrantyExpiration) : undefined,

      location: {
        site: formData.locationSite,
        building: formData.locationBuilding,
        floor: formData.locationFloor,
        room: formData.locationRoom
      },
      department: formData.department,
      assignedUser: formData.assignedUserName ? {
        name: formData.assignedUserName,
        email: formData.assignedUserEmail,
        assignedDate: new Date()
      } : undefined,

      maintenanceSchedule: {
        frequency: formData.maintenanceFrequency as Asset['maintenanceSchedule']['frequency'],
        maintenanceType: formData.maintenanceType as Asset['maintenanceSchedule']['maintenanceType'],
        maintenanceCost: formData.maintenanceCost,
        nextMaintenance: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      },
      utilizationRate: formData.utilizationRate,

      tags: formData.tags,
      notes: formData.notes,
      qrCode: formData.qrCode,

      status: 'Active' as Asset['status'],
      attachments: [],

      // Metadata
      createdAt: mode === 'create' ? new Date() : asset?.createdAt || new Date(),
      updatedAt: new Date(),
      createdBy: mode === 'create' ? 'Current User' : asset?.createdBy || 'Current User',
      lastModifiedBy: 'Current User'
    };

    onSave(assetData);
    onClose();
  }, [validateCurrentStep, formData, mode, asset, onSave, onClose]);

  const handleClose = useCallback(() => {
    setCurrentStep(0);
    setErrors({});
    onClose();
  }, [onClose]);

  if (!isOpen) return null;

  const currentStepData = FORM_STEPS[currentStep];
  const StepIcon = currentStepData.icon;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-5xl h-[95vh] max-h-[95vh] p-0 overflow-hidden bg-background/5 backdrop-blur-3xl border-2 border-white/8 shadow-2xl flex flex-col">
        {/* Enhanced Header */}
        <DialogHeader className="relative px-6 py-4 bg-gradient-to-r from-primary/5 via-primary/2 to-transparent border-b border-white/5 backdrop-blur-3xl">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/2 via-purple-500/2 to-transparent"></div>
          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-6">
              {/* Enhanced Form Icon */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl blur-sm"></div>
                <div
                  className="relative flex items-center justify-center w-16 h-16 rounded-2xl backdrop-blur-sm border"
                  style={{
                    ...dynamicColors.bgPrimary(0.2),
                    ...dynamicColors.borderPrimary(0.2)
                  }}
                >
                  <Plus className="h-8 w-8 drop-shadow-sm" style={dynamicColors.textPrimary} />
                </div>
              </div>

              {/* Enhanced Form Information */}
              <div className="space-y-2">
                <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                  {mode === 'create' ? 'Create New Asset' : 'Edit Asset'}
                </DialogTitle>
                <div className="flex items-center gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <StepIcon className="h-4 w-4" style={dynamicColors.textPrimary} />
                    <span className="font-medium text-foreground">{currentStepData.title}</span>
                  </div>
                  <div className="w-1 h-1 bg-muted-foreground/50 rounded-full"></div>
                  <span className="text-muted-foreground">{currentStepData.description}</span>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="hover:bg-red-500/20 hover:text-red-600 hover:scale-105 transition-all duration-300 rounded-full w-10 h-10 p-0"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="px-6 py-3 bg-gradient-to-r from-muted/12 via-muted/8 to-muted/12 border-b border-white/5 backdrop-blur-3xl">
          <div className="flex items-center justify-between">
            {FORM_STEPS.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 ${isActive
                    ? 'bg-primary/20 text-primary border border-primary/30'
                    : isCompleted
                      ? 'bg-green-500/20 text-green-600 border border-green-500/30'
                      : 'bg-muted/20 text-muted-foreground border border-white/10'
                    }`}>
                    <div className={`p-1 rounded-full ${isActive
                      ? 'bg-primary/30'
                      : isCompleted
                        ? 'bg-green-500/30'
                        : 'bg-muted/30'
                      }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Icon className="h-4 w-4" />
                      )}
                    </div>
                    <span className="text-xs font-medium hidden sm:block">{step.title}</span>
                  </div>
                  {index < FORM_STEPS.length - 1 && (
                    <div className={`w-8 h-0.5 mx-2 transition-all duration-300 ${isCompleted ? 'bg-green-500/50' : 'bg-muted/30'
                      }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1 flex flex-col min-h-0 bg-gradient-to-b from-background/20 to-background/40 backdrop-blur-3xl">
          <ScrollArea className="flex-1 min-h-0">
            <div className="px-6 py-4 pb-6 space-y-6" style={{ minHeight: 'calc(95vh - 320px)' }}>
              {/* Step 1: Basic Information */}
              {currentStep === 0 && (
                <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                      <div className="p-2 bg-gradient-to-br from-blue-500/8 to-blue-600/5 rounded-lg border border-white/8 backdrop-blur-3xl">
                        <FileText className="h-5 w-5 text-blue-600" />
                      </div>
                      Basic Information
                    </CardTitle>
                    <CardDescription>
                      Enter the fundamental details that identify and classify this asset
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-semibold">Asset Name *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Enter asset name"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.name ? 'border-red-500' : ''}`}
                        />
                        {errors.name && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.name}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="assetId" className="text-sm font-semibold">Asset ID</Label>
                        <Input
                          id="assetId"
                          value={formData.assetId}
                          readOnly
                          className="bg-muted/20 border-white/10 backdrop-blur-sm font-mono text-sm"
                        />
                        <p className="text-xs text-muted-foreground">Auto-generated based on category</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="category" className="text-sm font-semibold">Category *</Label>
                        <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                          <SelectTrigger className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.category ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            {ASSET_CATEGORIES.map((category) => (
                              <SelectItem key={category} value={category}>{category}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.category && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.category}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subCategory" className="text-sm font-semibold">Sub-Category</Label>
                        <Input
                          id="subCategory"
                          value={formData.subCategory}
                          onChange={(e) => handleInputChange('subCategory', e.target.value)}
                          placeholder="Enter sub-category"
                          className="bg-white/5 border-white/20 backdrop-blur-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="manufacturer" className="text-sm font-semibold">Manufacturer *</Label>
                        <Input
                          id="manufacturer"
                          value={formData.manufacturer}
                          onChange={(e) => handleInputChange('manufacturer', e.target.value)}
                          placeholder="Enter manufacturer"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.manufacturer ? 'border-red-500' : ''}`}
                        />
                        {errors.manufacturer && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.manufacturer}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="model" className="text-sm font-semibold">Model *</Label>
                        <Input
                          id="model"
                          value={formData.model}
                          onChange={(e) => handleInputChange('model', e.target.value)}
                          placeholder="Enter model"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.model ? 'border-red-500' : ''}`}
                        />
                        {errors.model && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.model}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="serialNumber" className="text-sm font-semibold">Serial Number *</Label>
                        <Input
                          id="serialNumber"
                          value={formData.serialNumber}
                          onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                          placeholder="Enter serial number"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.serialNumber ? 'border-red-500' : ''}`}
                        />
                        {errors.serialNumber && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.serialNumber}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="condition" className="text-sm font-semibold">Condition</Label>
                        <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
                          <SelectTrigger className="bg-white/5 border-white/20 backdrop-blur-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {CONDITION_OPTIONS.map((condition) => (
                              <SelectItem key={condition} value={condition}>{condition}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Step 2: Financial Details */}
              {currentStep === 1 && (
                <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                      <div className="p-2 bg-gradient-to-br from-emerald-500/8 to-emerald-600/5 rounded-lg border border-white/8 backdrop-blur-3xl">
                        <DollarSign className="h-5 w-5 text-emerald-600" />
                      </div>
                      Financial Details
                    </CardTitle>
                    <CardDescription>
                      Configure purchase information, depreciation settings, and warranty details
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="purchaseCost" className="text-sm font-semibold">Purchase Cost *</Label>
                        <Input
                          id="purchaseCost"
                          type="number"
                          value={formData.purchaseCost}
                          onChange={(e) => handleInputChange('purchaseCost', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.purchaseCost ? 'border-red-500' : ''}`}
                        />
                        {errors.purchaseCost && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.purchaseCost}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="purchaseDate" className="text-sm font-semibold">Purchase Date *</Label>
                        <Input
                          id="purchaseDate"
                          type="date"
                          value={formData.purchaseDate}
                          onChange={(e) => handleInputChange('purchaseDate', e.target.value)}
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.purchaseDate ? 'border-red-500' : ''}`}
                        />
                        {errors.purchaseDate && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.purchaseDate}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="depreciationMethod" className="text-sm font-semibold">Depreciation Method</Label>
                        <Select value={formData.depreciationMethod} onValueChange={(value) => handleInputChange('depreciationMethod', value)}>
                          <SelectTrigger className="bg-white/5 border-white/20 backdrop-blur-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {DEPRECIATION_METHODS.map((method) => (
                              <SelectItem key={method} value={method}>{method}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="depreciationRate" className="text-sm font-semibold">Depreciation Rate (%)</Label>
                        <Input
                          id="depreciationRate"
                          type="number"
                          value={formData.depreciationRate}
                          onChange={(e) => handleInputChange('depreciationRate', parseFloat(e.target.value) || 0)}
                          placeholder="10"
                          className="bg-white/5 border-white/20 backdrop-blur-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="usefulLife" className="text-sm font-semibold">Useful Life (Years) *</Label>
                        <Input
                          id="usefulLife"
                          type="number"
                          value={formData.usefulLife}
                          onChange={(e) => handleInputChange('usefulLife', parseInt(e.target.value) || 0)}
                          placeholder="5"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.usefulLife ? 'border-red-500' : ''}`}
                        />
                        {errors.usefulLife && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.usefulLife}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="salvageValue" className="text-sm font-semibold">Salvage Value</Label>
                        <Input
                          id="salvageValue"
                          type="number"
                          value={formData.salvageValue}
                          onChange={(e) => handleInputChange('salvageValue', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.salvageValue ? 'border-red-500' : ''}`}
                        />
                        {errors.salvageValue && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.salvageValue}</p>}
                      </div>

                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="warrantyExpiration" className="text-sm font-semibold">Warranty Expiration</Label>
                        <Input
                          id="warrantyExpiration"
                          type="date"
                          value={formData.warrantyExpiration}
                          onChange={(e) => handleInputChange('warrantyExpiration', e.target.value)}
                          className="bg-white/5 border-white/20 backdrop-blur-sm"
                        />
                        <p className="text-xs text-muted-foreground">Leave empty if no warranty or warranty information is unknown</p>
                      </div>
                    </div>

                    {/* Financial Preview */}
                    {formData.purchaseCost > 0 && formData.usefulLife > 0 && (
                      <div className="p-4 bg-gradient-to-br from-emerald-500/8 to-emerald-600/8 rounded-xl border border-emerald-500/12 backdrop-blur-2xl">
                        <h4 className="font-semibold text-emerald-600 mb-3 flex items-center gap-2">
                          <Info className="h-4 w-4" />
                          Financial Preview
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Annual Depreciation:</span>
                            <p className="font-semibold text-emerald-600">
                              ${((formData.purchaseCost - formData.salvageValue) / formData.usefulLife).toLocaleString()}
                            </p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Current Value:</span>
                            <p className="font-semibold text-blue-600">${formData.purchaseCost.toLocaleString()}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Total Depreciable:</span>
                            <p className="font-semibold text-orange-600">
                              ${(formData.purchaseCost - formData.salvageValue).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Step 3: Location & Assignment */}
              {currentStep === 2 && (
                <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                      <div className="p-2 bg-gradient-to-br from-green-500/8 to-green-600/5 rounded-lg border border-white/8 backdrop-blur-3xl">
                        <MapPin className="h-5 w-5 text-green-600" />
                      </div>
                      Location & Assignment
                    </CardTitle>
                    <CardDescription>
                      Specify the physical location and assign the asset to a user or department
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="locationSite" className="text-sm font-semibold">Site *</Label>
                        <Input
                          id="locationSite"
                          value={formData.locationSite}
                          onChange={(e) => handleInputChange('locationSite', e.target.value)}
                          placeholder="Enter site name"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.locationSite ? 'border-red-500' : ''}`}
                        />
                        {errors.locationSite && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.locationSite}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="locationBuilding" className="text-sm font-semibold">Building *</Label>
                        <Input
                          id="locationBuilding"
                          value={formData.locationBuilding}
                          onChange={(e) => handleInputChange('locationBuilding', e.target.value)}
                          placeholder="Enter building name"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.locationBuilding ? 'border-red-500' : ''}`}
                        />
                        {errors.locationBuilding && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.locationBuilding}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="locationFloor" className="text-sm font-semibold">Floor</Label>
                        <Input
                          id="locationFloor"
                          value={formData.locationFloor}
                          onChange={(e) => handleInputChange('locationFloor', e.target.value)}
                          placeholder="Enter floor"
                          className="bg-white/5 border-white/20 backdrop-blur-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="locationRoom" className="text-sm font-semibold">Room</Label>
                        <Input
                          id="locationRoom"
                          value={formData.locationRoom}
                          onChange={(e) => handleInputChange('locationRoom', e.target.value)}
                          placeholder="Enter room number"
                          className="bg-white/5 border-white/20 backdrop-blur-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="department" className="text-sm font-semibold">Department *</Label>
                        <Input
                          id="department"
                          value={formData.department}
                          onChange={(e) => handleInputChange('department', e.target.value)}
                          placeholder="Enter department"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.department ? 'border-red-500' : ''}`}
                        />
                        {errors.department && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.department}</p>}
                      </div>
                    </div>

                    <Separator className="bg-white/20" />

                    <div className="space-y-4">
                      <h4 className="font-semibold text-foreground">User Assignment (Optional)</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="assignedUserName" className="text-sm font-semibold">Assigned User Name</Label>
                          <Input
                            id="assignedUserName"
                            value={formData.assignedUserName}
                            onChange={(e) => handleInputChange('assignedUserName', e.target.value)}
                            placeholder="Enter user name"
                            className="bg-white/5 border-white/20 backdrop-blur-sm"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="assignedUserEmail" className="text-sm font-semibold">User Email</Label>
                          <Input
                            id="assignedUserEmail"
                            type="email"
                            value={formData.assignedUserEmail}
                            onChange={(e) => handleInputChange('assignedUserEmail', e.target.value)}
                            placeholder="Enter user email"
                            className="bg-white/5 border-white/20 backdrop-blur-sm"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Step 4: Maintenance & Performance */}
              {currentStep === 3 && (
                <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                      <div className="p-2 bg-gradient-to-br from-yellow-500/8 to-yellow-600/5 rounded-lg border border-white/8 backdrop-blur-3xl">
                        <Wrench className="h-5 w-5 text-yellow-600" />
                      </div>
                      Maintenance & Performance
                    </CardTitle>
                    <CardDescription>
                      Configure maintenance schedule and set performance targets
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="maintenanceFrequency" className="text-sm font-semibold">Maintenance Frequency</Label>
                        <Select value={formData.maintenanceFrequency} onValueChange={(value) => handleInputChange('maintenanceFrequency', value)}>
                          <SelectTrigger className="bg-white/5 border-white/20 backdrop-blur-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {MAINTENANCE_FREQUENCIES.map((frequency) => (
                              <SelectItem key={frequency} value={frequency}>{frequency}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="maintenanceType" className="text-sm font-semibold">Maintenance Type</Label>
                        <Select value={formData.maintenanceType} onValueChange={(value) => handleInputChange('maintenanceType', value)}>
                          <SelectTrigger className="bg-white/5 border-white/20 backdrop-blur-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {MAINTENANCE_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>{type}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="maintenanceCost" className="text-sm font-semibold">Estimated Maintenance Cost</Label>
                        <Input
                          id="maintenanceCost"
                          type="number"
                          value={formData.maintenanceCost}
                          onChange={(e) => handleInputChange('maintenanceCost', parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.maintenanceCost ? 'border-red-500' : ''}`}
                        />
                        {errors.maintenanceCost && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.maintenanceCost}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="utilizationRate" className="text-sm font-semibold">Target Utilization Rate (%)</Label>
                        <Input
                          id="utilizationRate"
                          type="number"
                          min="0"
                          max="100"
                          value={formData.utilizationRate}
                          onChange={(e) => handleInputChange('utilizationRate', parseInt(e.target.value) || 0)}
                          placeholder="85"
                          className={`bg-white/5 border-white/20 backdrop-blur-sm ${errors.utilizationRate ? 'border-red-500' : ''}`}
                        />
                        {errors.utilizationRate && <p className="text-xs text-red-500 flex items-center gap-1"><AlertCircle className="h-3 w-3" />{errors.utilizationRate}</p>}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Step 5: Additional Details */}
              {currentStep === 4 && (
                <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                      <div className="p-2 bg-gradient-to-br from-purple-500/8 to-purple-600/5 rounded-lg border border-white/8 backdrop-blur-3xl">
                        <Target className="h-5 w-5 text-purple-600" />
                      </div>
                      Additional Details
                    </CardTitle>
                    <CardDescription>
                      Add tags, notes, and review the generated QR code
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold">Tags</Label>
                        <div className="flex gap-2">
                          <Input
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Enter tag"
                            className="bg-white/5 border-white/20 backdrop-blur-sm"
                            onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                          />
                          <Button
                            type="button"
                            onClick={handleAddTag}
                            className="bg-primary/20 hover:bg-primary/30 border border-primary/30"
                          >
                            Add
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {formData.tags.map((tag, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="bg-primary/20 text-primary border border-primary/30 cursor-pointer hover:bg-red-500/20 hover:text-red-600 transition-colors"
                              onClick={() => handleRemoveTag(tag)}
                            >
                              {tag} ×
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes" className="text-sm font-semibold">Notes</Label>
                        <Textarea
                          id="notes"
                          value={formData.notes}
                          onChange={(e) => handleInputChange('notes', e.target.value)}
                          placeholder="Enter any additional notes or comments about this asset"
                          className="bg-white/5 border-white/20 backdrop-blur-sm min-h-[100px]"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-semibold">QR Code</Label>
                        <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/20 rounded-xl border border-white/10 backdrop-blur-sm">
                          <div className="flex items-center gap-4">
                            <div className="p-4 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30">
                              <QrCode className="h-12 w-12 text-primary" />
                            </div>
                            <div>
                              <p className="font-semibold text-foreground">Auto-generated QR Code</p>
                              <p className="text-sm text-muted-foreground font-mono">{formData.qrCode}</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                This QR code will be generated automatically for asset tracking
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </ScrollArea>

          {/* Navigation Footer */}
          <div className="flex-shrink-0 px-6 py-5 bg-gradient-to-r from-muted/12 via-muted/8 to-muted/12 border-t border-white/5 backdrop-blur-3xl">
            <div className="flex items-center justify-between min-h-[44px]">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                  className="bg-white/5 border-white/20 backdrop-blur-sm hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed min-h-[40px] px-4"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Step {currentStep + 1} of {FORM_STEPS.length}</span>
              </div>

              <div className="flex items-center gap-2">
                {currentStep < FORM_STEPS.length - 1 ? (
                  <Button
                    onClick={handleNext}
                    className="min-h-[40px] px-4 border"
                    style={{
                      ...dynamicColors.bgPrimary(0.2),
                      ...dynamicColors.borderPrimary(0.3),
                      ...dynamicColors.textPrimary
                    }}
                  >
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSave}
                    className="bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-600 hover:text-green-600 min-h-[40px] px-4"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {mode === 'create' ? 'Create Asset' : 'Save Changes'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
