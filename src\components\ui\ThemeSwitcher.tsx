import React, { useEffect, useState, useRef } from 'react';
import { useTheme } from '@/hooks/use-theme';

const ACCENT_COLORS = [
    { name: '<PERSON>', value: '262 100% 70%' },
    { name: '<PERSON><PERSON>', value: '190 100% 60%' },
    { name: 'Pink', value: '320 100% 65%' },
    { name: 'Orange', value: '30 100% 60%' },
    { name: 'Green', value: '173 58% 39%' },
];

const ACCENT_KEY = 'ui-accent';

export const ThemeSwitcher: React.FC = () => {
    const { theme, setTheme } = useTheme();
    const [accent, setAccent] = useState(() => localStorage.getItem(ACCENT_KEY) || ACCENT_COLORS[0].value);
    const [open, setOpen] = useState(false);
    const popoverRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        document.documentElement.style.setProperty('--primary', accent);
        localStorage.setItem(ACCENT_KEY, accent);
    }, [accent]);

    // Close popover when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
                setOpen(false);
            }
        }
        if (open) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [open]);

    return (
        <div className="relative">
            <button
                className="flex items-center gap-2 px-4 py-2 rounded-lg bg-card/80 shadow hover:bg-card/90 transition"
                onClick={() => setOpen((v) => !v)}
                aria-label="Open theme and color picker"
            >
                <span role="img" aria-label="palette">🎨</span> Themes
            </button>
            {open && (
                <div
                    ref={popoverRef}
                    className="absolute right-0 mt-2 w-64 z-50 bg-card/95 border border-border rounded-xl shadow-lg p-4 animate-fade-in"
                >
                    <div className="font-semibold mb-1">Theme</div>
                    <div className="flex gap-2 mb-3">
                        <button
                            className={`px-3 py-1 rounded ${theme === 'light' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
                            onClick={() => setTheme('light')}
                        >Light</button>
                        <button
                            className={`px-3 py-1 rounded ${theme === 'dark' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
                            onClick={() => setTheme('dark')}
                        >Dark</button>
                        <button
                            className={`px-3 py-1 rounded ${theme === 'system' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}
                            onClick={() => setTheme('system')}
                        >System</button>
                    </div>
                    <div className="font-semibold mb-1">Accent Color</div>
                    <div className="flex gap-2">
                        {ACCENT_COLORS.map((c) => (
                            <button
                                key={c.value}
                                className={`w-7 h-7 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${accent === c.value ? 'border-primary scale-110 ring-2 ring-primary' : 'border-border'}`}
                                style={{ background: `hsl(${c.value})` }}
                                onClick={() => setAccent(c.value)}
                                aria-label={c.name}
                            >
                                {accent === c.value && (
                                    <span className="text-white text-xs font-bold">✓</span>
                                )}
                            </button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

// Add fade-in animation
// In your CSS (index.css or global):
// .animate-fade-in { animation: fadeIn 0.2s ease; }
// @keyframes fadeIn { from { opacity: 0; transform: translateY(-8px);} to { opacity: 1; transform: translateY(0);} } 