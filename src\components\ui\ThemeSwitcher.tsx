import React, { useEffect, useState, useRef } from 'react';
import { useTheme } from '@/hooks/use-theme';
import { Palette, <PERSON>, <PERSON>, Monitor, Sparkles, Eye, Save, RotateCcw } from 'lucide-react';
import { Button } from './button';

const ACCENT_COLORS = [
    { name: '<PERSON>', value: '262 100% 70%', preview: '#8B5CF6' },
    { name: '<PERSON><PERSON>', value: '190 100% 60%', preview: '#06B6D4' },
    { name: 'Pink', value: '320 100% 65%', preview: '#EC4899' },
    { name: 'Orange', value: '30 100% 60%', preview: '#F97316' },
    { name: 'Green', value: '173 58% 39%', preview: '#10B981' },
    { name: 'Blue', value: '217 91% 60%', preview: '#3B82F6' },
    { name: 'Red', value: '0 84% 60%', preview: '#EF4444' },
    { name: '<PERSON>', value: '160 84% 39%', preview: '#059669' },
    { name: 'Violet', value: '262 83% 58%', preview: '#8B5CF6' },
    { name: '<PERSON>', value: '43 96% 56%', preview: '#F59E0B' },
];

const THEME_PRESETS = [
    { name: 'Default', theme: 'system', accent: '262 100% 70%' },
    { name: 'Ocean', theme: 'dark', accent: '190 100% 60%' },
    { name: 'Sunset', theme: 'light', accent: '30 100% 60%' },
    { name: 'Forest', theme: 'dark', accent: '173 58% 39%' },
    { name: 'Neon', theme: 'dark', accent: '320 100% 65%' },
];

const ACCENT_KEY = 'ui-accent';
const PRESET_KEY = 'ui-preset';
const FAVORITES_KEY = 'ui-favorites';

export const ThemeSwitcher: React.FC = () => {
    const { theme, setTheme } = useTheme();
    const [accent, setAccent] = useState(() => localStorage.getItem(ACCENT_KEY) || ACCENT_COLORS[0].value);
    const [open, setOpen] = useState(false);
    const [activeTab, setActiveTab] = useState<'theme' | 'colors' | 'presets'>('theme');
    const [previewMode, setPreviewMode] = useState(false);
    const [originalAccent, setOriginalAccent] = useState(accent);
    const [favorites, setFavorites] = useState<string[]>(() =>
        JSON.parse(localStorage.getItem(FAVORITES_KEY) || '[]')
    );
    const popoverRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        document.documentElement.style.setProperty('--primary', accent);
        if (!previewMode) {
            localStorage.setItem(ACCENT_KEY, accent);
        }
    }, [accent, previewMode]);

    const handlePreview = (color: string) => {
        if (!previewMode) {
            setOriginalAccent(accent);
            setPreviewMode(true);
        }
        setAccent(color);
    };

    const confirmChanges = () => {
        setPreviewMode(false);
        localStorage.setItem(ACCENT_KEY, accent);
    };

    const cancelChanges = () => {
        setAccent(originalAccent);
        setPreviewMode(false);
    };

    const toggleFavorite = (color: string) => {
        const newFavorites = favorites.includes(color)
            ? favorites.filter(f => f !== color)
            : [...favorites, color];
        setFavorites(newFavorites);
        localStorage.setItem(FAVORITES_KEY, JSON.stringify(newFavorites));
    };

    const applyPreset = (preset: typeof THEME_PRESETS[0]) => {
        setTheme(preset.theme as any);
        setAccent(preset.accent);
        localStorage.setItem(PRESET_KEY, preset.name);
    };

    // Close popover when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
                setOpen(false);
            }
        }
        if (open) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [open]);

    return (
        <div className="relative">
            <Button
                variant="outline"
                size="sm"
                className="glass-border backdrop-blur-strong hover:scale-105 transition-all duration-200"
                onClick={() => setOpen((v) => !v)}
                aria-label="Open theme and color picker"
            >
                <Palette className="w-4 h-4 mr-2" />
                Customize
                {previewMode && <Sparkles className="w-3 h-3 ml-1 text-primary animate-pulse" />}
            </Button>

            {open && (
                <div
                    ref={popoverRef}
                    className="absolute right-0 mt-2 w-80 z-50 glass-card backdrop-blur-strong border border-border/20 rounded-xl shadow-2xl p-0 animate-scale-in overflow-hidden"
                >
                    {/* Header */}
                    <div className="p-4 border-b border-border/10 bg-gradient-to-r from-primary/5 to-accent-cyan/5">
                        <div className="flex items-center justify-between">
                            <h3 className="font-semibold text-lg flex items-center gap-2">
                                <Palette className="w-5 h-5 text-primary" />
                                Theme Studio
                            </h3>
                            {previewMode && (
                                <div className="flex gap-1">
                                    <Button size="sm" variant="ghost" onClick={cancelChanges}>
                                        <RotateCcw className="w-3 h-3" />
                                    </Button>
                                    <Button size="sm" onClick={confirmChanges}>
                                        <Save className="w-3 h-3" />
                                    </Button>
                                </div>
                            )}
                        </div>

                        {/* Tabs */}
                        <div className="flex gap-1 mt-3 p-1 bg-muted/30 rounded-lg">
                            {[
                                { id: 'theme', label: 'Theme', icon: Sun },
                                { id: 'colors', label: 'Colors', icon: Palette },
                                { id: 'presets', label: 'Presets', icon: Sparkles }
                            ].map(tab => (
                                <button
                                    key={tab.id}
                                    className={`flex-1 flex items-center justify-center gap-1 px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200 ${activeTab === tab.id
                                            ? 'bg-primary text-primary-foreground shadow-sm'
                                            : 'hover:bg-muted/50'
                                        }`}
                                    onClick={() => setActiveTab(tab.id as any)}
                                >
                                    <tab.icon className="w-3 h-3" />
                                    {tab.label}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-4 max-h-80 overflow-y-auto">
                        {activeTab === 'theme' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium mb-2 block">Appearance</label>
                                    <div className="grid grid-cols-3 gap-2">
                                        {[
                                            { value: 'light', label: 'Light', icon: Sun },
                                            { value: 'dark', label: 'Dark', icon: Moon },
                                            { value: 'system', label: 'System', icon: Monitor }
                                        ].map(option => (
                                            <button
                                                key={option.value}
                                                className={`p-3 rounded-lg border-2 transition-all duration-200 flex flex-col items-center gap-2 ${theme === option.value
                                                        ? 'border-primary bg-primary/10 text-primary'
                                                        : 'border-border hover:border-primary/50 hover:bg-accent/50'
                                                    }`}
                                                onClick={() => setTheme(option.value as any)}
                                            >
                                                <option.icon className="w-5 h-5" />
                                                <span className="text-xs font-medium">{option.label}</span>
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeTab === 'colors' && (
                            <div className="space-y-4">
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <label className="text-sm font-medium">Accent Colors</label>
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => setPreviewMode(!previewMode)}
                                            className="text-xs"
                                        >
                                            <Eye className="w-3 h-3 mr-1" />
                                            Preview
                                        </Button>
                                    </div>
                                    <div className="grid grid-cols-5 gap-2">
                                        {ACCENT_COLORS.map((color) => (
                                            <div key={color.value} className="relative">
                                                <button
                                                    className={`w-full aspect-square rounded-lg border-2 transition-all duration-200 relative overflow-hidden ${accent === color.value
                                                            ? 'border-primary scale-110 ring-2 ring-primary/30'
                                                            : 'border-border hover:border-primary/50 hover:scale-105'
                                                        }`}
                                                    style={{ background: color.preview }}
                                                    onClick={() => previewMode ? handlePreview(color.value) : setAccent(color.value)}
                                                    onMouseEnter={() => previewMode && handlePreview(color.value)}
                                                    title={color.name}
                                                >
                                                    {accent === color.value && (
                                                        <div className="absolute inset-0 flex items-center justify-center">
                                                            <div className="w-3 h-3 bg-white rounded-full flex items-center justify-center">
                                                                <div className="w-1.5 h-1.5 bg-current rounded-full"></div>
                                                            </div>
                                                        </div>
                                                    )}
                                                </button>
                                                <button
                                                    className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border border-border bg-background flex items-center justify-center transition-all duration-200 ${favorites.includes(color.value) ? 'text-red-500' : 'text-muted-foreground hover:text-foreground'
                                                        }`}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        toggleFavorite(color.value);
                                                    }}
                                                >
                                                    <span className="text-xs">♥</span>
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {favorites.length > 0 && (
                                    <div>
                                        <label className="text-sm font-medium mb-2 block">Favorites</label>
                                        <div className="flex gap-2 flex-wrap">
                                            {favorites.map(fav => {
                                                const color = ACCENT_COLORS.find(c => c.value === fav);
                                                return color ? (
                                                    <button
                                                        key={fav}
                                                        className={`w-8 h-8 rounded-full border-2 transition-all duration-200 ${accent === fav ? 'border-primary scale-110' : 'border-border hover:scale-105'
                                                            }`}
                                                        style={{ background: color.preview }}
                                                        onClick={() => setAccent(fav)}
                                                        title={color.name}
                                                    />
                                                ) : null;
                                            })}
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}

                        {activeTab === 'presets' && (
                            <div className="space-y-3">
                                <label className="text-sm font-medium block">Quick Presets</label>
                                {THEME_PRESETS.map(preset => (
                                    <button
                                        key={preset.name}
                                        className="w-full p-3 rounded-lg border border-border hover:border-primary/50 hover:bg-accent/50 transition-all duration-200 text-left"
                                        onClick={() => applyPreset(preset)}
                                    >
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <div className="font-medium text-sm">{preset.name}</div>
                                                <div className="text-xs text-muted-foreground capitalize">
                                                    {preset.theme} theme
                                                </div>
                                            </div>
                                            <div
                                                className="w-6 h-6 rounded-full border border-border"
                                                style={{ background: `hsl(${preset.accent})` }}
                                            />
                                        </div>
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>

                    {previewMode && (
                        <div className="p-3 border-t border-border/10 bg-muted/20">
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                                <span className="flex items-center gap-1">
                                    <Eye className="w-3 h-3" />
                                    Preview Mode
                                </span>
                                <span>Hover colors to preview</span>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

// Add fade-in animation
// In your CSS (index.css or global):
// .animate-fade-in { animation: fadeIn 0.2s ease; }
// @keyframes fadeIn { from { opacity: 0; transform: translateY(-8px);} to { opacity: 1; transform: translateY(0);} } 