import React, { useEffect, useState, useRef } from 'react';
import { useTheme } from '@/hooks/use-theme';
import { Pa<PERSON>, <PERSON>, <PERSON>, Moon, Spark<PERSON>, Eye, Settings } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

const ACCENT_COLORS = [
    { name: 'Violet', value: '262 100% 70%', preview: '#8B5CF6' },
    { name: '<PERSON><PERSON>', value: '190 100% 60%', preview: '#06B6D4' },
    { name: 'Pink', value: '320 100% 65%', preview: '#EC4899' },
    { name: 'Orange', value: '30 100% 60%', preview: '#F97316' },
    { name: 'Emerald', value: '160 84% 39%', preview: '#10B981' },
    { name: 'Amber', value: '43 96% 56%', preview: '#F59E0B' },
    { name: '<PERSON>', value: '351 83% 61%', preview: '#F43F5E' },
    { name: 'Indigo', value: '239 84% 67%', preview: '#6366F1' },
    { name: 'Tea<PERSON>', value: '173 80% 40%', preview: '#14B8A6' },
    { name: 'Lime', value: '84 81% 44%', preview: '#84CC16' },
];

const GRADIENT_THEMES = [
    { name: 'Aurora', colors: ['262 100% 70%', '190 100% 60%', '320 100% 65%'] },
    { name: 'Sunset', colors: ['30 100% 60%', '351 83% 61%', '43 96% 56%'] },
    { name: 'Ocean', colors: ['239 84% 67%', '190 100% 60%', '173 80% 40%'] },
    { name: 'Forest', colors: ['160 84% 39%', '84 81% 44%', '173 80% 40%'] },
];

const ACCENT_KEY = 'ui-accent';
const GRADIENT_KEY = 'ui-gradient';

export const ThemeSwitcher: React.FC = () => {
    const { theme, setTheme } = useTheme();
    const [accent, setAccent] = useState(() => localStorage.getItem(ACCENT_KEY) || ACCENT_COLORS[0].value);
    const [selectedGradient, setSelectedGradient] = useState(() => localStorage.getItem(GRADIENT_KEY) || '');
    const [open, setOpen] = useState(false);
    const [previewMode, setPreviewMode] = useState(false);
    const [activeTab, setActiveTab] = useState<'theme' | 'colors' | 'gradients'>('theme');
    const popoverRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        document.documentElement.style.setProperty('--primary', accent);
        localStorage.setItem(ACCENT_KEY, accent);

        // Apply gradient if selected
        if (selectedGradient) {
            const gradient = GRADIENT_THEMES.find(g => g.name === selectedGradient);
            if (gradient) {
                document.documentElement.style.setProperty('--gradient-primary', gradient.colors.join(', '));
            }
        }
    }, [accent, selectedGradient]);

    // Close popover when clicking outside
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
                setOpen(false);
            }
        }
        if (open) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [open]);

    const handlePreviewToggle = () => {
        setPreviewMode(!previewMode);
        if (!previewMode) {
            // Enable preview mode - add visual indicators
            document.documentElement.classList.add('theme-preview-mode');
        } else {
            // Disable preview mode
            document.documentElement.classList.remove('theme-preview-mode');
        }
    };

    const handleGradientSelect = (gradientName: string) => {
        setSelectedGradient(gradientName);
        localStorage.setItem(GRADIENT_KEY, gradientName);
    };

    return (
        <div className="relative">
            <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-lg bg-card/80 backdrop-blur-sm shadow-sm hover:bg-card/90 transition-all duration-200 hover:scale-105 border border-border/50"
                onClick={() => setOpen(!open)}
                aria-label="Toggle theme"
            >
                <Palette className="h-4 w-4 text-primary" />
            </Button>

            {open && (
                <div
                    ref={popoverRef}
                    className="absolute right-0 mt-2 w-80 z-50 bg-card/95 backdrop-blur-xl border border-border rounded-xl shadow-2xl animate-fade-in overflow-hidden"
                >
                    {/* Header */}
                    <div className="p-4 border-b border-border bg-gradient-to-r from-primary/10 to-accent/10">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <Sparkles className="h-5 w-5 text-primary" />
                                <h3 className="font-semibold text-lg">Theme Studio</h3>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handlePreviewToggle}
                                className={`gap-1 ${previewMode ? 'bg-primary/20 text-primary' : ''}`}
                            >
                                <Eye className="h-4 w-4" />
                                {previewMode ? 'Exit Preview' : 'Preview'}
                            </Button>
                        </div>

                        {/* Tab Navigation */}
                        <div className="flex gap-1 mt-3 bg-muted/50 rounded-lg p-1">
                            {[
                                { id: 'theme', label: 'Theme', icon: Monitor },
                                { id: 'colors', label: 'Colors', icon: Palette },
                                { id: 'gradients', label: 'Effects', icon: Sparkles }
                            ].map((tab) => (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id as any)}
                                    className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${activeTab === tab.id
                                        ? 'bg-background text-foreground shadow-sm'
                                        : 'text-muted-foreground hover:text-foreground'
                                        }`}
                                >
                                    <tab.icon className="h-4 w-4" />
                                    <span className="hidden sm:inline">{tab.label}</span>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-4 max-h-96 overflow-y-auto">
                        {activeTab === 'theme' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium mb-2 block">Base Theme</label>
                                    <div className="grid grid-cols-3 gap-2">
                                        {[
                                            { value: 'light', label: 'Light', icon: Sun },
                                            { value: 'dark', label: 'Dark', icon: Moon },
                                            { value: 'system', label: 'Auto', icon: Monitor }
                                        ].map((themeOption) => (
                                            <Button
                                                key={themeOption.value}
                                                variant={theme === themeOption.value ? 'default' : 'outline'}
                                                size="sm"
                                                onClick={() => setTheme(themeOption.value as any)}
                                                className="flex flex-col gap-1 h-auto py-3"
                                            >
                                                <themeOption.icon className="h-4 w-4" />
                                                <span className="text-xs">{themeOption.label}</span>
                                            </Button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeTab === 'colors' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium mb-3 block">Accent Colors</label>
                                    <div className="grid grid-cols-5 gap-3">
                                        {ACCENT_COLORS.map((color) => (
                                            <button
                                                key={color.value}
                                                className={`group relative w-12 h-12 rounded-xl border-2 transition-all duration-200 hover:scale-110 ${accent === color.value
                                                    ? 'border-primary scale-110 ring-2 ring-primary/30'
                                                    : 'border-border hover:border-primary/50'
                                                    }`}
                                                style={{ background: color.preview }}
                                                onClick={() => setAccent(color.value)}
                                                title={color.name}
                                            >
                                                {accent === color.value && (
                                                    <div className="absolute inset-0 flex items-center justify-center">
                                                        <div className="w-3 h-3 bg-white rounded-full shadow-lg"></div>
                                                    </div>
                                                )}
                                                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                                                    {color.name}
                                                </div>
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeTab === 'gradients' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium mb-3 block">Gradient Themes</label>
                                    <div className="space-y-2">
                                        {GRADIENT_THEMES.map((gradient) => (
                                            <button
                                                key={gradient.name}
                                                onClick={() => handleGradientSelect(gradient.name)}
                                                className={`w-full p-3 rounded-lg border-2 transition-all duration-200 hover:scale-[1.02] ${selectedGradient === gradient.name
                                                    ? 'border-primary ring-2 ring-primary/30'
                                                    : 'border-border hover:border-primary/50'
                                                    }`}
                                                style={{
                                                    background: `linear-gradient(135deg, hsl(${gradient.colors[0]}), hsl(${gradient.colors[1]}), hsl(${gradient.colors[2]}))`
                                                }}
                                            >
                                                <div className="text-white font-medium text-sm drop-shadow-lg">
                                                    {gradient.name}
                                                </div>
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Footer */}
                    <div className="p-4 border-t border-border bg-muted/30">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>Preferences auto-saved</span>
                            <Button variant="ghost" size="sm" className="h-6 px-2">
                                <Settings className="h-3 w-3" />
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// Add fade-in animation
// In your CSS (index.css or global):
// .animate-fade-in { animation: fadeIn 0.2s ease; }
// @keyframes fadeIn { from { opacity: 0; transform: translateY(-8px);} to { opacity: 1; transform: translateY(0);} } 