import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  X, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  AlertCircle,
  Clock,
  Star,
  Archive,
  Settings,
  Filter
} from 'lucide-react';
import { Button } from './button';
import { Badge } from './badge';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'warning' | 'info' | 'error';
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  read: boolean;
  starred: boolean;
  category: string;
  actions?: Array<{
    label: string;
    action: () => void;
    variant?: 'default' | 'destructive';
  }>;
  autoHide?: boolean;
  persistent?: boolean;
}

const NOTIFICATIONS_KEY = 'app-notifications';
const SETTINGS_KEY = 'notification-settings';

interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  categories: Record<string, boolean>;
  autoHide: boolean;
  hideAfter: number; // seconds
}

const defaultSettings: NotificationSettings = {
  enabled: true,
  sound: true,
  desktop: true,
  categories: {
    'system': true,
    'maintenance': true,
    'safety': true,
    'financial': true,
    'hr': true,
  },
  autoHide: true,
  hideAfter: 5,
};

export const NotificationSystem: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>(() => {
    const saved = localStorage.getItem(NOTIFICATIONS_KEY);
    return saved ? JSON.parse(saved).map((n: any) => ({
      ...n,
      timestamp: new Date(n.timestamp)
    })) : [];
  });
  
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'starred'>('all');
  const [settings, setSettings] = useState<NotificationSettings>(() => {
    const saved = localStorage.getItem(SETTINGS_KEY);
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  // Save notifications to localStorage
  useEffect(() => {
    localStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(notifications));
  }, [notifications]);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
  }, [settings]);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read' | 'starred'>) => {
    if (!settings.enabled || !settings.categories[notification.category]) return;

    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false,
      starred: false,
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Play sound if enabled
    if (settings.sound) {
      const audio = new Audio('/notification-sound.mp3');
      audio.play().catch(() => {}); // Ignore errors
    }

    // Show desktop notification if enabled and permission granted
    if (settings.desktop && 'Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
      });
    }

    // Auto-hide if enabled
    if (settings.autoHide && notification.autoHide !== false) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, settings.hideAfter * 1000);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, read: true } : n
    ));
  };

  const toggleStar = (id: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, starred: !n.starred } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const getIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return AlertCircle;
      case 'info': return Info;
      default: return Info;
    }
  };

  const getTypeColor = (type: Notification['type']) => {
    switch (type) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      case 'info': return 'text-blue-500';
      default: return 'text-gray-500';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread': return !notification.read;
      case 'starred': return notification.starred;
      default: return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  // Request desktop notification permission
  useEffect(() => {
    if (settings.desktop && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, [settings.desktop]);

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        className="glass-border backdrop-blur-strong hover:scale-105 transition-all duration-200 relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Bell className="w-4 h-4" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs animate-pulse"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 z-50 glass-card backdrop-blur-strong border border-border/20 rounded-xl shadow-2xl animate-scale-in overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-border/10 bg-gradient-to-r from-primary/5 to-accent-cyan/5">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-lg flex items-center gap-2">
                <Bell className="w-5 h-5 text-primary" />
                Notifications
                {unreadCount > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {unreadCount} new
                  </Badge>
                )}
              </h3>
              <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Filters */}
            <div className="flex gap-1 p-1 bg-muted/30 rounded-lg">
              {[
                { id: 'all', label: 'All', icon: Bell },
                { id: 'unread', label: 'Unread', icon: Info },
                { id: 'starred', label: 'Starred', icon: Star }
              ].map(tab => (
                <button
                  key={tab.id}
                  className={`flex-1 flex items-center justify-center gap-1 px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200 ${
                    filter === tab.id 
                      ? 'bg-primary text-primary-foreground shadow-sm' 
                      : 'hover:bg-muted/50'
                  }`}
                  onClick={() => setFilter(tab.id as any)}
                >
                  <tab.icon className="w-3 h-3" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No notifications</p>
              </div>
            ) : (
              <div className="p-2 space-y-1">
                {filteredNotifications.map(notification => {
                  const Icon = getIcon(notification.type);
                  return (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border transition-all duration-200 hover:bg-accent/50 ${
                        notification.read 
                          ? 'border-border/50 opacity-75' 
                          : 'border-primary/20 bg-primary/5'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`mt-0.5 ${getTypeColor(notification.type)}`}>
                          <Icon className="w-4 h-4" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <div className="font-medium text-sm flex items-center gap-2">
                                {notification.title}
                                <div 
                                  className={`w-2 h-2 rounded-full ${getPriorityColor(notification.priority)}`}
                                  title={`${notification.priority} priority`}
                                />
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                {notification.message}
                              </p>
                              <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                                <Clock className="w-3 h-3" />
                                {notification.timestamp.toLocaleTimeString()}
                                <Badge variant="outline" className="text-xs">
                                  {notification.category}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <button
                                className={`p-1 rounded hover:bg-accent transition-colors ${
                                  notification.starred ? 'text-yellow-500' : 'text-muted-foreground'
                                }`}
                                onClick={() => toggleStar(notification.id)}
                              >
                                <Star className="w-3 h-3" fill={notification.starred ? 'currentColor' : 'none'} />
                              </button>
                              
                              {!notification.read && (
                                <button
                                  className="p-1 rounded hover:bg-accent text-muted-foreground transition-colors"
                                  onClick={() => markAsRead(notification.id)}
                                >
                                  <CheckCircle className="w-3 h-3" />
                                </button>
                              )}
                              
                              <button
                                className="p-1 rounded hover:bg-accent text-muted-foreground transition-colors"
                                onClick={() => removeNotification(notification.id)}
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </div>
                          </div>

                          {notification.actions && (
                            <div className="flex gap-2 mt-3">
                              {notification.actions.map((action, index) => (
                                <Button
                                  key={index}
                                  size="sm"
                                  variant={action.variant || 'outline'}
                                  className="text-xs h-7"
                                  onClick={action.action}
                                >
                                  {action.label}
                                </Button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-3 border-t border-border/10 bg-muted/20">
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button size="sm" variant="ghost" onClick={markAllAsRead} className="text-xs">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Mark all read
                  </Button>
                  <Button size="sm" variant="ghost" onClick={clearAll} className="text-xs">
                    <Archive className="w-3 h-3 mr-1" />
                    Clear all
                  </Button>
                </div>
                <Button size="sm" variant="ghost" className="text-xs">
                  <Settings className="w-3 h-3 mr-1" />
                  Settings
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Hook for adding notifications from anywhere in the app
export const useNotifications = () => {
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read' | 'starred'>) => {
    // This would typically dispatch to a global state or context
    // For now, we'll use a custom event
    window.dispatchEvent(new CustomEvent('addNotification', { detail: notification }));
  };

  return { addNotification };
};
