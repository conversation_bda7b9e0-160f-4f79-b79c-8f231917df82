import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { 
  X, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  AlertCircle,
  Zap,
  Clock,
  Bell,
  Settings,
  Volume2,
  VolumeX
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  duration?: number; // Auto-dismiss time in ms, 0 for persistent
  timestamp: Date;
  actions?: Array<{
    label: string;
    action: () => void;
    variant?: 'default' | 'destructive' | 'outline';
  }>;
  category?: string;
  source?: string;
  persistent?: boolean;
  sound?: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => string;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  markAsRead: (id: string) => void;
  settings: NotificationSettings;
  updateSettings: (settings: Partial<NotificationSettings>) => void;
}

interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxVisible: number;
  defaultDuration: number;
  groupByCategory: boolean;
}

const defaultSettings: NotificationSettings = {
  enabled: true,
  sound: true,
  position: 'top-right',
  maxVisible: 5,
  defaultDuration: 5000,
  groupByCategory: false,
};

const NotificationContext = createContext<NotificationContextType | null>(null);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within NotificationProvider');
  }
  return context;
};

const typeConfig = {
  success: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-50 dark:bg-green-950/50', border: 'border-green-200 dark:border-green-800' },
  error: { icon: AlertCircle, color: 'text-red-600', bg: 'bg-red-50 dark:bg-red-950/50', border: 'border-red-200 dark:border-red-800' },
  warning: { icon: AlertTriangle, color: 'text-yellow-600', bg: 'bg-yellow-50 dark:bg-yellow-950/50', border: 'border-yellow-200 dark:border-yellow-800' },
  info: { icon: Info, color: 'text-blue-600', bg: 'bg-blue-50 dark:bg-blue-950/50', border: 'border-blue-200 dark:border-blue-800' },
  system: { icon: Zap, color: 'text-purple-600', bg: 'bg-purple-50 dark:bg-purple-950/50', border: 'border-purple-200 dark:border-purple-800' },
};

const priorityConfig = {
  low: { badge: 'bg-gray-100 text-gray-600', urgency: '' },
  medium: { badge: 'bg-blue-100 text-blue-600', urgency: '' },
  high: { badge: 'bg-orange-100 text-orange-600', urgency: 'animate-pulse' },
  critical: { badge: 'bg-red-100 text-red-600', urgency: 'animate-bounce' },
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>(() => {
    const saved = localStorage.getItem('notification-settings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
      duration: notification.duration ?? settings.defaultDuration,
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      return updated.slice(0, settings.maxVisible * 2); // Keep some buffer
    });

    // Play sound if enabled
    if (settings.sound && notification.sound !== false) {
      playNotificationSound(notification.type, notification.priority);
    }

    // Auto-dismiss if duration is set
    if (newNotification.duration && newNotification.duration > 0 && !newNotification.persistent) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, [settings]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const markAsRead = useCallback((id: string) => {
    // Implementation for marking as read (could update a read status)
    removeNotification(id);
  }, [removeNotification]);

  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      localStorage.setItem('notification-settings', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const playNotificationSound = (type: Notification['type'], priority: Notification['priority']) => {
    if (!settings.sound) return;
    
    // Create audio context for different notification sounds
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // Different frequencies for different types/priorities
    const frequencies = {
      success: 800,
      info: 600,
      warning: 400,
      error: 300,
      system: 1000,
    };
    
    const volumes = {
      low: 0.1,
      medium: 0.2,
      high: 0.3,
      critical: 0.4,
    };
    
    oscillator.frequency.setValueAtTime(frequencies[type], audioContext.currentTime);
    gainNode.gain.setValueAtTime(volumes[priority], audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  };

  const value: NotificationContextType = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    markAsRead,
    settings,
    updateSettings,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  );
};

const NotificationContainer: React.FC = () => {
  const { notifications, removeNotification, settings } = useNotifications();

  const visibleNotifications = notifications.slice(0, settings.maxVisible);

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  if (!settings.enabled || visibleNotifications.length === 0) {
    return null;
  }

  return createPortal(
    <div className={`fixed ${positionClasses[settings.position]} z-50 space-y-3 max-w-sm w-full`}>
      {visibleNotifications.map((notification, index) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onRemove={() => removeNotification(notification.id)}
          style={{
            animationDelay: `${index * 100}ms`,
          }}
        />
      ))}
    </div>,
    document.body
  );
};

const NotificationItem: React.FC<{
  notification: Notification;
  onRemove: () => void;
  style?: React.CSSProperties;
}> = ({ notification, onRemove, style }) => {
  const config = typeConfig[notification.type];
  const priorityStyle = priorityConfig[notification.priority];
  const IconComponent = config.icon;

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div
      className={`${config.bg} ${config.border} border backdrop-blur-sm rounded-lg shadow-lg overflow-hidden animate-fade-in ${priorityStyle.urgency}`}
      style={style}
    >
      <div className="p-4">
        <div className="flex items-start gap-3">
          <IconComponent className={`h-5 w-5 ${config.color} mt-0.5 flex-shrink-0`} />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-semibold text-sm text-foreground">{notification.title}</h4>
              <Badge className={`text-xs ${priorityStyle.badge}`}>
                {notification.priority}
              </Badge>
            </div>
            <p className="text-sm text-foreground/80 leading-relaxed mb-2">
              {notification.message}
            </p>
            
            {/* Metadata */}
            <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
              <div className="flex items-center gap-2">
                <Clock className="h-3 w-3" />
                <span>{formatTime(notification.timestamp)}</span>
                {notification.source && (
                  <>
                    <span>•</span>
                    <span>{notification.source}</span>
                  </>
                )}
              </div>
              {notification.category && (
                <Badge variant="outline" className="text-xs">
                  {notification.category}
                </Badge>
              )}
            </div>

            {/* Actions */}
            {notification.actions && notification.actions.length > 0 && (
              <div className="flex gap-2">
                {notification.actions.map((action, index) => (
                  <Button
                    key={index}
                    size="sm"
                    variant={action.variant || 'outline'}
                    onClick={() => {
                      action.action();
                      onRemove();
                    }}
                    className="text-xs h-7"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Notification Settings Component
export const NotificationSettings: React.FC<{
  isOpen: boolean;
  onClose: () => void;
}> = ({ isOpen, onClose }) => {
  const { settings, updateSettings, clearAll } = useNotifications();

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-fade-in">
      <div className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md">
        <div className="mx-4 bg-card border border-border rounded-xl shadow-2xl overflow-hidden">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-primary" />
                <h3 className="font-semibold">Notification Settings</h3>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Enable Notifications</span>
              <Button
                variant={settings.enabled ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateSettings({ enabled: !settings.enabled })}
              >
                {settings.enabled ? 'Enabled' : 'Disabled'}
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Sound Alerts</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateSettings({ sound: !settings.sound })}
              >
                {settings.sound ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>
            </div>
            
            <div className="space-y-2">
              <span className="text-sm font-medium">Position</span>
              <div className="grid grid-cols-2 gap-2">
                {(['top-right', 'top-left', 'bottom-right', 'bottom-left'] as const).map((position) => (
                  <Button
                    key={position}
                    variant={settings.position === position ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => updateSettings({ position })}
                    className="text-xs"
                  >
                    {position.replace('-', ' ')}
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="pt-4 border-t border-border">
              <Button
                variant="destructive"
                size="sm"
                onClick={clearAll}
                className="w-full"
              >
                Clear All Notifications
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

// Helper functions for common notifications
export const notificationHelpers = {
  success: (title: string, message: string, options?: Partial<Notification>) => ({
    title,
    message,
    type: 'success' as const,
    priority: 'medium' as const,
    ...options,
  }),
  
  error: (title: string, message: string, options?: Partial<Notification>) => ({
    title,
    message,
    type: 'error' as const,
    priority: 'high' as const,
    persistent: true,
    ...options,
  }),
  
  warning: (title: string, message: string, options?: Partial<Notification>) => ({
    title,
    message,
    type: 'warning' as const,
    priority: 'medium' as const,
    ...options,
  }),
  
  info: (title: string, message: string, options?: Partial<Notification>) => ({
    title,
    message,
    type: 'info' as const,
    priority: 'low' as const,
    ...options,
  }),
  
  system: (title: string, message: string, options?: Partial<Notification>) => ({
    title,
    message,
    type: 'system' as const,
    priority: 'medium' as const,
    category: 'System',
    ...options,
  }),
};
