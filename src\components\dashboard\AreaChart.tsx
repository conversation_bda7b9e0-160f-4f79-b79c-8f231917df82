
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Area, AreaChart as RechartsAreaChart, CartesianGrid, ResponsiveContainer, Tooltip, <PERSON>Axis, YAxis } from "recharts";
import { cn } from "@/lib/utils";

interface AreaChartProps {
  title: string;
  description?: string;
  data: Array<{
    name: string;
    [key: string]: number | string;
  }>;
  categories: Array<{
    name: string;
    color: string;
  }>;
  className?: string;
  height?: number;
}

export function AreaChart({
  title,
  description,
  data,
  categories,
  className,
  height = 300,
}: AreaChartProps) {
  const [chartData, setChartData] = useState(data);

  useEffect(() => {
    // Animate data loading
    const animateData = () => {
      setChartData(data);
    };
    
    const timer = setTimeout(animateData, 300);
    return () => clearTimeout(timer);
  }, [data]);
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <RechartsAreaChart
            data={chartData}
            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
          >
            <defs>
              {categories.map((category) => (
                <linearGradient
                  key={category.name}
                  id={`color-${category.name}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={category.color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={category.color} stopOpacity={0.1} />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
            />
            <Tooltip />
            {categories.map((category) => (
              <Area
                key={category.name}
                type="monotone"
                dataKey={category.name}
                stroke={category.color}
                fillOpacity={1}
                fill={`url(#color-${category.name})`}
              />
            ))}
          </RechartsAreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
