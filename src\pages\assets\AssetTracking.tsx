
import React, { useState, useMemo, useEffect } from 'react';
import { AppLayout } from '@/components/layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MapPin,
  QrCode,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  Search,
  Filter,
  Download,
  RefreshCw,
  Navigation,
  Calendar,
  ArrowRight,
  Eye,
  Edit,
  MoreHorizontal,
  Scan,
  UserCheck,
  UserX,
  Building,
  Activity
} from 'lucide-react';
import { SAMPLE_ASSETS, SAMPLE_CHECKOUTS, SAMPLE_MOVEMENTS, LOCATIONS, SAMPLE_USERS } from '@/data/assetSampleData';
import { Asset, AssetCheckout, AssetMovement, AssetLocation } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';
import { useDynamicColors } from '@/hooks/use-theme-colors';

// Asset Tracking Component
const AssetTracking: React.FC = () => {
  const dynamicColors = useDynamicColors();
  const [assets] = useState<Asset[]>(SAMPLE_ASSETS);
  const [checkouts] = useState<AssetCheckout[]>(SAMPLE_CHECKOUTS);
  const [movements] = useState<AssetMovement[]>(SAMPLE_MOVEMENTS);
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Filter assets by location
  const filteredAssets = useMemo(() => {
    let filtered = assets;

    if (selectedLocation !== 'all') {
      filtered = filtered.filter(asset =>
        `${asset.location.site} - ${asset.location.building}` === selectedLocation
      );
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(asset =>
        asset.name.toLowerCase().includes(query) ||
        asset.assetId.toLowerCase().includes(query) ||
        asset.assignedUser?.name.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [assets, selectedLocation, searchQuery]);

  // Get unique locations for filter
  const locations = useMemo(() => {
    const uniqueLocations = new Set(
      assets.map(asset => `${asset.location.site} - ${asset.location.building}`)
    );
    return Array.from(uniqueLocations);
  }, [assets]);

  // Statistics
  const stats = useMemo(() => {
    const checkedOutAssets = checkouts.filter(checkout => checkout.status === 'checked-out').length;
    const overdueAssets = checkouts.filter(checkout => checkout.status === 'overdue').length;
    const recentMovements = movements.filter(movement => {
      const movementDate = new Date(movement.movedAt);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return movementDate > weekAgo;
    }).length;

    return {
      totalTracked: assets.length,
      checkedOut: checkedOutAssets,
      overdue: overdueAssets,
      recentMovements
    };
  }, [assets, checkouts, movements]);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'checked-out': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'returned': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Tracking</h1>
            <p className="text-muted-foreground">
              Real-time asset location monitoring and check-in/check-out management
            </p>
          </div>
          <div className="flex items-center gap-2">
            <ContextTooltip content={{
              title: 'Refresh Data',
              description: 'Update asset locations and status information',
              type: 'info'
            }}>
              <Button variant="outline" className="gap-2">
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </ContextTooltip>

            <ContextTooltip content={{
              title: 'QR Scanner',
              description: 'Scan asset QR codes for quick check-in/check-out',
              type: 'info'
            }}>
              <Button variant="outline" className="gap-2">
                <Scan className="h-4 w-4" />
                QR Scanner
              </Button>
            </ContextTooltip>

            <Button
              className="gap-2 border"
              style={{
                ...dynamicColors.bgPrimary(0.2),
                ...dynamicColors.borderPrimary(0.3),
                ...dynamicColors.textPrimary
              }}
            >
              <UserCheck className="h-4 w-4" />
              Check Out Asset
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ContextTooltip content={{
            title: 'Total Tracked Assets',
            description: 'Total number of assets being actively tracked in the system',
            type: 'info'
          }}>
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tracked</CardTitle>
                <MapPin className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold" style={dynamicColors.textPrimary}>{stats.totalTracked}</div>
                <p className="text-xs text-muted-foreground">
                  Across {locations.length} locations
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Checked Out Assets',
            description: 'Assets currently checked out to users and not yet returned',
            type: 'info'
          }}>
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Checked Out</CardTitle>
                <User className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.checkedOut}</div>
                <p className="text-xs text-muted-foreground">
                  Currently with users
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Overdue Returns',
            description: 'Assets that have exceeded their expected return date',
            type: 'warning'
          }}>
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                <AlertTriangle className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
                <p className="text-xs text-muted-foreground">
                  Require immediate attention
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Recent Movements',
            description: 'Asset location changes in the past 7 days',
            type: 'info'
          }}>
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recent Movements</CardTitle>
                <Activity className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.recentMovements}</div>
                <p className="text-xs text-muted-foreground">
                  Last 7 days
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Location Overview</TabsTrigger>
            <TabsTrigger value="checkouts">Check-in/Out</TabsTrigger>
            <TabsTrigger value="movements">Asset Movements</TabsTrigger>
            <TabsTrigger value="qr-codes">QR Codes</TabsTrigger>
          </TabsList>

          {/* Location Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <Card className="glass-theme">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" style={dynamicColors.textPrimary} />
                      Asset Location Map
                    </CardTitle>
                    <CardDescription>
                      Interactive facility map showing real-time asset locations
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <select
                      value={selectedLocation}
                      onChange={(e) => setSelectedLocation(e.target.value)}
                      className="px-3 py-2 border border-border rounded-md bg-background"
                    >
                      <option value="all">All Locations</option>
                      {locations.map((location) => (
                        <option key={location} value={location}>
                          {location}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Facility Map Placeholder */}
                <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-8 min-h-[400px] border-2 border-dashed border-blue-200 dark:border-blue-800">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <Building className="h-16 w-16 text-blue-500 mx-auto" />
                      <div>
                        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                          Interactive Facility Map
                        </h3>
                        <p className="text-blue-700 dark:text-blue-300 max-w-md">
                          This area will display an interactive map showing asset locations with clickable pins,
                          heat maps, and real-time position updates.
                        </p>
                      </div>
                      <div className="flex items-center justify-center gap-4 text-sm text-blue-600 dark:text-blue-400">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span>Active Assets ({filteredAssets.filter(a => a.status === 'Active').length})</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                          <span>In Maintenance ({filteredAssets.filter(a => a.status === 'Maintenance').length})</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span>Checked Out ({stats.checkedOut})</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Location Distribution */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {locations.slice(0, 6).map((location) => {
                    const locationAssets = assets.filter(asset =>
                      `${asset.location.site} - ${asset.location.building}` === location
                    );
                    return (
                      <Card key={location} className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{location}</h4>
                              <p className="text-sm text-muted-foreground">
                                {locationAssets.length} assets
                              </p>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold">
                                {locationAssets.filter(a => a.status === 'Active').length}
                              </div>
                              <div className="text-xs text-muted-foreground">Active</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Check-in/Check-out Tab */}
          <TabsContent value="checkouts" className="space-y-6">
            <Card className="glass-theme">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserCheck className="h-5 w-5" style={dynamicColors.textPrimary} />
                  Asset Check-in/Check-out Management
                </CardTitle>
                <CardDescription>
                  Manage asset assignments and track user responsibilities
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Search and Filter */}
                <div className="flex items-center gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search checkouts by asset or user..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline" className="gap-2">
                    <Filter className="h-4 w-4" />
                    Filter
                  </Button>
                </div>

                {/* Checkout List */}
                <div className="space-y-4">
                  {checkouts.slice(0, 10).map((checkout) => {
                    const asset = assets.find(a => a.id === checkout.assetId);
                    if (!asset) return null;

                    return (
                      <div key={checkout.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                        <div className="flex items-center gap-4">
                          <div className="p-2 bg-primary/10 rounded-lg">
                            <QrCode className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{asset.name}</h3>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>{asset.assetId}</span>
                              <span>•</span>
                              <span>{checkout.checkedOutBy.name}</span>
                              <span>•</span>
                              <span>{checkout.checkedOutBy.department}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              Due: {formatDate(checkout.expectedReturnDate)}
                            </div>
                            <Badge className={`text-xs ${getStatusColor(checkout.status)}`}>
                              {checkout.status.replace('-', ' ').toUpperCase()}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-1">
                            <ContextTooltip content={{
                              title: 'Check In Asset',
                              description: 'Process the return of this asset',
                              type: 'success'
                            }}>
                              <Button variant="ghost" size="sm">
                                <UserX className="h-4 w-4" />
                              </Button>
                            </ContextTooltip>
                            <ContextTooltip content={{
                              title: 'View Details',
                              description: 'View checkout details and history',
                              type: 'info'
                            }}>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </ContextTooltip>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Asset Movements Tab */}
          <TabsContent value="movements" className="space-y-6">
            <Card className="glass-theme">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Navigation className="h-5 w-5" style={dynamicColors.textPrimary} />
                  Asset Movement History
                </CardTitle>
                <CardDescription>
                  Track asset location changes and transfer approvals
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {movements.slice(0, 10).map((movement) => {
                    const asset = assets.find(a => a.id === movement.assetId);
                    if (!asset) return null;

                    return (
                      <div key={movement.id} className="flex items-center gap-4 p-4 border border-border rounded-lg">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                          <ArrowRight className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold">{asset.name}</h3>
                          <div className="text-sm text-muted-foreground">
                            <span>{asset.assetId}</span> • <span>{movement.reason}</span>
                          </div>
                          <div className="flex items-center gap-4 mt-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">From:</span>
                              <span className="ml-1">{movement.fromLocation.building} - {movement.fromLocation.room}</span>
                            </div>
                            <ArrowRight className="h-3 w-3 text-muted-foreground" />
                            <div>
                              <span className="text-muted-foreground">To:</span>
                              <span className="ml-1">{movement.toLocation.building} - {movement.toLocation.room}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{formatDate(movement.movedAt)}</div>
                          <div className="text-xs text-muted-foreground">by {movement.movedBy}</div>
                          <Badge className={`text-xs mt-1 ${getStatusColor(movement.status)}`}>
                            {movement.status.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* QR Codes Tab */}
          <TabsContent value="qr-codes" className="space-y-6">
            <Card className="glass-theme">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" style={dynamicColors.textPrimary} />
                  QR Code Management
                </CardTitle>
                <CardDescription>
                  Generate, view, and manage QR codes for asset tracking
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {filteredAssets.slice(0, 12).map((asset) => (
                    <Card key={asset.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4 text-center">
                        <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-lg mx-auto mb-3 flex items-center justify-center">
                          <QrCode className="h-12 w-12 text-gray-500" />
                        </div>
                        <h4 className="font-medium text-sm mb-1">{asset.name}</h4>
                        <p className="text-xs text-muted-foreground font-mono mb-2">{asset.assetId}</p>
                        <div className="space-y-1">
                          <Button variant="outline" size="sm" className="w-full text-xs">
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                          <Button variant="ghost" size="sm" className="w-full text-xs">
                            <Eye className="h-3 w-3 mr-1" />
                            Preview
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Last Update Info */}
        <div className="text-center text-sm text-muted-foreground">
          Last updated: {formatDate(lastUpdate)} • Real-time tracking active
        </div>
      </div>
    </AppLayout>
  );
};

export default AssetTracking;
