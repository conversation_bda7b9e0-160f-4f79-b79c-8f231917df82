
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { StatCard } from "@/components/dashboard/StatCard";
import { MapPin, Clock, Activity, AlertCircle, Search, Filter, QrCode, Smartphone } from "lucide-react";
import { useState } from "react";

const AssetTracking = () => {
  const [selectedLocation, setSelectedLocation] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  const assets = [
    {
      id: "AST-001",
      name: "Pump Station A",
      location: "Building 1 - Floor 2",
      status: "operational",
      lastSeen: "2024-01-15 14:30",
      condition: "excellent",
      utilization: 85,
      assignedTo: "<PERSON>"
    },
    {
      id: "AST-002",
      name: "Generator Unit 3",
      location: "Outdoor Yard - Zone C",
      status: "maintenance",
      lastSeen: "2024-01-15 12:15",
      condition: "good",
      utilization: 0,
      assignedTo: "Mike Johnson"
    },
    {
      id: "AST-003",
      name: "Conveyor Belt B2",
      location: "Production Hall - Line 2",
      status: "operational",
      lastSeen: "2024-01-15 15:45",
      condition: "fair",
      utilization: 92,
      assignedTo: "Sarah Wilson"
    },
    {
      id: "AST-004",
      name: "HVAC Unit 12",
      location: "Building 3 - Roof",
      status: "offline",
      lastSeen: "2024-01-14 18:00",
      condition: "poor",
      utilization: 0,
      assignedTo: "David Brown"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "operational": return "text-green-600 bg-green-100";
      case "maintenance": return "text-yellow-600 bg-yellow-100";
      case "offline": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "excellent": return "text-green-600";
      case "good": return "text-blue-600";
      case "fair": return "text-yellow-600";
      case "poor": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Tracking</h1>
            <p className="text-muted-foreground">Real-time asset location and status monitoring</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="gap-2">
              <QrCode className="h-4 w-4" />
              Scan Asset
            </Button>
            <Button variant="outline" className="gap-2">
              <Smartphone className="h-4 w-4" />
              Mobile View
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Assets"
            value="1,247"
            icon={<Activity className="text-blue-600 h-4 w-4" />}
            description="Tracked assets"
          />
          <StatCard
            title="Operational"
            value="1,089"
            icon={<Activity className="text-green-600 h-4 w-4" />}
            description="87% uptime"
          />
          <StatCard
            title="In Maintenance"
            value="94"
            icon={<Clock className="text-yellow-600 h-4 w-4" />}
            description="Scheduled work"
          />
          <StatCard
            title="Offline/Issues"
            value="64"
            icon={<AlertCircle className="text-red-600 h-4 w-4" />}
            description="Need attention"
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Asset Filters</CardTitle>
            <CardDescription>Filter assets by location, status, and condition</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search assets..." className="pl-10" />
                </div>
              </div>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select Location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  <SelectItem value="building-1">Building 1</SelectItem>
                  <SelectItem value="building-2">Building 2</SelectItem>
                  <SelectItem value="building-3">Building 3</SelectItem>
                  <SelectItem value="outdoor">Outdoor Areas</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="operational">Operational</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                  <SelectItem value="offline">Offline</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Asset Location & Status</CardTitle>
            <CardDescription>Current asset tracking information</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Asset ID</TableHead>
                  <TableHead>Asset Name</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Seen</TableHead>
                  <TableHead>Condition</TableHead>
                  <TableHead>Utilization</TableHead>
                  <TableHead>Assigned To</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {assets.map((asset) => (
                  <TableRow key={asset.id}>
                    <TableCell className="font-medium">{asset.id}</TableCell>
                    <TableCell>{asset.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        {asset.location}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(asset.status)}>
                        {asset.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {asset.lastSeen}
                    </TableCell>
                    <TableCell className={getConditionColor(asset.condition)}>
                      {asset.condition}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${asset.utilization}%` }}
                          ></div>
                        </div>
                        <span className="text-sm">{asset.utilization}%</span>
                      </div>
                    </TableCell>
                    <TableCell>{asset.assignedTo}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">Track</Button>
                        <Button variant="ghost" size="sm">Edit</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default AssetTracking;
