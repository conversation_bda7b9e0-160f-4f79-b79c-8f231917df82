
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { StatCard } from "@/components/dashboard/StatCard";
import { BarChart } from "@/components/dashboard/BarChart";
import { AreaChart } from "@/components/dashboard/AreaChart";
import { DonutChart } from "@/components/dashboard/DonutChart";
import { Search, Filter, TrendingUp, TrendingDown, Activity, AlertTriangle, Zap, Clock } from "lucide-react";
import { useState } from "react";

const AssetPerformance = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const [selectedAssetType, setSelectedAssetType] = useState("all");

  const performanceMetrics = [
    { name: "Jan", efficiency: 88, availability: 92, quality: 95 },
    { name: "Feb", efficiency: 91, availability: 89, quality: 93 },
    { name: "Mar", efficiency: 87, availability: 94, quality: 97 },
    { name: "Apr", efficiency: 93, availability: 91, quality: 96 },
    { name: "May", efficiency: 89, availability: 88, quality: 94 },
    { name: "Jun", efficiency: 94, availability: 95, quality: 98 }
  ];

  const uptimeData = [
    { name: "Week 1", planned: 168, actual: 158, downtime: 10 },
    { name: "Week 2", planned: 168, actual: 162, downtime: 6 },
    { name: "Week 3", planned: 168, actual: 165, downtime: 3 },
    { name: "Week 4", planned: 168, actual: 160, downtime: 8 }
  ];

  const assetTypePerformance = [
    { name: "Pumps", value: 92, color: "#3B82F6" },
    { name: "Compressors", value: 88, color: "#10B981" },
    { name: "Motors", value: 95, color: "#F59E0B" },
    { name: "Generators", value: 85, color: "#EF4444" },
    { name: "Conveyors", value: 90, color: "#8B5CF6" }
  ];

  const maintenanceCosts = [
    { name: "Jan", preventive: 45000, corrective: 25000, emergency: 15000 },
    { name: "Feb", preventive: 48000, corrective: 22000, emergency: 12000 },
    { name: "Mar", preventive: 52000, corrective: 28000, emergency: 18000 },
    { name: "Apr", preventive: 46000, corrective: 20000, emergency: 10000 },
    { name: "May", preventive: 50000, corrective: 24000, emergency: 14000 },
    { name: "Jun", preventive: 49000, corrective: 26000, emergency: 16000 }
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Performance Analytics</h1>
            <p className="text-muted-foreground">Monitor asset efficiency, availability, and performance metrics</p>
          </div>
          <Button variant="outline" className="gap-2">
            <TrendingUp className="h-4 w-4" />
            Performance Report
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Overall Efficiency"
            value="91.2%"
            icon={<Zap className="text-green-600 h-4 w-4" />}
            description="+2.3% from last month"
          />
          <StatCard
            title="Availability"
            value="93.5%"
            icon={<Activity className="text-blue-600 h-4 w-4" />}
            description="Target: 95%"
          />
          <StatCard
            title="MTBF"
            value="342 hrs"
            icon={<Clock className="text-orange-600 h-4 w-4" />}
            description="Mean time between failures"
          />
          <StatCard
            title="Critical Assets"
            value="12"
            icon={<AlertTriangle className="text-red-600 h-4 w-4" />}
            description="Require attention"
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Performance Filters</CardTitle>
            <CardDescription>Filter performance data by period and asset type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search assets..." className="pl-10" />
                </div>
              </div>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select Period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedAssetType} onValueChange={setSelectedAssetType}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Asset Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="pumps">Pumps</SelectItem>
                  <SelectItem value="compressors">Compressors</SelectItem>
                  <SelectItem value="motors">Motors</SelectItem>
                  <SelectItem value="generators">Generators</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AreaChart
            title="Key Performance Indicators"
            description="Monthly efficiency, availability, and quality metrics"
            data={performanceMetrics}
            categories={[
              { name: "efficiency", color: "#3B82F6" },
              { name: "availability", color: "#10B981" },
              { name: "quality", color: "#F59E0B" }
            ]}
          />
          <BarChart
            title="Asset Uptime Analysis"
            description="Weekly planned vs actual operating hours"
            data={uptimeData}
            categories={[
              { name: "planned", color: "#E5E7EB" },
              { name: "actual", color: "#3B82F6" },
              { name: "downtime", color: "#EF4444" }
            ]}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <BarChart
              title="Maintenance Cost Analysis"
              description="Monthly breakdown of maintenance expenditure"
              data={maintenanceCosts}
              categories={[
                { name: "preventive", color: "#10B981" },
                { name: "corrective", color: "#F59E0B" },
                { name: "emergency", color: "#EF4444" }
              ]}
              height={400}
            />
          </div>
          <DonutChart
            title="Performance by Asset Type"
            description="Average efficiency scores"
            data={assetTypePerformance}
            height={400}
          />
        </div>
      </div>
    </AppLayout>
  );
};

export default AssetPerformance;
