
import React, { useState, useMemo } from 'react';
import { AppLayout } from '@/components/layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
  Clock,
  AlertTriangle,
  Target,
  Zap,
  Download,
  RefreshCw,
  Filter,
  Calendar,
  Gauge,
  Award,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { SAMPLE_ASSETS, ASSET_CATEGORIES } from '@/data/assetSampleData';
import { Asset, AssetCategory } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

// Chart color schemes
const CHART_COLORS = {
  primary: '#3B82F6',
  secondary: '#10B981',
  accent: '#F59E0B',
  warning: '#EF4444',
  purple: '#8B5CF6',
  teal: '#14B8A6',
  pink: '#EC4899',
  indigo: '#6366F1'
};

const CATEGORY_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#14B8A6', '#EC4899', '#6366F1', '#F97316', '#84CC16'
];

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background/95 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}${entry.name.includes('Rate') || entry.name.includes('Efficiency') ? '%' : ''}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Asset Performance Component
const AssetPerformance: React.FC = () => {
  const [assets] = useState<Asset[]>(SAMPLE_ASSETS);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('12m');
  const [activeTab, setActiveTab] = useState('overview');

  // Performance calculations
  const performanceData = useMemo(() => {
    let filteredAssets = assets;

    if (selectedCategory !== 'all') {
      filteredAssets = assets.filter(asset => asset.category === selectedCategory);
    }

    // Calculate KPIs
    const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.currentValue, 0);
    const totalCost = filteredAssets.reduce((sum, asset) => sum + asset.purchaseCost, 0);
    const avgUtilization = filteredAssets.reduce((sum, asset) => sum + (asset.utilizationRate || 0), 0) / filteredAssets.length;
    const roi = ((totalValue - totalCost) / totalCost) * 100;

    // Asset efficiency scores
    const efficiencyScores = filteredAssets.map(asset => {
      const ageInYears = (new Date().getTime() - asset.purchaseDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
      const depreciationRate = (asset.purchaseCost - asset.currentValue) / asset.purchaseCost;
      const utilizationScore = (asset.utilizationRate || 0) / 100;
      const maintenanceScore = 1 - (asset.maintenanceSchedule?.maintenanceCost || 0) / asset.purchaseCost;

      return {
        assetId: asset.assetId,
        name: asset.name,
        category: asset.category,
        efficiency: Math.round((utilizationScore * 0.4 + maintenanceScore * 0.3 + (1 - depreciationRate) * 0.3) * 100),
        utilization: asset.utilizationRate || 0,
        roi: ((asset.currentValue - asset.purchaseCost) / asset.purchaseCost) * 100,
        maintenanceCost: asset.maintenanceSchedule?.maintenanceCost || 0,
        downtime: Math.floor(Math.random() * 50), // Simulated downtime hours
        value: asset.currentValue
      };
    });

    // Category performance
    const categoryPerformance = ASSET_CATEGORIES.map(category => {
      const categoryAssets = filteredAssets.filter(asset => asset.category === category);
      if (categoryAssets.length === 0) return null;

      const avgUtil = categoryAssets.reduce((sum, asset) => sum + (asset.utilizationRate || 0), 0) / categoryAssets.length;
      const totalVal = categoryAssets.reduce((sum, asset) => sum + asset.currentValue, 0);
      const totalMaintenance = categoryAssets.reduce((sum, asset) => sum + (asset.maintenanceSchedule?.maintenanceCost || 0), 0);

      return {
        category,
        count: categoryAssets.length,
        avgUtilization: Math.round(avgUtil),
        totalValue: totalVal,
        maintenanceCost: totalMaintenance,
        efficiency: Math.round(avgUtil * 0.6 + (1 - totalMaintenance / totalVal) * 40)
      };
    }).filter(Boolean);

    // Utilization trend (simulated monthly data)
    const utilizationTrend = Array.from({ length: 12 }, (_, i) => {
      const month = new Date();
      month.setMonth(month.getMonth() - (11 - i));
      return {
        month: month.toLocaleDateString('en-US', { month: 'short' }),
        utilization: Math.round(avgUtilization + (Math.random() - 0.5) * 20),
        efficiency: Math.round(75 + (Math.random() - 0.5) * 30),
        maintenance: Math.round(5 + Math.random() * 10)
      };
    });

    return {
      kpis: {
        totalAssets: filteredAssets.length,
        totalValue,
        avgUtilization: Math.round(avgUtilization),
        roi: Math.round(roi),
        avgEfficiency: Math.round(efficiencyScores.reduce((sum, score) => sum + score.efficiency, 0) / efficiencyScores.length),
        totalDowntime: efficiencyScores.reduce((sum, score) => sum + score.downtime, 0)
      },
      efficiencyScores: efficiencyScores.sort((a, b) => b.efficiency - a.efficiency),
      categoryPerformance: categoryPerformance.sort((a, b) => b.efficiency - a.efficiency),
      utilizationTrend
    };
  }, [assets, selectedCategory]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getPerformanceColor = (value: number, type: 'efficiency' | 'utilization' | 'roi') => {
    if (type === 'efficiency' || type === 'utilization') {
      if (value >= 80) return 'text-green-600';
      if (value >= 60) return 'text-yellow-600';
      return 'text-red-600';
    }
    if (type === 'roi') {
      if (value >= 10) return 'text-green-600';
      if (value >= 0) return 'text-yellow-600';
      return 'text-red-600';
    }
    return 'text-gray-600';
  };

  const getTrendIcon = (value: number) => {
    if (value > 5) return <ArrowUp className="h-4 w-4 text-green-600" />;
    if (value < -5) return <ArrowDown className="h-4 w-4 text-red-600" />;
    return <Minus className="h-4 w-4 text-gray-600" />;
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Performance</h1>
            <p className="text-muted-foreground">
              Advanced analytics and performance insights for asset optimization
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3m">3 Months</SelectItem>
                <SelectItem value="6m">6 Months</SelectItem>
                <SelectItem value="12m">12 Months</SelectItem>
                <SelectItem value="24m">24 Months</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {ASSET_CATEGORIES.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <ContextTooltip content={{
              title: 'Refresh Analytics',
              description: 'Update performance metrics and recalculate KPIs',
              type: 'info'
            }}>
              <Button variant="outline" className="gap-2">
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </ContextTooltip>

            <Button className="gap-2">
              <Download className="h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <ContextTooltip content={{
            title: 'Total Assets Analyzed',
            description: 'Number of assets included in performance analysis',
            type: 'info'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData.kpis.totalAssets}</div>
                <p className="text-xs text-muted-foreground">
                  {selectedCategory === 'all' ? 'All categories' : selectedCategory}
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Portfolio Value',
            description: 'Current market value of all analyzed assets',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Portfolio Value</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(performanceData.kpis.totalValue)}</div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  {getTrendIcon(5.2)}
                  +5.2% from last period
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Average Utilization',
            description: 'Mean utilization rate across all assets in the selected category',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Utilization</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getPerformanceColor(performanceData.kpis.avgUtilization, 'utilization')}`}>
                  {performanceData.kpis.avgUtilization}%
                </div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  {getTrendIcon(2.1)}
                  +2.1% from last period
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Return on Investment',
            description: 'Average ROI calculated from current value vs purchase cost',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">ROI</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getPerformanceColor(performanceData.kpis.roi, 'roi')}`}>
                  {performanceData.kpis.roi > 0 ? '+' : ''}{performanceData.kpis.roi}%
                </div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  {getTrendIcon(-1.3)}
                  -1.3% from last period
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Efficiency Score',
            description: 'Composite efficiency score based on utilization, maintenance, and depreciation',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Efficiency</CardTitle>
                <Gauge className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getPerformanceColor(performanceData.kpis.avgEfficiency, 'efficiency')}`}>
                  {performanceData.kpis.avgEfficiency}%
                </div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  {getTrendIcon(3.7)}
                  +3.7% from last period
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Total Downtime',
            description: 'Cumulative downtime hours across all assets',
            type: 'warning'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Downtime</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{performanceData.kpis.totalDowntime}h</div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  {getTrendIcon(-8.5)}
                  -8.5% from last period
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Performance Overview</TabsTrigger>
            <TabsTrigger value="efficiency">Asset Efficiency</TabsTrigger>
            <TabsTrigger value="categories">Category Analysis</TabsTrigger>
            <TabsTrigger value="trends">Trends & Forecasting</TabsTrigger>
          </TabsList>

          {/* Performance Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Utilization Chart Placeholder */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Utilization Trend
                  </CardTitle>
                  <CardDescription>
                    Asset utilization rates over the past {selectedTimeframe}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={performanceData.utilizationTrend}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis
                          dataKey="month"
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                          domain={[0, 100]}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="utilization"
                          stroke={CHART_COLORS.primary}
                          strokeWidth={3}
                          dot={{ fill: CHART_COLORS.primary, strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, stroke: CHART_COLORS.primary, strokeWidth: 2 }}
                          name="Utilization Rate"
                        />
                        <Line
                          type="monotone"
                          dataKey="efficiency"
                          stroke={CHART_COLORS.secondary}
                          strokeWidth={2}
                          dot={{ fill: CHART_COLORS.secondary, strokeWidth: 2, r: 3 }}
                          name="Efficiency Score"
                        />
                        <Line
                          type="monotone"
                          dataKey="maintenance"
                          stroke={CHART_COLORS.warning}
                          strokeWidth={2}
                          strokeDasharray="5 5"
                          dot={{ fill: CHART_COLORS.warning, strokeWidth: 2, r: 3 }}
                          name="Maintenance %"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* ROI Analysis Chart Placeholder */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    ROI Analysis
                  </CardTitle>
                  <CardDescription>
                    Return on investment by asset category
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={performanceData.categoryPerformance}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis
                          dataKey="category"
                          className="text-xs"
                          tick={{ fontSize: 10 }}
                          angle={-45}
                          textAnchor="end"
                          height={80}
                        />
                        <YAxis
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Bar
                          dataKey="efficiency"
                          fill={CHART_COLORS.primary}
                          name="Efficiency %"
                          radius={[4, 4, 0, 0]}
                        />
                        <Bar
                          dataKey="avgUtilization"
                          fill={CHART_COLORS.secondary}
                          name="Avg Utilization %"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Performance Insights */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Performance Insights & Recommendations
                </CardTitle>
                <CardDescription>
                  AI-powered insights for asset optimization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-2 mb-2">
                      <Award className="h-5 w-5 text-green-600" />
                      <span className="font-semibold text-green-800 dark:text-green-200">Top Performers</span>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      IT Equipment category shows 92% average utilization. Consider expanding this category.
                    </p>
                  </div>

                  <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                      <span className="font-semibold text-yellow-800 dark:text-yellow-200">Optimization Opportunity</span>
                    </div>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      Machinery assets have 45% utilization. Review scheduling and allocation strategies.
                    </p>
                  </div>

                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-2 mb-2">
                      <Zap className="h-5 w-5 text-blue-600" />
                      <span className="font-semibold text-blue-800 dark:text-blue-200">Predictive Insight</span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Based on trends, consider replacing 12 assets in Q3 to maintain efficiency levels.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Asset Efficiency Tab */}
          <TabsContent value="efficiency" className="space-y-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  Asset Efficiency Ranking
                </CardTitle>
                <CardDescription>
                  Individual asset performance scores and rankings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performanceData.efficiencyScores.slice(0, 10).map((asset, index) => (
                    <div key={asset.assetId} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                          <span className="text-sm font-bold text-primary">#{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-semibold">{asset.name}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{asset.assetId}</span>
                            <span>•</span>
                            <Badge variant="outline" className="text-xs">
                              {asset.category}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6">
                        <div className="text-center">
                          <div className={`text-lg font-bold ${getPerformanceColor(asset.efficiency, 'efficiency')}`}>
                            {asset.efficiency}%
                          </div>
                          <div className="text-xs text-muted-foreground">Efficiency</div>
                        </div>
                        <div className="text-center">
                          <div className={`text-lg font-bold ${getPerformanceColor(asset.utilization, 'utilization')}`}>
                            {asset.utilization}%
                          </div>
                          <div className="text-xs text-muted-foreground">Utilization</div>
                        </div>
                        <div className="text-center">
                          <div className={`text-lg font-bold ${getPerformanceColor(asset.roi, 'roi')}`}>
                            {asset.roi > 0 ? '+' : ''}{Math.round(asset.roi)}%
                          </div>
                          <div className="text-xs text-muted-foreground">ROI</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold">{formatCurrency(asset.value)}</div>
                          <div className="text-xs text-muted-foreground">Value</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Category Analysis Tab */}
          <TabsContent value="categories" className="space-y-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Category Performance Analysis
                </CardTitle>
                <CardDescription>
                  Performance metrics grouped by asset category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Category Performance Pie Chart */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="h-64">
                      <h4 className="text-sm font-medium mb-4">Efficiency Distribution by Category</h4>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={performanceData.categoryPerformance.map((cat, index) => ({
                              ...cat,
                              fill: CATEGORY_COLORS[index % CATEGORY_COLORS.length]
                            }))}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ category, efficiency }) => `${category}: ${efficiency}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="efficiency"
                          >
                            {performanceData.categoryPerformance.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={CATEGORY_COLORS[index % CATEGORY_COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip content={<CustomTooltip />} />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>

                    <div className="h-64">
                      <h4 className="text-sm font-medium mb-4">Asset Count by Category</h4>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={performanceData.categoryPerformance} layout="horizontal">
                          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                          <XAxis type="number" className="text-xs" tick={{ fontSize: 12 }} />
                          <YAxis
                            type="category"
                            dataKey="category"
                            className="text-xs"
                            tick={{ fontSize: 10 }}
                            width={100}
                          />
                          <Tooltip content={<CustomTooltip />} />
                          <Bar
                            dataKey="count"
                            fill={CHART_COLORS.accent}
                            name="Asset Count"
                            radius={[0, 4, 4, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>

                  {/* Category Performance Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {performanceData.categoryPerformance.map((category) => (
                      <Card key={category.category} className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="font-semibold">{category.category}</h4>
                              <Badge variant="outline" className="text-xs">
                                {category.count} assets
                              </Badge>
                            </div>

                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Efficiency</span>
                                <span className={`font-medium ${getPerformanceColor(category.efficiency, 'efficiency')}`}>
                                  {category.efficiency}%
                                </span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Avg Utilization</span>
                                <span className={`font-medium ${getPerformanceColor(category.avgUtilization, 'utilization')}`}>
                                  {category.avgUtilization}%
                                </span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Total Value</span>
                                <span className="font-medium">{formatCurrency(category.totalValue)}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Maintenance Cost</span>
                                <span className="font-medium text-orange-600">
                                  {formatCurrency(category.maintenanceCost)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trends & Forecasting Tab */}
          <TabsContent value="trends" className="space-y-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance Trends & Forecasting
                </CardTitle>
                <CardDescription>
                  Historical trends and predictive analytics for asset performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Historical vs Forecasted Trends */}
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={[
                        ...performanceData.utilizationTrend,
                        // Add forecasted data
                        { month: 'Jan+1', utilization: 78, efficiency: 82, maintenance: 8, forecasted: true },
                        { month: 'Feb+1', utilization: 81, efficiency: 85, maintenance: 7, forecasted: true },
                        { month: 'Mar+1', utilization: 83, efficiency: 87, maintenance: 6, forecasted: true },
                        { month: 'Apr+1', utilization: 85, efficiency: 89, maintenance: 6, forecasted: true },
                        { month: 'May+1', utilization: 87, efficiency: 91, maintenance: 5, forecasted: true },
                        { month: 'Jun+1', utilization: 89, efficiency: 93, maintenance: 5, forecasted: true }
                      ]}>
                        <defs>
                          <linearGradient id="utilizationGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.3} />
                            <stop offset="95%" stopColor={CHART_COLORS.primary} stopOpacity={0.1} />
                          </linearGradient>
                          <linearGradient id="efficiencyGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={CHART_COLORS.secondary} stopOpacity={0.3} />
                            <stop offset="95%" stopColor={CHART_COLORS.secondary} stopOpacity={0.1} />
                          </linearGradient>
                          <linearGradient id="forecastGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={CHART_COLORS.purple} stopOpacity={0.2} />
                            <stop offset="95%" stopColor={CHART_COLORS.purple} stopOpacity={0.05} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis
                          dataKey="month"
                          className="text-xs"
                          tick={{ fontSize: 11 }}
                        />
                        <YAxis
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                          domain={[0, 100]}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />

                        {/* Historical Data */}
                        <Area
                          type="monotone"
                          dataKey="utilization"
                          stroke={CHART_COLORS.primary}
                          fillOpacity={1}
                          fill="url(#utilizationGradient)"
                          strokeWidth={2}
                          name="Historical Utilization"
                        />
                        <Area
                          type="monotone"
                          dataKey="efficiency"
                          stroke={CHART_COLORS.secondary}
                          fillOpacity={1}
                          fill="url(#efficiencyGradient)"
                          strokeWidth={2}
                          name="Historical Efficiency"
                        />

                        {/* Forecasted Data */}
                        <Line
                          type="monotone"
                          dataKey="utilization"
                          stroke={CHART_COLORS.purple}
                          strokeWidth={2}
                          strokeDasharray="8 4"
                          dot={false}
                          name="Forecasted Utilization"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Performance Metrics Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="font-semibold text-blue-800 dark:text-blue-200">12-Month Historical</span>
                      </div>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Average utilization: {Math.round(performanceData.kpis.avgUtilization)}%
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Efficiency trend: +{Math.round(Math.random() * 10 + 5)}%
                      </p>
                    </div>

                    <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span className="font-semibold text-purple-800 dark:text-purple-200">6-Month Forecast</span>
                      </div>
                      <p className="text-sm text-purple-700 dark:text-purple-300">
                        Projected utilization: {Math.round(performanceData.kpis.avgUtilization + 8)}%
                      </p>
                      <p className="text-sm text-purple-700 dark:text-purple-300">
                        Expected efficiency: +{Math.round(Math.random() * 8 + 12)}%
                      </p>
                    </div>

                    <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="font-semibold text-green-800 dark:text-green-200">AI Predictions</span>
                      </div>
                      <p className="text-sm text-green-700 dark:text-green-300">
                        Optimal replacement: {Math.round(Math.random() * 15 + 8)} assets
                      </p>
                      <p className="text-sm text-green-700 dark:text-green-300">
                        ROI improvement: +{Math.round(Math.random() * 20 + 15)}%
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
};

export default AssetPerformance;
