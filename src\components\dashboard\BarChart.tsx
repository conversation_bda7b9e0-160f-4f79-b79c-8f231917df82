
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>, Bar<PERSON>hart as RechartsBar<PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, <PERSON>Axis, <PERSON>Axis } from "recharts";
import { cn } from "@/lib/utils";

interface BarChartProps {
  title: string;
  description?: string;
  data: Array<{
    name: string;
    category?: string;
    value?: number;
    [key: string]: number | string | undefined;
  }>;
  categories: Array<{
    name: string;
    color: string;
  }>;
  className?: string;
  height?: number;
}

export function BarChart({
  title,
  description,
  data,
  categories,
  className,
  height = 300,
}: BarChartProps) {
  const [chartData, setChartData] = useState<Array<{
    name: string;
    category?: string;
    value?: number;
    [key: string]: number | string | undefined;
  }>>(data.map(item => ({ ...item })));
  
  useEffect(() => {
    // Animate data loading
    const animateData = () => {
      setChartData(data);
    };
    
    const timer = setTimeout(animateData, 300);
    return () => clearTimeout(timer);
  }, [data]);
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <RechartsBarChart
            data={chartData}
            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.2} vertical={false} />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
            />
            <Tooltip />
            {categories.map((category) => (
              <Bar
                key={category.name}
                dataKey={category.name}
                fill={category.color}
                radius={[4, 4, 0, 0]}
              />
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}
