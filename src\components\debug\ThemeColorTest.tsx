import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useDynamicColors, useThemeColors } from '@/hooks/use-theme-colors';
import { Palette, Package, TrendingUp, CheckCircle, AlertTriangle } from 'lucide-react';

export const ThemeColorTest: React.FC = () => {
  const dynamicColors = useDynamicColors();
  const { currentColor, currentGradient } = useThemeColors();

  return (
    <div className="p-6 space-y-6">
      <Card className="glass-theme">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" style={dynamicColors.textPrimary} />
            Theme Color Test Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Theme Info */}
          <div className="p-4 rounded-lg border" style={dynamicColors.bgPrimary(0.05)}>
            <h3 className="font-semibold mb-2" style={dynamicColors.textPrimary}>
              Current Theme: {currentColor.name}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Light:</span>
                <div 
                  className="w-full h-6 rounded mt-1 border"
                  style={{ backgroundColor: `hsl(${currentColor.variants.light})` }}
                />
              </div>
              <div>
                <span className="text-muted-foreground">Medium:</span>
                <div 
                  className="w-full h-6 rounded mt-1 border"
                  style={{ backgroundColor: `hsl(${currentColor.variants.medium})` }}
                />
              </div>
              <div>
                <span className="text-muted-foreground">Dark:</span>
                <div 
                  className="w-full h-6 rounded mt-1 border"
                  style={{ backgroundColor: `hsl(${currentColor.variants.dark})` }}
                />
              </div>
              <div>
                <span className="text-muted-foreground">Subtle:</span>
                <div 
                  className="w-full h-6 rounded mt-1 border"
                  style={{ backgroundColor: `hsl(${currentColor.variants.subtle})` }}
                />
              </div>
            </div>
            {currentGradient && (
              <div className="mt-4">
                <span className="text-muted-foreground">Gradient: {currentGradient.name}</span>
                <div 
                  className="w-full h-6 rounded mt-1 border"
                  style={dynamicColors.gradientPrimary}
                />
              </div>
            )}
          </div>

          {/* Test Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Dynamic Icon</CardTitle>
                <Package className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold" style={dynamicColors.textPrimary}>1,234</div>
                <p className="text-xs text-muted-foreground">Theme colored</p>
              </CardContent>
            </Card>

            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Trending</CardTitle>
                <TrendingUp className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold" style={dynamicColors.textPrimary}>$45.2K</div>
                <p className="text-xs text-muted-foreground">Dynamic value</p>
              </CardContent>
            </Card>

            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
                <CheckCircle className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold" style={dynamicColors.textPrimary}>98%</div>
                <p className="text-xs text-muted-foreground">Active rate</p>
              </CardContent>
            </Card>

            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold" style={dynamicColors.textPrimary}>3</div>
                <p className="text-xs text-muted-foreground">Pending</p>
              </CardContent>
            </Card>
          </div>

          {/* Test Buttons */}
          <div className="space-y-4">
            <h3 className="font-semibold">Dynamic Buttons</h3>
            <div className="flex flex-wrap gap-4">
              <Button 
                className="border"
                style={{
                  ...dynamicColors.bgPrimary(0.2),
                  ...dynamicColors.borderPrimary(0.3),
                  ...dynamicColors.textPrimary
                }}
              >
                Primary Button
              </Button>
              
              <Button 
                variant="outline"
                className="border"
                style={dynamicColors.borderPrimary(0.3)}
              >
                <span style={dynamicColors.textPrimary}>Outline Button</span>
              </Button>
              
              <Button 
                className="border"
                style={{
                  ...dynamicColors.bgPrimaryLight(0.1),
                  ...dynamicColors.borderPrimary(0.2),
                  ...dynamicColors.textPrimary
                }}
              >
                Light Button
              </Button>
            </div>
          </div>

          {/* Test Badges */}
          <div className="space-y-4">
            <h3 className="font-semibold">Dynamic Badges</h3>
            <div className="flex flex-wrap gap-2">
              <Badge 
                className="border"
                style={{
                  ...dynamicColors.bgPrimary(0.15),
                  ...dynamicColors.borderPrimary(0.3),
                  ...dynamicColors.textPrimary
                }}
              >
                Theme Badge
              </Badge>
              
              <Badge 
                variant="outline"
                className="border"
                style={dynamicColors.borderPrimary(0.3)}
              >
                <span style={dynamicColors.textPrimary}>Outline Badge</span>
              </Badge>
              
              <Badge 
                className="border"
                style={{
                  ...dynamicColors.bgPrimarySubtle(0.8),
                  ...dynamicColors.borderPrimary(0.2),
                  ...dynamicColors.textPrimary
                }}
              >
                Subtle Badge
              </Badge>
            </div>
          </div>

          {/* Gradient Test */}
          <div className="space-y-4">
            <h3 className="font-semibold">Gradient Effects</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                className="p-6 rounded-lg border text-center"
                style={dynamicColors.gradientPrimary}
              >
                <h4 className="font-semibold text-white">Primary Gradient</h4>
                <p className="text-white/80 text-sm">Dynamic gradient background</p>
              </div>
              
              <div 
                className="p-6 rounded-lg border text-center"
                style={dynamicColors.gradientSubtle}
              >
                <h4 className="font-semibold" style={dynamicColors.textPrimary}>Subtle Gradient</h4>
                <p className="text-muted-foreground text-sm">Light gradient effect</p>
              </div>
            </div>
          </div>

          {/* CSS Variables Test */}
          <div className="space-y-4">
            <h3 className="font-semibold">CSS Variables Test</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-mono">
              <div className="p-3 bg-muted/20 rounded">
                <div>--primary: <span className="text-theme-primary">{currentColor.value}</span></div>
                <div>--primary-light: <span className="text-theme-primary">{currentColor.variants.light}</span></div>
                <div>--primary-dark: <span className="text-theme-primary">{currentColor.variants.dark}</span></div>
                <div>--primary-subtle: <span className="text-theme-primary">{currentColor.variants.subtle}</span></div>
              </div>
              
              <div className="p-3 bg-muted/20 rounded">
                <div className="bg-theme-primary-5 p-2 rounded mb-2">bg-theme-primary-5</div>
                <div className="bg-theme-primary-10 p-2 rounded mb-2">bg-theme-primary-10</div>
                <div className="bg-theme-primary-20 p-2 rounded mb-2">bg-theme-primary-20</div>
                <div className="text-theme-primary">text-theme-primary</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
