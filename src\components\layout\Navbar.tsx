
import { But<PERSON> } from "@/components/ui/button";
import { Menu, <PERSON>, <PERSON>, <PERSON>, User, Search, Settings, LogOut } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface NavbarProps {
  toggleSidebar: () => void;
}

export function Navbar({ toggleSidebar }: NavbarProps) {
  const { theme, setTheme } = useTheme();
  
  return (
    <header className="h-16 border-b border-border bg-background/95 backdrop-blur-sm z-20 sticky top-0">
      <div className="flex items-center justify-between h-full px-4 lg:px-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className="hover:bg-accent">
            <Menu className="h-5 w-5" />
          </Button>
          
          <div className="hidden md:flex items-center gap-3">
            <h1 className="text-xl font-bold gradient-text">
              Toshka Financial Insight Hub
            </h1>
            <Badge variant="secondary" className="text-xs">
              Enterprise
            </Badge>
          </div>
        </div>

        {/* Search Bar */}
        <div className="hidden lg:flex items-center flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="Search anything..." 
              className="pl-10 bg-muted/50 border-0 focus:bg-background transition-colors"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Search Button for Mobile */}
          <Button variant="ghost" size="icon" className="lg:hidden">
            <Search className="h-5 w-5" />
          </Button>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500">
                  3
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <div className="p-3 border-b">
                <h4 className="font-semibold">Notifications</h4>
                <p className="text-sm text-muted-foreground">You have 3 unread messages</p>
              </div>
              <div className="p-2">
                <div className="p-3 hover:bg-accent rounded-md cursor-pointer">
                  <div className="font-medium text-sm">System Update Available</div>
                  <div className="text-xs text-muted-foreground">Version 2.1.1 is ready to install</div>
                  <div className="text-xs text-muted-foreground mt-1">2 minutes ago</div>
                </div>
                <div className="p-3 hover:bg-accent rounded-md cursor-pointer">
                  <div className="font-medium text-sm">Maintenance Scheduled</div>
                  <div className="text-xs text-muted-foreground">Equipment PM-001 due tomorrow</div>
                  <div className="text-xs text-muted-foreground mt-1">1 hour ago</div>
                </div>
                <div className="p-3 hover:bg-accent rounded-md cursor-pointer">
                  <div className="font-medium text-sm">Budget Alert</div>
                  <div className="text-xs text-muted-foreground">Q4 budget 85% utilized</div>
                  <div className="text-xs text-muted-foreground mt-1">3 hours ago</div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Theme Toggle */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setTheme('light')}>
                <Sun className="h-4 w-4 mr-2" />
                Light
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('dark')}>
                <Moon className="h-4 w-4 mr-2" />
                Dark
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('system')}>
                <Settings className="h-4 w-4 mr-2" />
                System
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="p-3 border-b">
                <div className="font-semibold">John Doe</div>
                <div className="text-sm text-muted-foreground"><EMAIL></div>
                <Badge variant="outline" className="mt-1 text-xs">Administrator</Badge>
              </div>
              <DropdownMenuItem>
                <User className="h-4 w-4 mr-2" />
                Profile Settings
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Preferences
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
