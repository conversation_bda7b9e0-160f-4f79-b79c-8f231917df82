
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>u, Bell, User, Search, Settings, LogOut, Command, Zap } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ThemeSwitcher } from "@/components/ui/ThemeSwitcher";
import { useDynamicColors } from "@/hooks/use-theme-colors";
// import { useCommandPalette } from "@/hooks/use-command-palette";
// import { useNotifications } from "@/components/ui/NotificationSystem";

interface NavbarProps {
  toggleSidebar: () => void;
}

export function Navbar({ toggleSidebar }: NavbarProps) {
  const { theme } = useTheme();
  const dynamicColors = useDynamicColors();
  // const { open: openCommandPalette } = useCommandPalette();
  // const { notifications } = useNotifications();

  // const unreadCount = notifications.length;
  const unreadCount = 0; // Temporary placeholder
  const openCommandPalette = () => console.log('Command palette clicked'); // Temporary placeholder

  return (
    <header className="h-16 header-enhanced z-20 sticky top-0">
      <div className="flex items-center justify-between h-full px-4 lg:px-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={toggleSidebar} className="hover:bg-accent">
            <Menu className="h-5 w-5" />
          </Button>

          <div className="hidden md:flex items-center gap-3">
            <h1 className="text-xl font-bold header-logo">
              Toshka Financial Insight Hub
            </h1>
            <Badge
              variant="secondary"
              className="text-xs border"
              style={{
                ...dynamicColors.bgPrimary(0.1),
                ...dynamicColors.textPrimary,
                ...dynamicColors.borderPrimary(0.2)
              }}
            >
              Enterprise
            </Badge>
          </div>
        </div>

        {/* Enhanced Search Bar */}
        <div className="hidden lg:flex items-center flex-1 max-w-md mx-8">
          <div className="relative w-full group header-search rounded-lg">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
            <Input
              placeholder="Search anything... (Ctrl+K for command palette)"
              className="pl-10 pr-12 bg-transparent border-0 focus:bg-transparent transition-all duration-200 focus:ring-0"
              onClick={openCommandPalette}
              readOnly
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Badge variant="outline" className="text-xs px-2 py-1 bg-background/50 border-primary/30">
                <Command className="h-3 w-3 mr-1" />
                K
              </Badge>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Command Palette Button for Mobile */}
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden header-button rounded-lg"
            onClick={openCommandPalette}
          >
            <Command className="h-5 w-5" />
          </Button>

          {/* Quick Actions */}
          <div className="hidden xl:flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={openCommandPalette}
              className="gap-2 header-button rounded-lg px-3"
            >
              <Zap className="h-4 w-4" />
              <span className="text-sm">Quick Actions</span>
            </Button>
          </div>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative header-button rounded-lg">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs header-notification-badge">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <div className="p-3 border-b bg-gradient-to-r from-primary/10 to-accent/10">
                <h4 className="font-semibold flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  Notifications
                </h4>
                <p className="text-sm text-muted-foreground">
                  {unreadCount > 0 ? `You have ${unreadCount} unread messages` : 'All caught up!'}
                </p>
              </div>
              <div className="p-2 max-h-80 overflow-y-auto">
                <div className="p-6 text-center text-muted-foreground">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No new notifications</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Notification system will be available soon
                  </p>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Advanced Theme Switcher */}
          <ThemeSwitcher />

          {/* Enhanced User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative p-1">
                <div className="w-8 h-8 header-user-avatar rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 header-status-indicator rounded-full border-2 border-background"></div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64">
              <div className="p-4 border-b bg-gradient-to-r from-primary/10 to-accent/10">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold">John Doe</div>
                    <div className="text-sm text-muted-foreground"><EMAIL></div>
                  </div>
                </div>
                <div className="flex gap-2 mt-3">
                  <Badge variant="outline" className="text-xs">Administrator</Badge>
                  <Badge variant="secondary" className="text-xs">Online</Badge>
                </div>
              </div>
              <div className="p-2">
                <DropdownMenuItem className="cursor-pointer hover:bg-accent/50 transition-colors">
                  <User className="h-4 w-4 mr-3" />
                  <div>
                    <div className="font-medium">Profile Settings</div>
                    <div className="text-xs text-muted-foreground">Manage your account</div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer hover:bg-accent/50 transition-colors">
                  <Settings className="h-4 w-4 mr-3" />
                  <div>
                    <div className="font-medium">Preferences</div>
                    <div className="text-xs text-muted-foreground">Customize your experience</div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600 cursor-pointer hover:bg-red-50 dark:hover:bg-red-950/50 transition-colors">
                  <LogOut className="h-4 w-4 mr-3" />
                  <div>
                    <div className="font-medium">Sign Out</div>
                    <div className="text-xs text-muted-foreground">End your session</div>
                  </div>
                </DropdownMenuItem>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
