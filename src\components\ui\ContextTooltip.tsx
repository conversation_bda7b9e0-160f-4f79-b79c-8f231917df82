import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { 
  Info, 
  HelpCircle, 
  AlertTriangle, 
  CheckCircle, 
  Lightbulb,
  Zap,
  TrendingUp,
  Shield,
  Clock
} from 'lucide-react';

interface TooltipContent {
  title?: string;
  description: string;
  type?: 'info' | 'warning' | 'success' | 'tip' | 'performance' | 'security' | 'time';
  action?: {
    label: string;
    onClick: () => void;
  };
  shortcut?: string;
  context?: string;
}

interface ContextTooltipProps {
  children: React.ReactNode;
  content: TooltipContent;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  disabled?: boolean;
  trigger?: 'hover' | 'click' | 'focus';
  className?: string;
}

const typeConfig = {
  info: { icon: Info, color: 'text-blue-500', bg: 'bg-blue-50 dark:bg-blue-950/50', border: 'border-blue-200 dark:border-blue-800' },
  warning: { icon: AlertTriangle, color: 'text-yellow-500', bg: 'bg-yellow-50 dark:bg-yellow-950/50', border: 'border-yellow-200 dark:border-yellow-800' },
  success: { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-50 dark:bg-green-950/50', border: 'border-green-200 dark:border-green-800' },
  tip: { icon: Lightbulb, color: 'text-purple-500', bg: 'bg-purple-50 dark:bg-purple-950/50', border: 'border-purple-200 dark:border-purple-800' },
  performance: { icon: TrendingUp, color: 'text-emerald-500', bg: 'bg-emerald-50 dark:bg-emerald-950/50', border: 'border-emerald-200 dark:border-emerald-800' },
  security: { icon: Shield, color: 'text-red-500', bg: 'bg-red-50 dark:bg-red-950/50', border: 'border-red-200 dark:border-red-800' },
  time: { icon: Clock, color: 'text-indigo-500', bg: 'bg-indigo-50 dark:bg-indigo-950/50', border: 'border-indigo-200 dark:border-indigo-800' },
};

export const ContextTooltip: React.FC<ContextTooltipProps> = ({
  children,
  content,
  placement = 'top',
  delay = 500,
  disabled = false,
  trigger = 'hover',
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const config = typeConfig[content.type || 'info'];
  const IconComponent = config.icon;

  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    let x = 0;
    let y = 0;

    switch (placement) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
        y = triggerRect.top - tooltipRect.height - 8;
        break;
      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
        y = triggerRect.bottom + 8;
        break;
      case 'left':
        x = triggerRect.left - tooltipRect.width - 8;
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
        break;
      case 'right':
        x = triggerRect.right + 8;
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
        break;
    }

    // Adjust for viewport boundaries
    if (x < 8) x = 8;
    if (x + tooltipRect.width > viewport.width - 8) {
      x = viewport.width - tooltipRect.width - 8;
    }
    if (y < 8) y = 8;
    if (y + tooltipRect.height > viewport.height - 8) {
      y = viewport.height - tooltipRect.height - 8;
    }

    setPosition({ x, y });
  };

  const showTooltip = () => {
    if (disabled) return;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const toggleTooltip = () => {
    if (isVisible) {
      hideTooltip();
    } else {
      showTooltip();
    }
  };

  useEffect(() => {
    if (isVisible) {
      calculatePosition();
      
      const handleResize = () => calculatePosition();
      const handleScroll = () => calculatePosition();
      
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, true);
      
      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [isVisible, placement]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleTriggerEvents = () => {
    const events: Record<string, () => void> = {};

    if (trigger === 'hover') {
      events.onMouseEnter = showTooltip;
      events.onMouseLeave = hideTooltip;
    } else if (trigger === 'click') {
      events.onClick = toggleTooltip;
    } else if (trigger === 'focus') {
      events.onFocus = showTooltip;
      events.onBlur = hideTooltip;
    }

    return events;
  };

  const tooltip = isVisible && (
    <div
      ref={tooltipRef}
      className={`fixed z-50 max-w-sm animate-fade-in ${config.bg} ${config.border} border backdrop-blur-sm rounded-lg shadow-lg overflow-hidden`}
      style={{
        left: position.x,
        top: position.y,
      }}
    >
      {/* Header */}
      {content.title && (
        <div className="flex items-center gap-2 p-3 border-b border-border/50">
          <IconComponent className={`h-4 w-4 ${config.color}`} />
          <span className="font-semibold text-sm">{content.title}</span>
          {content.context && (
            <span className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded">
              {content.context}
            </span>
          )}
        </div>
      )}

      {/* Content */}
      <div className="p-3">
        {!content.title && (
          <div className="flex items-start gap-2 mb-2">
            <IconComponent className={`h-4 w-4 ${config.color} mt-0.5 flex-shrink-0`} />
            <div className="flex-1">
              <p className="text-sm text-foreground leading-relaxed">{content.description}</p>
            </div>
          </div>
        )}
        
        {content.title && (
          <p className="text-sm text-foreground leading-relaxed mb-3">{content.description}</p>
        )}

        {/* Action and Shortcut */}
        {(content.action || content.shortcut) && (
          <div className="flex items-center justify-between gap-2 mt-3 pt-2 border-t border-border/50">
            {content.action && (
              <button
                onClick={content.action.onClick}
                className="text-xs font-medium text-primary hover:text-primary/80 transition-colors"
              >
                {content.action.label}
              </button>
            )}
            {content.shortcut && (
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground font-mono bg-muted/50 px-1.5 py-0.5 rounded">
                  {content.shortcut}
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Arrow */}
      <div
        className={`absolute w-2 h-2 ${config.bg} ${config.border} border transform rotate-45 ${
          placement === 'top' ? 'bottom-[-5px] left-1/2 -translate-x-1/2 border-t-0 border-l-0' :
          placement === 'bottom' ? 'top-[-5px] left-1/2 -translate-x-1/2 border-b-0 border-r-0' :
          placement === 'left' ? 'right-[-5px] top-1/2 -translate-y-1/2 border-l-0 border-b-0' :
          'left-[-5px] top-1/2 -translate-y-1/2 border-r-0 border-t-0'
        }`}
      />
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        className={`inline-block ${className}`}
        {...handleTriggerEvents()}
      >
        {children}
      </div>
      {tooltip && createPortal(tooltip, document.body)}
    </>
  );
};

// Predefined tooltip content for common use cases
export const tooltipContent = {
  financial: {
    revenue: {
      title: 'Revenue Tracking',
      description: 'Monitor total income from all business operations. This includes sales, services, and other revenue streams.',
      type: 'performance' as const,
      context: 'Financial'
    },
    expenses: {
      title: 'Expense Management',
      description: 'Track operational costs including maintenance, salaries, utilities, and other business expenses.',
      type: 'info' as const,
      context: 'Financial'
    },
    profit: {
      title: 'Profit Analysis',
      description: 'Net profit calculated as revenue minus total expenses. Key indicator of business performance.',
      type: 'success' as const,
      context: 'Financial'
    }
  },
  assets: {
    utilization: {
      title: 'Asset Utilization',
      description: 'Percentage of time assets are actively used in operations. Higher utilization indicates better efficiency.',
      type: 'performance' as const,
      context: 'Assets'
    },
    maintenance: {
      title: 'Maintenance Status',
      description: 'Current maintenance state of equipment. Regular maintenance prevents costly breakdowns.',
      type: 'warning' as const,
      context: 'Assets'
    }
  },
  safety: {
    incidents: {
      title: 'Safety Incidents',
      description: 'Track workplace incidents to maintain safety standards and compliance with regulations.',
      type: 'security' as const,
      context: 'HSE'
    },
    compliance: {
      title: 'Compliance Score',
      description: 'Adherence to safety regulations and industry standards. Critical for operational licensing.',
      type: 'security' as const,
      context: 'HSE'
    }
  },
  performance: {
    efficiency: {
      title: 'Operational Efficiency',
      description: 'Measure of how effectively resources are used to achieve business objectives.',
      type: 'performance' as const,
      context: 'Operations'
    },
    kpi: {
      title: 'Key Performance Indicator',
      description: 'Critical metrics that measure progress toward strategic business goals.',
      type: 'tip' as const,
      context: 'Analytics'
    }
  }
};

// Helper component for quick tooltips
export const QuickTooltip: React.FC<{
  children: React.ReactNode;
  text: string;
  type?: TooltipContent['type'];
}> = ({ children, text, type = 'info' }) => (
  <ContextTooltip
    content={{ description: text, type }}
    placement="top"
    delay={300}
  >
    {children}
  </ContextTooltip>
);
