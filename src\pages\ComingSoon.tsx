import { useNavigate, useLocation } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Construction, 
  Calendar, 
  Zap, 
  Star,
  Clock,
  Rocket,
  CheckCircle
} from "lucide-react";

interface ComingSoonProps {
  moduleName?: string;
  description?: string;
  features?: string[];
  estimatedDate?: string;
}

const ComingSoon = ({ 
  moduleName, 
  description, 
  features = [], 
  estimatedDate = "Q2 2024" 
}: ComingSoonProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Extract module name from URL if not provided
  const getModuleFromPath = (path: string) => {
    const segments = path.split('/').filter(Boolean);
    if (segments.length >= 2) {
      const module = segments[0];
      const submodule = segments[1];
      return `${module.charAt(0).toUpperCase() + module.slice(1)} - ${submodule.charAt(0).toUpperCase() + submodule.slice(1)}`;
    }
    return segments[0]?.charAt(0).toUpperCase() + segments[0]?.slice(1) || 'Module';
  };

  const currentModule = moduleName || getModuleFromPath(location.pathname);
  
  const defaultFeatures = [
    "Real-time data synchronization",
    "Advanced analytics and reporting", 
    "Mobile-responsive interface",
    "Role-based access control",
    "Integration with existing systems",
    "Automated workflows and notifications"
  ];

  const moduleFeatures = features.length > 0 ? features : defaultFeatures;

  const moduleDescriptions: Record<string, string> = {
    'assets': 'Comprehensive asset lifecycle management with tracking, maintenance scheduling, and performance analytics.',
    'maintenance': 'Complete CMMS solution with work order management, preventive maintenance, and spare parts inventory.',
    'hr': 'Human resources management including employee records, payroll, attendance, and performance tracking.',
    'inventory': 'Advanced inventory management with stock control, procurement, warehouse management, and supplier relations.',
    'projects': 'Project management tools with planning, resource allocation, timeline tracking, and collaboration features.',
    'fleet': 'Fleet management system for vehicle tracking, maintenance scheduling, fuel management, and driver oversight.',
    'hse': 'Health, Safety, and Environment management with incident tracking, compliance monitoring, and safety training.',
    'engineering': 'Engineering management tools for technical documentation, design workflows, and project coordination.',
    'analytics': 'Advanced analytics and reporting platform with customizable dashboards and business intelligence.'
  };

  const getModuleDescription = () => {
    const pathSegment = location.pathname.split('/')[1];
    return description || moduleDescriptions[pathSegment] || 'Advanced enterprise management capabilities designed to streamline your business operations.';
  };

  return (
    <AppLayout>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/')}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>

        {/* Main Content */}
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-primary/20 to-accent/20 rounded-full flex items-center justify-center mb-6">
              <Construction className="h-12 w-12 text-primary animate-pulse" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
              <Star className="h-4 w-4 text-white" />
            </div>
          </div>

          <div className="space-y-4">
            <Badge variant="secondary" className="px-4 py-2 text-sm">
              <Clock className="h-4 w-4 mr-2" />
              Coming Soon
            </Badge>
            
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              {currentModule}
            </h1>
            
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              {getModuleDescription()}
            </p>
          </div>
        </div>

        {/* Features Preview */}
        <Card className="glass-card">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Rocket className="h-5 w-5 text-primary" />
              Planned Features
            </CardTitle>
            <CardDescription>
              Here's what you can expect when this module launches
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {moduleFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-accent/30 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span className="text-sm font-medium">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card className="border-primary/20">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              Development Timeline
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full">
              <Zap className="h-4 w-4" />
              <span className="font-semibold">Estimated Release: {estimatedDate}</span>
            </div>
            <p className="text-sm text-muted-foreground">
              We're working hard to bring you this feature. Stay tuned for updates!
            </p>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            onClick={() => navigate('/financial')}
            className="gap-2"
          >
            <Zap className="h-4 w-4" />
            Explore Available Features
          </Button>
          <Button 
            variant="outline"
            onClick={() => navigate('/')}
            className="gap-2"
          >
            Return to Dashboard
          </Button>
        </div>

        {/* Status Footer */}
        <div className="text-center pt-8 border-t border-border">
          <p className="text-sm text-muted-foreground">
            This module is currently under development. For immediate assistance or questions,
            please contact our support team.
          </p>
        </div>
      </div>
    </AppLayout>
  );
};

export default ComingSoon;
