import React, { useState } from 'react';
import { 
  Settings, 
  Palette, 
  Layout, 
  Bell, 
  Globe, 
  Shield, 
  Download, 
  Upload,
  RotateCcw,
  Save,
  X,
  Monitor,
  Sun,
  Moon,
  Volume2,
  VolumeX,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from './button';
import { Switch } from './switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Slider } from './slider';
import { Label } from './label';
import { Separator } from './separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { usePreferences } from '@/hooks/use-preferences';
import { useTheme } from '@/hooks/use-theme';

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'appearance' | 'layout' | 'notifications' | 'data' | 'advanced'>('appearance');
  const { preferences, updatePreferences, resetPreferences, exportPreferences, importPreferences } = usePreferences();
  const { theme, setTheme } = useTheme();

  const handleExport = () => {
    const data = exportPreferences();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'toshka-preferences.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target?.result as string;
        if (importPreferences(data)) {
          // Success feedback
          console.log('Preferences imported successfully');
        } else {
          // Error feedback
          console.error('Failed to import preferences');
        }
      };
      reader.readAsText(file);
    }
  };

  if (!isOpen) return null;

  const tabs = [
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'layout', label: 'Layout', icon: Layout },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'data', label: 'Data & Display', icon: Globe },
    { id: 'advanced', label: 'Advanced', icon: Shield },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 frosted-overlay" onClick={onClose} />
      
      {/* Settings Panel */}
      <div className="relative w-full max-w-4xl mx-4 h-[80vh] glass-card backdrop-blur-strong border border-border/20 rounded-xl shadow-2xl animate-scale-in overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-border/10 bg-gradient-to-r from-primary/5 to-accent-cyan/5">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Settings className="w-6 h-6 text-primary" />
              <div>
                <h2 className="text-2xl font-bold">Settings & Preferences</h2>
                <p className="text-sm text-muted-foreground">Customize your experience</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 border-r border-border/10 bg-muted/20 p-4">
            <div className="space-y-1">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-all duration-200 ${
                    activeTab === tab.id 
                      ? 'bg-primary text-primary-foreground shadow-sm' 
                      : 'hover:bg-accent/50'
                  }`}
                  onClick={() => setActiveTab(tab.id as any)}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.label}
                </button>
              ))}
            </div>

            <Separator className="my-4" />

            {/* Quick Actions */}
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export Settings
              </Button>
              <div className="relative">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImport}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Upload className="w-4 h-4 mr-2" />
                  Import Settings
                </Button>
              </div>
              <Button variant="outline" size="sm" className="w-full justify-start" onClick={resetPreferences}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset to Default
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      Theme & Colors
                    </CardTitle>
                    <CardDescription>Customize the visual appearance</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Theme Mode</Label>
                      <div className="grid grid-cols-3 gap-2 mt-2">
                        {[
                          { value: 'light', label: 'Light', icon: Sun },
                          { value: 'dark', label: 'Dark', icon: Moon },
                          { value: 'system', label: 'System', icon: Monitor }
                        ].map(option => (
                          <button
                            key={option.value}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 flex flex-col items-center gap-2 ${
                              theme === option.value 
                                ? 'border-primary bg-primary/10 text-primary' 
                                : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => setTheme(option.value as any)}
                          >
                            <option.icon className="w-5 h-5" />
                            <span className="text-sm font-medium">{option.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label>Font Size</Label>
                      <Select 
                        value={preferences.fontSize} 
                        onValueChange={(value: any) => updatePreferences({ fontSize: value })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Reduce Motion</Label>
                        <p className="text-sm text-muted-foreground">Minimize animations for accessibility</p>
                      </div>
                      <Switch 
                        checked={preferences.reducedMotion}
                        onCheckedChange={(checked) => updatePreferences({ reducedMotion: checked })}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'layout' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Layout className="w-5 h-5" />
                      Layout & Navigation
                    </CardTitle>
                    <CardDescription>Configure the interface layout</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Compact Mode</Label>
                        <p className="text-sm text-muted-foreground">Reduce spacing and padding</p>
                      </div>
                      <Switch 
                        checked={preferences.compactMode}
                        onCheckedChange={(checked) => updatePreferences({ compactMode: checked })}
                      />
                    </div>

                    <div>
                      <Label>Grid Density</Label>
                      <Select 
                        value={preferences.gridDensity} 
                        onValueChange={(value: any) => updatePreferences({ gridDensity: value })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="compact">Compact</SelectItem>
                          <SelectItem value="comfortable">Comfortable</SelectItem>
                          <SelectItem value="spacious">Spacious</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Dashboard Layout</Label>
                      <Select 
                        value={preferences.dashboardLayout} 
                        onValueChange={(value: any) => updatePreferences({ dashboardLayout: value })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">Default</SelectItem>
                          <SelectItem value="compact">Compact</SelectItem>
                          <SelectItem value="detailed">Detailed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="w-5 h-5" />
                      Notification Settings
                    </CardTitle>
                    <CardDescription>Control how you receive notifications</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Enable Notifications</Label>
                        <p className="text-sm text-muted-foreground">Receive system notifications</p>
                      </div>
                      <Switch 
                        checked={preferences.notificationsEnabled}
                        onCheckedChange={(checked) => updatePreferences({ notificationsEnabled: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="flex items-center gap-2">
                          {preferences.soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                          Sound Notifications
                        </Label>
                        <p className="text-sm text-muted-foreground">Play sound for notifications</p>
                      </div>
                      <Switch 
                        checked={preferences.soundEnabled}
                        onCheckedChange={(checked) => updatePreferences({ soundEnabled: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Desktop Notifications</Label>
                        <p className="text-sm text-muted-foreground">Show browser notifications</p>
                      </div>
                      <Switch 
                        checked={preferences.desktopNotifications}
                        onCheckedChange={(checked) => updatePreferences({ desktopNotifications: checked })}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="w-5 h-5" />
                      Data & Display Formats
                    </CardTitle>
                    <CardDescription>Configure how data is displayed</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Date Format</Label>
                      <Select 
                        value={preferences.dateFormat} 
                        onValueChange={(value: any) => updatePreferences({ dateFormat: value })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Time Format</Label>
                      <Select 
                        value={preferences.timeFormat} 
                        onValueChange={(value: any) => updatePreferences({ timeFormat: value })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="12h">12 Hour</SelectItem>
                          <SelectItem value="24h">24 Hour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Currency</Label>
                      <Select 
                        value={preferences.currency} 
                        onValueChange={(value: any) => updatePreferences({ currency: value })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                          <SelectItem value="JPY">JPY (¥)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'advanced' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Advanced Settings
                    </CardTitle>
                    <CardDescription>Advanced configuration options</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Auto Save</Label>
                        <p className="text-sm text-muted-foreground">Automatically save changes</p>
                      </div>
                      <Switch 
                        checked={preferences.autoSave}
                        onCheckedChange={(checked) => updatePreferences({ autoSave: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Auto Refresh</Label>
                        <p className="text-sm text-muted-foreground">Automatically refresh data</p>
                      </div>
                      <Switch 
                        checked={preferences.autoRefresh}
                        onCheckedChange={(checked) => updatePreferences({ autoRefresh: checked })}
                      />
                    </div>

                    {preferences.autoRefresh && (
                      <div>
                        <Label>Refresh Interval (minutes)</Label>
                        <div className="mt-2">
                          <Slider
                            value={[preferences.refreshInterval]}
                            onValueChange={([value]) => updatePreferences({ refreshInterval: value })}
                            max={60}
                            min={1}
                            step={1}
                            className="w-full"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>1 min</span>
                            <span>{preferences.refreshInterval} min</span>
                            <span>60 min</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="flex items-center gap-2">
                          {preferences.debugMode ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                          Debug Mode
                        </Label>
                        <p className="text-sm text-muted-foreground">Show debug information</p>
                      </div>
                      <Switch 
                        checked={preferences.debugMode}
                        onCheckedChange={(checked) => updatePreferences({ debugMode: checked })}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
