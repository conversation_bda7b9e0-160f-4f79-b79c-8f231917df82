import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface UserPreferences {
  // Theme & Appearance
  theme: 'light' | 'dark' | 'system';
  accentColor: string;
  gradientTheme: string;
  fontSize: 'sm' | 'md' | 'lg';
  reducedMotion: boolean;
  highContrast: boolean;
  
  // Layout & Navigation
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  dashboardLayout: 'grid' | 'list' | 'compact';
  cardDensity: 'comfortable' | 'compact' | 'spacious';
  showQuickActions: boolean;
  
  // Notifications & Alerts
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  notificationPosition: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  emailNotifications: boolean;
  pushNotifications: boolean;
  
  // Data & Analytics
  defaultDateRange: '7d' | '30d' | '90d' | '1y';
  defaultCurrency: string;
  numberFormat: 'standard' | 'compact' | 'scientific';
  chartAnimations: boolean;
  autoRefreshInterval: number; // in minutes, 0 = disabled
  
  // Productivity
  keyboardShortcuts: boolean;
  commandPaletteEnabled: boolean;
  tooltipsEnabled: boolean;
  contextualHelp: boolean;
  
  // Privacy & Security
  analyticsOptIn: boolean;
  sessionTimeout: number; // in minutes
  twoFactorEnabled: boolean;
  
  // Accessibility
  screenReaderOptimized: boolean;
  keyboardNavigation: boolean;
  focusIndicators: 'standard' | 'enhanced';
  
  // Custom Preferences
  favoritePages: string[];
  recentActions: string[];
  customDashboardWidgets: string[];
  workspaceLayout: Record<string, any>;
}

interface PersonalizationContextType {
  preferences: UserPreferences;
  updatePreference: <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => void;
  updatePreferences: (updates: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  exportPreferences: () => string;
  importPreferences: (data: string) => boolean;
  isLoading: boolean;
}

const defaultPreferences: UserPreferences = {
  // Theme & Appearance
  theme: 'system',
  accentColor: '262 100% 70%',
  gradientTheme: '',
  fontSize: 'md',
  reducedMotion: false,
  highContrast: false,
  
  // Layout & Navigation
  sidebarCollapsed: false,
  sidebarWidth: 280,
  dashboardLayout: 'grid',
  cardDensity: 'comfortable',
  showQuickActions: true,
  
  // Notifications & Alerts
  notificationsEnabled: true,
  soundEnabled: true,
  notificationPosition: 'top-right',
  emailNotifications: true,
  pushNotifications: true,
  
  // Data & Analytics
  defaultDateRange: '30d',
  defaultCurrency: 'USD',
  numberFormat: 'standard',
  chartAnimations: true,
  autoRefreshInterval: 5,
  
  // Productivity
  keyboardShortcuts: true,
  commandPaletteEnabled: true,
  tooltipsEnabled: true,
  contextualHelp: true,
  
  // Privacy & Security
  analyticsOptIn: true,
  sessionTimeout: 60,
  twoFactorEnabled: false,
  
  // Accessibility
  screenReaderOptimized: false,
  keyboardNavigation: true,
  focusIndicators: 'standard',
  
  // Custom Preferences
  favoritePages: [],
  recentActions: [],
  customDashboardWidgets: [],
  workspaceLayout: {},
};

const PersonalizationContext = createContext<PersonalizationContextType | null>(null);

export const usePersonalization = () => {
  const context = useContext(PersonalizationContext);
  if (!context) {
    throw new Error('usePersonalization must be used within PersonalizationProvider');
  }
  return context;
};

interface PersonalizationProviderProps {
  children: ReactNode;
  userId?: string; // For user-specific preferences
}

export const PersonalizationProvider: React.FC<PersonalizationProviderProps> = ({
  children,
  userId = 'default'
}) => {
  const [preferences, setPreferences] = useState<UserPreferences>(defaultPreferences);
  const [isLoading, setIsLoading] = useState(true);

  const storageKey = `user-preferences-${userId}`;

  // Load preferences from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const parsed = JSON.parse(saved);
        setPreferences({ ...defaultPreferences, ...parsed });
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    } finally {
      setIsLoading(false);
    }
  }, [storageKey]);

  // Save preferences to localStorage
  const savePreferences = (newPreferences: UserPreferences) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(newPreferences));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  };

  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    setPreferences(prev => {
      const updated = { ...prev, [key]: value };
      savePreferences(updated);
      return updated;
    });
  };

  const updatePreferences = (updates: Partial<UserPreferences>) => {
    setPreferences(prev => {
      const updated = { ...prev, ...updates };
      savePreferences(updated);
      return updated;
    });
  };

  const resetPreferences = () => {
    setPreferences(defaultPreferences);
    savePreferences(defaultPreferences);
  };

  const exportPreferences = (): string => {
    return JSON.stringify(preferences, null, 2);
  };

  const importPreferences = (data: string): boolean => {
    try {
      const parsed = JSON.parse(data);
      const validated = { ...defaultPreferences, ...parsed };
      setPreferences(validated);
      savePreferences(validated);
      return true;
    } catch (error) {
      console.error('Failed to import preferences:', error);
      return false;
    }
  };

  // Apply preferences to document
  useEffect(() => {
    if (isLoading) return;

    const root = document.documentElement;

    // Apply theme
    root.classList.remove('light', 'dark');
    if (preferences.theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(preferences.theme);
    }

    // Apply accent color
    root.style.setProperty('--primary', preferences.accentColor);

    // Apply font size
    root.classList.remove('text-sm', 'text-base', 'text-lg');
    const fontSizeClass = {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg'
    }[preferences.fontSize];
    root.classList.add(fontSizeClass);

    // Apply reduced motion
    if (preferences.reducedMotion) {
      root.style.setProperty('--animation-duration', '0s');
      root.style.setProperty('--transition-duration', '0s');
    } else {
      root.style.removeProperty('--animation-duration');
      root.style.removeProperty('--transition-duration');
    }

    // Apply high contrast
    if (preferences.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Apply focus indicators
    root.classList.remove('focus-standard', 'focus-enhanced');
    root.classList.add(`focus-${preferences.focusIndicators}`);

  }, [preferences, isLoading]);

  const value: PersonalizationContextType = {
    preferences,
    updatePreference,
    updatePreferences,
    resetPreferences,
    exportPreferences,
    importPreferences,
    isLoading,
  };

  return (
    <PersonalizationContext.Provider value={value}>
      {children}
    </PersonalizationContext.Provider>
  );
};

// Helper hooks for specific preference categories
export const useThemePreferences = () => {
  const { preferences, updatePreference } = usePersonalization();
  
  return {
    theme: preferences.theme,
    accentColor: preferences.accentColor,
    gradientTheme: preferences.gradientTheme,
    fontSize: preferences.fontSize,
    reducedMotion: preferences.reducedMotion,
    highContrast: preferences.highContrast,
    setTheme: (theme: UserPreferences['theme']) => updatePreference('theme', theme),
    setAccentColor: (color: string) => updatePreference('accentColor', color),
    setGradientTheme: (gradient: string) => updatePreference('gradientTheme', gradient),
    setFontSize: (size: UserPreferences['fontSize']) => updatePreference('fontSize', size),
    setReducedMotion: (enabled: boolean) => updatePreference('reducedMotion', enabled),
    setHighContrast: (enabled: boolean) => updatePreference('highContrast', enabled),
  };
};

export const useLayoutPreferences = () => {
  const { preferences, updatePreference } = usePersonalization();
  
  return {
    sidebarCollapsed: preferences.sidebarCollapsed,
    sidebarWidth: preferences.sidebarWidth,
    dashboardLayout: preferences.dashboardLayout,
    cardDensity: preferences.cardDensity,
    showQuickActions: preferences.showQuickActions,
    setSidebarCollapsed: (collapsed: boolean) => updatePreference('sidebarCollapsed', collapsed),
    setSidebarWidth: (width: number) => updatePreference('sidebarWidth', width),
    setDashboardLayout: (layout: UserPreferences['dashboardLayout']) => updatePreference('dashboardLayout', layout),
    setCardDensity: (density: UserPreferences['cardDensity']) => updatePreference('cardDensity', density),
    setShowQuickActions: (show: boolean) => updatePreference('showQuickActions', show),
  };
};

export const useNotificationPreferences = () => {
  const { preferences, updatePreference } = usePersonalization();
  
  return {
    notificationsEnabled: preferences.notificationsEnabled,
    soundEnabled: preferences.soundEnabled,
    notificationPosition: preferences.notificationPosition,
    emailNotifications: preferences.emailNotifications,
    pushNotifications: preferences.pushNotifications,
    setNotificationsEnabled: (enabled: boolean) => updatePreference('notificationsEnabled', enabled),
    setSoundEnabled: (enabled: boolean) => updatePreference('soundEnabled', enabled),
    setNotificationPosition: (position: UserPreferences['notificationPosition']) => updatePreference('notificationPosition', position),
    setEmailNotifications: (enabled: boolean) => updatePreference('emailNotifications', enabled),
    setPushNotifications: (enabled: boolean) => updatePreference('pushNotifications', enabled),
  };
};

export const useAccessibilityPreferences = () => {
  const { preferences, updatePreference } = usePersonalization();
  
  return {
    screenReaderOptimized: preferences.screenReaderOptimized,
    keyboardNavigation: preferences.keyboardNavigation,
    focusIndicators: preferences.focusIndicators,
    reducedMotion: preferences.reducedMotion,
    highContrast: preferences.highContrast,
    setScreenReaderOptimized: (enabled: boolean) => updatePreference('screenReaderOptimized', enabled),
    setKeyboardNavigation: (enabled: boolean) => updatePreference('keyboardNavigation', enabled),
    setFocusIndicators: (style: UserPreferences['focusIndicators']) => updatePreference('focusIndicators', style),
    setReducedMotion: (enabled: boolean) => updatePreference('reducedMotion', enabled),
    setHighContrast: (enabled: boolean) => updatePreference('highContrast', enabled),
  };
};

// Utility functions
export const preferencesUtils = {
  // Check if user prefers dark mode
  isDarkMode: (preferences: UserPreferences): boolean => {
    if (preferences.theme === 'dark') return true;
    if (preferences.theme === 'light') return false;
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  },

  // Get effective font size in pixels
  getFontSizePixels: (preferences: UserPreferences): number => {
    const baseSizes = { sm: 14, md: 16, lg: 18 };
    return baseSizes[preferences.fontSize];
  },

  // Check if animations should be disabled
  shouldReduceMotion: (preferences: UserPreferences): boolean => {
    return preferences.reducedMotion || window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  // Get CSS custom properties for current preferences
  getCSSVariables: (preferences: UserPreferences): Record<string, string> => {
    return {
      '--primary': preferences.accentColor,
      '--font-size-base': preferencesUtils.getFontSizePixels(preferences) + 'px',
      '--sidebar-width': preferences.sidebarWidth + 'px',
      '--animation-duration': preferencesUtils.shouldReduceMotion(preferences) ? '0s' : '0.3s',
    };
  },
};
