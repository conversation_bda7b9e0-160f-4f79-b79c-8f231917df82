import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  Edit,
  Download,
  Share2,
  MapPin,
  User,
  Calendar,
  DollarSign,
  TrendingDown,
  Wrench,
  Activity,
  FileText,
  QrCode,
  Camera,
  Clock,
  AlertTriangle,
  CheckCircle,
  Target,
  BarChart3,
  Zap,
  Award
} from 'lucide-react';
import { Asset } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';

interface AssetDetailModalProps {
  asset: Asset | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (asset: Asset) => void;
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    case 'maintenance': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'retired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
  }
};

const getConditionColor = (condition: string) => {
  switch (condition.toLowerCase()) {
    case 'excellent': return 'text-green-600';
    case 'good': return 'text-blue-600';
    case 'fair': return 'text-yellow-600';
    case 'poor': return 'text-orange-600';
    case 'critical': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export const AssetDetailModal: React.FC<AssetDetailModalProps> = ({
  asset,
  isOpen,
  onClose,
  onEdit
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!asset) return null;

  // Calculate depreciation data
  const currentYear = new Date().getFullYear();
  const assetAge = currentYear - asset.purchaseDate.getFullYear();
  const depreciationAmount = asset.purchaseCost - asset.currentValue;
  const depreciationPercentage = (depreciationAmount / asset.purchaseCost) * 100;
  const remainingLife = Math.max(0, asset.usefulLife - assetAge);

  // Generate sample performance data
  const performanceData = {
    utilization: asset.utilizationRate || Math.floor(Math.random() * 40 + 60),
    efficiency: Math.floor(Math.random() * 30 + 70),
    uptime: Math.floor(Math.random() * 20 + 80),
    maintenanceCost: asset.maintenanceSchedule?.maintenanceCost || Math.floor(Math.random() * 5000 + 1000),
    downtimeHours: Math.floor(Math.random() * 50 + 10),
    lastMaintenance: asset.lastMaintenanceDate || new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    nextMaintenance: asset.nextMaintenanceDate || new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000)
  };

  // Generate sample history data
  const historyData = [
    { date: new Date('2024-01-15'), action: 'Asset Created', user: 'John Smith', details: 'Initial asset registration' },
    { date: new Date('2024-02-01'), action: 'Assigned', user: 'Sarah Johnson', details: `Assigned to ${asset.assignedUser?.name || 'Department'}` },
    { date: new Date('2024-03-15'), action: 'Maintenance', user: 'Mike Wilson', details: 'Routine preventive maintenance completed' },
    { date: new Date('2024-04-10'), action: 'Location Update', user: 'Emily Davis', details: `Moved to ${asset.location.building}` },
    { date: new Date('2024-05-20'), action: 'Value Update', user: 'David Brown', details: 'Depreciation calculation updated' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] p-0 overflow-hidden bg-background/95 backdrop-blur-xl border-2 border-white/20 shadow-2xl">
        {/* Enhanced Header with Gradient Background */}
        <DialogHeader className="relative px-8 py-6 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent border-b border-white/10 backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-transparent"></div>
          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-6">
              {/* Enhanced Asset Icon */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl blur-sm"></div>
                <div className="relative flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl border border-white/20 backdrop-blur-sm">
                  <BarChart3 className="h-8 w-8 text-primary drop-shadow-sm" />
                </div>
              </div>

              {/* Enhanced Asset Information */}
              <div className="space-y-2">
                <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                  {asset.name}
                </DialogTitle>
                <div className="flex items-center gap-3 text-sm">
                  <span className="font-mono text-muted-foreground bg-muted/50 px-2 py-1 rounded-md border border-white/10">
                    {asset.assetId}
                  </span>
                  <div className="w-1 h-1 bg-muted-foreground/50 rounded-full"></div>
                  <Badge
                    variant="outline"
                    className="text-xs bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/20 transition-all duration-300"
                  >
                    {asset.category}
                  </Badge>
                  <div className="w-1 h-1 bg-muted-foreground/50 rounded-full"></div>
                  <Badge className={`${getStatusColor(asset.status)} border-0 shadow-sm font-medium`}>
                    <div className="w-2 h-2 bg-current rounded-full mr-2 animate-pulse"></div>
                    {asset.status}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center gap-3">
              <ContextTooltip content={{
                title: 'Edit Asset',
                description: 'Open asset editor to modify details',
                type: 'info'
              }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/20 hover:scale-105 transition-all duration-300 shadow-lg"
                  onClick={() => onEdit?.(asset)}
                >
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
              </ContextTooltip>

              <ContextTooltip content={{
                title: 'Download Report',
                description: 'Generate and download asset report',
                type: 'info'
              }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/20 hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </ContextTooltip>

              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/20 hover:scale-105 transition-all duration-300 shadow-lg"
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="hover:bg-red-500/20 hover:text-red-600 hover:scale-105 transition-all duration-300 rounded-full w-10 h-10 p-0"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Enhanced Tabs Section */}
        <div className="flex-1 overflow-hidden bg-gradient-to-b from-background/50 to-background/80 backdrop-blur-sm">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="px-8 py-4 bg-gradient-to-r from-muted/30 via-muted/20 to-muted/30 border-b border-white/10 backdrop-blur-sm">
              <TabsList className="grid w-full grid-cols-5 bg-white/10 backdrop-blur-md border border-white/20 shadow-lg rounded-xl p-1">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-white/20 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-sm transition-all duration-300 hover:bg-white/10 rounded-lg font-medium"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="financial"
                  className="data-[state=active]:bg-white/20 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-sm transition-all duration-300 hover:bg-white/10 rounded-lg font-medium"
                >
                  <DollarSign className="h-4 w-4 mr-2" />
                  Financial
                </TabsTrigger>
                <TabsTrigger
                  value="maintenance"
                  className="data-[state=active]:bg-white/20 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-sm transition-all duration-300 hover:bg-white/10 rounded-lg font-medium"
                >
                  <Wrench className="h-4 w-4 mr-2" />
                  Maintenance
                </TabsTrigger>
                <TabsTrigger
                  value="performance"
                  className="data-[state=active]:bg-white/20 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-sm transition-all duration-300 hover:bg-white/10 rounded-lg font-medium"
                >
                  <Activity className="h-4 w-4 mr-2" />
                  Performance
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="data-[state=active]:bg-white/20 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-sm transition-all duration-300 hover:bg-white/10 rounded-lg font-medium"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  History
                </TabsTrigger>
              </TabsList>
            </div>

            <ScrollArea className="flex-1 px-8 py-6">
              {/* Enhanced Overview Tab */}
              <TabsContent value="overview" className="mt-0 space-y-8 animate-in fade-in-50 duration-500">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Enhanced Basic Information */}
                  <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                        <div className="p-2 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-5 w-5 text-blue-600" />
                        </div>
                        Basic Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Asset ID</label>
                          <p className="font-mono text-sm font-medium bg-muted/30 px-3 py-2 rounded-lg border border-white/10 backdrop-blur-sm">
                            {asset.assetId}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Serial Number</label>
                          <p className="font-mono text-sm font-medium bg-muted/30 px-3 py-2 rounded-lg border border-white/10 backdrop-blur-sm">
                            {asset.serialNumber}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Manufacturer</label>
                          <p className="font-medium text-foreground">{asset.manufacturer}</p>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Model</label>
                          <p className="font-medium text-foreground">{asset.model}</p>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Category</label>
                          <p className="font-medium text-foreground">{asset.category}</p>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Sub-Category</label>
                          <p className="font-medium text-foreground">{asset.subCategory}</p>
                        </div>
                      </div>

                      <Separator className="bg-white/20" />

                      <div className="space-y-3">
                        <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Condition Status</label>
                        <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-white/10 backdrop-blur-sm">
                          <div className="relative">
                            <CheckCircle className={`h-6 w-6 ${getConditionColor(asset.condition)} drop-shadow-sm`} />
                            <div className="absolute inset-0 animate-ping">
                              <CheckCircle className={`h-6 w-6 ${getConditionColor(asset.condition)} opacity-20`} />
                            </div>
                          </div>
                          <div>
                            <span className={`font-semibold text-lg ${getConditionColor(asset.condition)}`}>
                              {asset.condition}
                            </span>
                            <p className="text-xs text-muted-foreground">Current asset condition</p>
                          </div>
                        </div>
                      </div>

                      {asset.notes && (
                        <div className="space-y-3">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Notes</label>
                          <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/20 rounded-xl border border-white/10 backdrop-blur-sm">
                            <p className="text-sm leading-relaxed text-foreground/90">{asset.notes}</p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Enhanced Location & Assignment */}
                  <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                        <div className="p-2 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                          <MapPin className="h-5 w-5 text-green-600" />
                        </div>
                        Location & Assignment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-3">
                        <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Physical Location</label>
                        <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/20 rounded-xl border border-white/10 backdrop-blur-sm">
                          <div className="space-y-2">
                            <p className="font-semibold text-lg text-foreground">{asset.location.site}</p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                              <span>{asset.location.building}</span>
                              <span>•</span>
                              <span>{asset.location.floor}</span>
                              <span>•</span>
                              <span>{asset.location.room}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Department</label>
                        <div className="p-3 bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-lg border border-blue-500/20 backdrop-blur-sm">
                          <p className="font-medium text-blue-600">{asset.department}</p>
                        </div>
                      </div>

                      {asset.assignedUser && (
                        <div className="space-y-3">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Assigned User</label>
                          <div className="p-4 bg-gradient-to-br from-purple-500/10 to-purple-600/10 rounded-xl border border-purple-500/20 backdrop-blur-sm hover:bg-purple-500/15 transition-all duration-300">
                            <div className="flex items-center gap-4">
                              <div className="relative">
                                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/30 to-purple-600/30 rounded-full blur-sm"></div>
                                <div className="relative flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-full border border-white/20 backdrop-blur-sm">
                                  <User className="h-6 w-6 text-purple-600" />
                                </div>
                              </div>
                              <div className="space-y-1">
                                <p className="font-semibold text-foreground">{asset.assignedUser.name}</p>
                                <p className="text-sm text-muted-foreground">{asset.assignedUser.email}</p>
                                <p className="text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded-md inline-block">
                                  Assigned: {formatDate(asset.assignedUser.assignedDate)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Enhanced QR Code & Tags */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                        <div className="p-2 bg-gradient-to-br from-orange-500/20 to-orange-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                          <QrCode className="h-5 w-5 text-orange-600" />
                        </div>
                        QR Code & Identification
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="relative p-8 bg-gradient-to-br from-muted/30 to-muted/20 rounded-2xl border border-white/10 backdrop-blur-sm overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-orange-600/5"></div>
                        <div className="relative text-center space-y-4">
                          <div className="relative inline-block">
                            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-orange-600/20 rounded-2xl blur-lg"></div>
                            <div className="relative p-4 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30">
                              <QrCode className="h-20 w-20 text-orange-600 mx-auto drop-shadow-lg" />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <p className="text-lg font-semibold text-foreground">QR Code</p>
                            <p className="text-sm font-mono text-muted-foreground bg-muted/30 px-3 py-1 rounded-lg border border-white/10 inline-block">
                              {asset.qrCode}
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="gap-2 bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/20 hover:scale-105 transition-all duration-300 shadow-lg"
                          >
                            <Download className="h-4 w-4" />
                            Download QR Code
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                        <div className="p-2 bg-gradient-to-br from-pink-500/20 to-pink-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                          <Target className="h-5 w-5 text-pink-600" />
                        </div>
                        Tags & Classification
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="space-y-3">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Asset Tags</label>
                          <div className="flex flex-wrap gap-3">
                            {asset.tags.map((tag, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="text-xs bg-gradient-to-r from-pink-500/20 to-pink-600/20 border border-pink-500/30 text-pink-600 hover:bg-pink-500/30 transition-all duration-300 hover:scale-105 shadow-sm backdrop-blur-sm"
                              >
                                <div className="w-2 h-2 bg-pink-500 rounded-full mr-2 animate-pulse"></div>
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-3">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Attachments</label>
                          <div className="space-y-3">
                            {asset.attachments.length > 0 ? (
                              asset.attachments.map((attachment) => (
                                <div
                                  key={attachment.id}
                                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-muted/30 to-muted/20 rounded-lg border border-white/10 backdrop-blur-sm hover:bg-muted/40 transition-all duration-300 group/attachment"
                                >
                                  <div className="p-2 bg-blue-500/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover/attachment:scale-110 transition-transform duration-300">
                                    <FileText className="h-4 w-4 text-blue-600" />
                                  </div>
                                  <span className="text-sm font-medium flex-1">{attachment.name}</span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="hover:bg-blue-500/20 hover:text-blue-600 transition-all duration-300 rounded-full w-8 h-8 p-0"
                                  >
                                    <Download className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))
                            ) : (
                              <div className="text-center py-6 text-muted-foreground">
                                <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <p className="text-sm">No attachments available</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Enhanced Financial Tab */}
              <TabsContent value="financial" className="mt-0 space-y-8 animate-in fade-in-50 duration-500">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Enhanced Purchase Information */}
                  <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                        <div className="p-2 bg-gradient-to-br from-emerald-500/20 to-emerald-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                          <DollarSign className="h-5 w-5 text-emerald-600" />
                        </div>
                        Purchase Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Purchase Date</label>
                          <div className="p-3 bg-gradient-to-r from-muted/30 to-muted/20 rounded-lg border border-white/10 backdrop-blur-sm">
                            <p className="font-medium text-foreground">{formatDate(asset.purchaseDate)}</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Purchase Cost</label>
                          <div className="p-3 bg-gradient-to-br from-emerald-500/10 to-emerald-600/10 rounded-lg border border-emerald-500/20 backdrop-blur-sm">
                            <p className="font-bold text-xl text-emerald-600">{formatCurrency(asset.purchaseCost)}</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Current Value</label>
                          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-lg border border-blue-500/20 backdrop-blur-sm">
                            <p className="font-bold text-xl text-blue-600">{formatCurrency(asset.currentValue)}</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Salvage Value</label>
                          <div className="p-3 bg-gradient-to-r from-muted/30 to-muted/20 rounded-lg border border-white/10 backdrop-blur-sm">
                            <p className="font-medium text-foreground">{formatCurrency(asset.salvageValue)}</p>
                          </div>
                        </div>
                      </div>

                      {asset.warrantyExpiration && (
                        <div className="space-y-3">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Warranty Status</label>
                          <div className="p-4 bg-gradient-to-br from-muted/30 to-muted/20 rounded-xl border border-white/10 backdrop-blur-sm">
                            <div className="flex items-center gap-3">
                              <Calendar className="h-5 w-5 text-muted-foreground" />
                              <div className="flex-1">
                                <p className="font-medium text-foreground">{formatDate(asset.warrantyExpiration)}</p>
                                <p className="text-xs text-muted-foreground">Warranty expiration date</p>
                              </div>
                              {asset.warrantyExpiration > new Date() ? (
                                <Badge className="bg-gradient-to-r from-green-500/20 to-green-600/20 text-green-600 border border-green-500/30 shadow-sm backdrop-blur-sm">
                                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                  Active
                                </Badge>
                              ) : (
                                <Badge className="bg-gradient-to-r from-red-500/20 to-red-600/20 text-red-600 border border-red-500/30 shadow-sm backdrop-blur-sm">
                                  <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                  Expired
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Enhanced Depreciation Analysis */}
                  <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                        <div className="p-2 bg-gradient-to-br from-orange-500/20 to-orange-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                          <TrendingDown className="h-5 w-5 text-orange-600" />
                        </div>
                        Depreciation Analysis
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Method</label>
                          <div className="p-3 bg-gradient-to-r from-orange-500/10 to-orange-600/10 rounded-lg border border-orange-500/20 backdrop-blur-sm">
                            <p className="font-medium text-orange-600">{asset.depreciationMethod}</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Rate</label>
                          <div className="p-3 bg-gradient-to-r from-muted/30 to-muted/20 rounded-lg border border-white/10 backdrop-blur-sm">
                            <p className="font-medium text-foreground">{asset.depreciationRate}%</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Useful Life</label>
                          <div className="p-3 bg-gradient-to-r from-muted/30 to-muted/20 rounded-lg border border-white/10 backdrop-blur-sm">
                            <p className="font-medium text-foreground">{asset.usefulLife} years</p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Asset Age</label>
                          <div className="p-3 bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-lg border border-blue-500/20 backdrop-blur-sm">
                            <p className="font-medium text-blue-600">{assetAge} years</p>
                          </div>
                        </div>
                      </div>

                      <Separator className="bg-white/20" />

                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-500/10 to-orange-600/10 rounded-lg border border-orange-500/20 backdrop-blur-sm">
                          <span className="text-sm font-semibold text-muted-foreground">Total Depreciation</span>
                          <span className="font-bold text-lg text-orange-600">{formatCurrency(depreciationAmount)}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-muted/30 to-muted/20 rounded-lg border border-white/10 backdrop-blur-sm">
                          <span className="text-sm font-semibold text-muted-foreground">Depreciation Percentage</span>
                          <span className="font-bold text-lg text-foreground">{Math.round(depreciationPercentage)}%</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-lg border border-blue-500/20 backdrop-blur-sm">
                          <span className="text-sm font-semibold text-muted-foreground">Remaining Life</span>
                          <span className="font-bold text-lg text-blue-600">{remainingLife} years</span>
                        </div>
                      </div>

                      {/* Enhanced Progress Bar */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Depreciation Progress</span>
                          <span className="text-sm font-bold text-orange-600">{Math.round(depreciationPercentage)}%</span>
                        </div>
                        <div className="relative">
                          <div className="w-full h-3 bg-gradient-to-r from-gray-200/50 to-gray-300/50 rounded-full border border-white/20 backdrop-blur-sm overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden"
                              style={{ width: `${Math.min(depreciationPercentage, 100)}%` }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                              <div className="absolute inset-0 animate-pulse bg-white/10"></div>
                            </div>
                          </div>
                          <div className="absolute inset-0 rounded-full shadow-inner"></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Enhanced Financial Performance */}
                <Card className="bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/15 group">
                  <CardHeader className="pb-6">
                    <CardTitle className="flex items-center gap-3 text-xl font-semibold">
                      <div className="p-2 bg-gradient-to-br from-indigo-500/20 to-indigo-600/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover:scale-110 transition-transform duration-300">
                        <BarChart3 className="h-6 w-6 text-indigo-600" />
                      </div>
                      Financial Performance Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* ROI Analysis */}
                      <div className="relative p-6 bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-2xl border border-blue-500/20 backdrop-blur-sm hover:bg-blue-500/15 transition-all duration-500 group/metric overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent"></div>
                        <div className="relative">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="p-2 bg-blue-500/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover/metric:scale-110 transition-transform duration-300">
                              <Award className="h-5 w-5 text-blue-600" />
                            </div>
                            <span className="font-semibold text-blue-600">ROI Analysis</span>
                          </div>
                          <div className="space-y-2">
                            <p className="text-3xl font-bold text-blue-600 drop-shadow-sm">
                              {(((asset.currentValue - asset.purchaseCost) / asset.purchaseCost) * 100).toFixed(1)}%
                            </p>
                            <p className="text-sm text-blue-600/80 font-medium">Return on Investment</p>
                          </div>
                        </div>
                      </div>

                      {/* Annual TCO */}
                      <div className="relative p-6 bg-gradient-to-br from-emerald-500/10 to-emerald-600/10 rounded-2xl border border-emerald-500/20 backdrop-blur-sm hover:bg-emerald-500/15 transition-all duration-500 group/metric overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent"></div>
                        <div className="relative">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="p-2 bg-emerald-500/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover/metric:scale-110 transition-transform duration-300">
                              <DollarSign className="h-5 w-5 text-emerald-600" />
                            </div>
                            <span className="font-semibold text-emerald-600">Annual TCO</span>
                          </div>
                          <div className="space-y-2">
                            <p className="text-3xl font-bold text-emerald-600 drop-shadow-sm">
                              {formatCurrency((asset.purchaseCost + (performanceData.maintenanceCost * assetAge)) / Math.max(assetAge, 1))}
                            </p>
                            <p className="text-sm text-emerald-600/80 font-medium">Total Cost of Ownership</p>
                          </div>
                        </div>
                      </div>

                      {/* Value Retention */}
                      <div className="relative p-6 bg-gradient-to-br from-purple-500/10 to-purple-600/10 rounded-2xl border border-purple-500/20 backdrop-blur-sm hover:bg-purple-500/15 transition-all duration-500 group/metric overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent"></div>
                        <div className="relative">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="p-2 bg-purple-500/20 rounded-lg border border-white/20 backdrop-blur-sm group-hover/metric:scale-110 transition-transform duration-300">
                              <Target className="h-5 w-5 text-purple-600" />
                            </div>
                            <span className="font-semibold text-purple-600">Value Retention</span>
                          </div>
                          <div className="space-y-2">
                            <p className="text-3xl font-bold text-purple-600 drop-shadow-sm">
                              {Math.round((asset.currentValue / asset.purchaseCost) * 100)}%
                            </p>
                            <p className="text-sm text-purple-600/80 font-medium">Of original value</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Maintenance Tab */}
              <TabsContent value="maintenance" className="mt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Maintenance Schedule */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Wrench className="h-5 w-5" />
                        Maintenance Schedule
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {asset.maintenanceSchedule ? (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Frequency</label>
                              <p className="font-medium capitalize">{asset.maintenanceSchedule.frequency}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Type</label>
                              <p className="font-medium capitalize">{asset.maintenanceSchedule.maintenanceType}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Cost per Service</label>
                              <p className="font-medium">{formatCurrency(asset.maintenanceSchedule.maintenanceCost)}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Next Maintenance</label>
                              <p className="font-medium">{formatDate(asset.maintenanceSchedule.nextMaintenance)}</p>
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-muted-foreground">Last Maintenance</span>
                              <span className="font-medium">{formatDate(performanceData.lastMaintenance)}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-muted-foreground">Days Since Last</span>
                              <span className="font-medium">
                                {Math.floor((new Date().getTime() - performanceData.lastMaintenance.getTime()) / (1000 * 60 * 60 * 24))} days
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-muted-foreground">Days Until Next</span>
                              <span className="font-medium text-blue-600">
                                {Math.floor((performanceData.nextMaintenance.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                              </span>
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="text-center py-8">
                          <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                          <p className="text-muted-foreground">No maintenance schedule configured</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Maintenance History */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        Recent Maintenance
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          { date: new Date('2024-03-15'), type: 'Preventive', cost: 1200, status: 'Completed' },
                          { date: new Date('2024-01-20'), type: 'Corrective', cost: 850, status: 'Completed' },
                          { date: new Date('2023-11-10'), type: 'Preventive', cost: 1100, status: 'Completed' },
                          { date: new Date('2023-09-05'), type: 'Inspection', cost: 300, status: 'Completed' }
                        ].map((maintenance, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                                <Wrench className="h-4 w-4 text-primary" />
                              </div>
                              <div>
                                <p className="font-medium">{maintenance.type}</p>
                                <p className="text-sm text-muted-foreground">{formatDate(maintenance.date)}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">{formatCurrency(maintenance.cost)}</p>
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                                {maintenance.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Maintenance Insights */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Maintenance Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="font-semibold text-green-800 dark:text-green-200">Reliability Score</span>
                        </div>
                        <p className="text-2xl font-bold text-green-600">{performanceData.uptime}%</p>
                        <p className="text-sm text-green-700 dark:text-green-300">Uptime performance</p>
                      </div>

                      <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="h-5 w-5 text-yellow-600" />
                          <span className="font-semibold text-yellow-800 dark:text-yellow-200">Downtime</span>
                        </div>
                        <p className="text-2xl font-bold text-yellow-600">{performanceData.downtimeHours}h</p>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300">This quarter</p>
                      </div>

                      <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center gap-2 mb-2">
                          <DollarSign className="h-5 w-5 text-blue-600" />
                          <span className="font-semibold text-blue-800 dark:text-blue-200">Maintenance Cost</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-600">{formatCurrency(performanceData.maintenanceCost)}</p>
                        <p className="text-sm text-blue-700 dark:text-blue-300">Annual average</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Performance Tab */}
              <TabsContent value="performance" className="mt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Performance Metrics */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        Performance Metrics
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Utilization Rate</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${performanceData.utilization}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-blue-600">{performanceData.utilization}%</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Efficiency Score</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${performanceData.efficiency}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-green-600">{performanceData.efficiency}%</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Uptime</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${performanceData.uptime}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-purple-600">{performanceData.uptime}%</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Overall Score</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.round((performanceData.utilization + performanceData.efficiency + performanceData.uptime) / 3)}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-orange-600">
                              {Math.round((performanceData.utilization + performanceData.efficiency + performanceData.uptime) / 3)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Trends */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Performance Trends
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-green-800 dark:text-green-200">This Month</span>
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              +5.2%
                            </Badge>
                          </div>
                          <p className="text-sm text-green-700 dark:text-green-300">
                            Performance improved compared to last month
                          </p>
                        </div>

                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-blue-800 dark:text-blue-200">This Quarter</span>
                            <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              +12.8%
                            </Badge>
                          </div>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            Significant improvement in utilization rate
                          </p>
                        </div>

                        <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-purple-800 dark:text-purple-200">Year to Date</span>
                            <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                              +8.4%
                            </Badge>
                          </div>
                          <p className="text-sm text-purple-700 dark:text-purple-300">
                            Consistent performance growth throughout the year
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Performance Insights */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Performance Insights & Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">Strengths</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">High reliability score ({performanceData.uptime}%)</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Consistent utilization rate</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Low maintenance costs</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-medium">Improvement Areas</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-950/20 rounded">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm">Optimize scheduling for higher utilization</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-950/20 rounded">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm">Consider preventive maintenance</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-950/20 rounded">
                            <Target className="h-4 w-4 text-blue-600" />
                            <span className="text-sm">Monitor efficiency trends closely</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* History Tab */}
              <TabsContent value="history" className="mt-0 space-y-6">
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Asset History & Activity Log
                    </CardTitle>
                    <CardDescription>
                      Complete timeline of asset activities and changes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {historyData.map((entry, index) => (
                        <div key={index} className="flex items-start gap-4 p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                          <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full flex-shrink-0">
                            {entry.action === 'Asset Created' && <FileText className="h-5 w-5 text-primary" />}
                            {entry.action === 'Assigned' && <User className="h-5 w-5 text-primary" />}
                            {entry.action === 'Maintenance' && <Wrench className="h-5 w-5 text-primary" />}
                            {entry.action === 'Location Update' && <MapPin className="h-5 w-5 text-primary" />}
                            {entry.action === 'Value Update' && <DollarSign className="h-5 w-5 text-primary" />}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium">{entry.action}</h4>
                              <span className="text-sm text-muted-foreground">{formatDate(entry.date)}</span>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">{entry.details}</p>
                            <div className="flex items-center gap-2">
                              <div className="flex items-center justify-center w-6 h-6 bg-muted rounded-full">
                                <User className="h-3 w-3 text-muted-foreground" />
                              </div>
                              <span className="text-xs text-muted-foreground">{entry.user}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Asset Metadata */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Asset Metadata
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <h4 className="font-medium">Creation Information</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Created At</span>
                            <span className="text-sm font-medium">{formatDate(asset.createdAt)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Created By</span>
                            <span className="text-sm font-medium">{asset.createdBy}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Asset ID</span>
                            <span className="text-sm font-medium font-mono">{asset.assetId}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-medium">Last Modified</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Updated At</span>
                            <span className="text-sm font-medium">{formatDate(asset.updatedAt)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Modified By</span>
                            <span className="text-sm font-medium">{asset.lastModifiedBy}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">QR Code</span>
                            <span className="text-sm font-medium font-mono">{asset.qrCode}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
