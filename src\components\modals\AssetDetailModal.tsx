import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  Edit,
  Download,
  Share2,
  MapPin,
  User,
  Calendar,
  DollarSign,
  TrendingDown,
  Wrench,
  Activity,
  FileText,
  QrCode,
  Clock,
  AlertTriangle,
  CheckCircle,
  Target,
  BarChart3,
  Zap,
  Award
} from 'lucide-react';
import { Asset } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';

interface AssetDetailModalProps {
  asset: Asset | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (asset: Asset) => void;
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    case 'maintenance': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'retired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
  }
};

const getConditionColor = (condition: string) => {
  switch (condition.toLowerCase()) {
    case 'excellent': return 'text-green-600';
    case 'good': return 'text-blue-600';
    case 'fair': return 'text-yellow-600';
    case 'poor': return 'text-orange-600';
    case 'critical': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export const AssetDetailModal: React.FC<AssetDetailModalProps> = ({
  asset,
  isOpen,
  onClose,
  onEdit
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!asset) return null;

  // Calculate depreciation data
  const currentYear = new Date().getFullYear();
  const assetAge = currentYear - asset.purchaseDate.getFullYear();
  const depreciationAmount = asset.purchaseCost - asset.currentValue;
  const depreciationPercentage = (depreciationAmount / asset.purchaseCost) * 100;
  const remainingLife = Math.max(0, asset.usefulLife - assetAge);

  // Generate sample performance data
  const performanceData = {
    utilization: asset.utilizationRate || Math.floor(Math.random() * 40 + 60),
    efficiency: Math.floor(Math.random() * 30 + 70),
    uptime: Math.floor(Math.random() * 20 + 80),
    maintenanceCost: asset.maintenanceSchedule?.maintenanceCost || Math.floor(Math.random() * 5000 + 1000),
    downtimeHours: Math.floor(Math.random() * 50 + 10),
    lastMaintenance: asset.lastMaintenanceDate || new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    nextMaintenance: asset.nextMaintenanceDate || new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000)
  };

  // Generate sample history data
  const historyData = [
    { date: new Date('2024-01-15'), action: 'Asset Created', user: 'John Smith', details: 'Initial asset registration' },
    { date: new Date('2024-02-01'), action: 'Assigned', user: 'Sarah Johnson', details: `Assigned to ${asset.assignedUser?.name || 'Department'}` },
    { date: new Date('2024-03-15'), action: 'Maintenance', user: 'Mike Wilson', details: 'Routine preventive maintenance completed' },
    { date: new Date('2024-04-10'), action: 'Location Update', user: 'Emily Davis', details: `Moved to ${asset.location.building}` },
    { date: new Date('2024-05-20'), action: 'Value Update', user: 'David Brown', details: 'Depreciation calculation updated' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] p-0 overflow-hidden bg-background/5 backdrop-blur-3xl border-2 border-white/8 shadow-2xl">
        {/* Enhanced Header */}
        <DialogHeader className="relative px-6 py-4 bg-gradient-to-r from-primary/5 via-primary/2 to-transparent border-b border-white/5 backdrop-blur-3xl">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/2 via-purple-500/2 to-transparent"></div>
          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Enhanced Asset Icon */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/12 to-primary/6 rounded-2xl blur-sm"></div>
                <div className="relative flex items-center justify-center w-14 h-14 bg-gradient-to-br from-primary/8 to-primary/4 rounded-2xl border border-white/12 backdrop-blur-2xl">
                  <BarChart3 className="h-7 w-7 text-primary drop-shadow-sm" />
                </div>
              </div>

              {/* Enhanced Asset Information */}
              <div className="space-y-1">
                <DialogTitle className="text-xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                  {asset.name}
                </DialogTitle>
                <div className="flex items-center gap-2 text-sm">
                  <span className="font-mono text-muted-foreground bg-muted/30 px-2 py-1 rounded-md border border-white/8">
                    {asset.assetId}
                  </span>
                  <div className="w-1 h-1 bg-muted-foreground/50 rounded-full"></div>
                  <Badge
                    variant="outline"
                    className="text-xs bg-white/5 border-white/12 backdrop-blur-2xl hover:bg-white/10 transition-all duration-300"
                  >
                    {asset.category}
                  </Badge>
                  <div className="w-1 h-1 bg-muted-foreground/50 rounded-full"></div>
                  <Badge className={`${getStatusColor(asset.status)} border-0 shadow-sm font-medium`}>
                    <div className="w-2 h-2 bg-current rounded-full mr-2 animate-pulse"></div>
                    {asset.status}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center gap-2">
              <ContextTooltip content={{
                title: 'Edit Asset',
                description: 'Open asset editor to modify details',
                type: 'info'
              }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 bg-white/5 border-white/12 backdrop-blur-2xl hover:bg-white/10 hover:scale-105 transition-all duration-300 shadow-lg"
                  onClick={() => onEdit?.(asset)}
                >
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
              </ContextTooltip>

              <ContextTooltip content={{
                title: 'Download Report',
                description: 'Generate and download asset report',
                type: 'info'
              }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 bg-white/5 border-white/12 backdrop-blur-2xl hover:bg-white/10 hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </ContextTooltip>

              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-white/5 border-white/12 backdrop-blur-2xl hover:bg-white/10 hover:scale-105 transition-all duration-300 shadow-lg"
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="hover:bg-red-500/15 hover:text-red-600 hover:scale-105 transition-all duration-300 rounded-full w-9 h-9 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Enhanced Tabs Section with Fixed Height */}
        <div className="flex-1 flex flex-col min-h-0 bg-gradient-to-b from-background/20 to-background/40 backdrop-blur-3xl">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col min-h-0">
            <div className="px-6 py-3 bg-gradient-to-r from-muted/12 via-muted/8 to-muted/12 border-b border-white/5 backdrop-blur-3xl">
              <TabsList className="grid w-full grid-cols-5 bg-white/3 backdrop-blur-3xl border border-white/8 shadow-lg rounded-xl p-1">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-white/8 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-3xl transition-all duration-300 hover:bg-white/5 rounded-lg font-medium text-xs"
                >
                  <FileText className="h-3 w-3 mr-1" />
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="financial"
                  className="data-[state=active]:bg-white/8 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-3xl transition-all duration-300 hover:bg-white/5 rounded-lg font-medium text-xs"
                >
                  <DollarSign className="h-3 w-3 mr-1" />
                  Financial
                </TabsTrigger>
                <TabsTrigger
                  value="maintenance"
                  className="data-[state=active]:bg-white/8 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-3xl transition-all duration-300 hover:bg-white/5 rounded-lg font-medium text-xs"
                >
                  <Wrench className="h-3 w-3 mr-1" />
                  Maintenance
                </TabsTrigger>
                <TabsTrigger
                  value="performance"
                  className="data-[state=active]:bg-white/8 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-3xl transition-all duration-300 hover:bg-white/5 rounded-lg font-medium text-xs"
                >
                  <Activity className="h-3 w-3 mr-1" />
                  Performance
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="data-[state=active]:bg-white/8 data-[state=active]:text-primary data-[state=active]:shadow-lg data-[state=active]:backdrop-blur-3xl transition-all duration-300 hover:bg-white/5 rounded-lg font-medium text-xs"
                >
                  <Clock className="h-3 w-3 mr-1" />
                  History
                </TabsTrigger>
              </TabsList>
            </div>

            <ScrollArea className="flex-1 min-h-0 max-h-[calc(95vh-200px)]">
              <div className="px-6 py-4 pb-12 space-y-8">
                {/* Overview Tab */}
                <TabsContent value="overview" className="mt-0 space-y-6 animate-in fade-in-50 duration-500">
                  <div className="grid grid-cols-1 gap-6">
                    {/* Basic Information */}
                    <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-3 text-base font-semibold">
                          <div className="p-2 bg-gradient-to-br from-blue-500/12 to-blue-600/8 rounded-lg border border-white/12 backdrop-blur-2xl group-hover:scale-110 transition-transform duration-300">
                            <FileText className="h-4 w-4 text-blue-600" />
                          </div>
                          Basic Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Asset ID</label>
                            <p className="font-mono text-sm font-medium bg-muted/20 px-2 py-1 rounded-lg border border-white/8 backdrop-blur-2xl">
                              {asset.assetId}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Serial Number</label>
                            <p className="font-mono text-sm font-medium bg-muted/20 px-2 py-1 rounded-lg border border-white/8 backdrop-blur-2xl">
                              {asset.serialNumber}
                            </p>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Manufacturer</label>
                            <p className="font-medium text-foreground text-sm">{asset.manufacturer}</p>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Model</label>
                            <p className="font-medium text-foreground text-sm">{asset.model}</p>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Category</label>
                            <p className="font-medium text-foreground text-sm">{asset.category}</p>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Sub-Category</label>
                            <p className="font-medium text-foreground text-sm">{asset.subCategory}</p>
                          </div>
                        </div>

                        <Separator className="bg-white/12" />

                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Condition Status</label>
                          <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-muted/12 to-muted/6 rounded-xl border border-white/8 backdrop-blur-2xl">
                            <div className="relative">
                              <CheckCircle className={`h-5 w-5 ${getConditionColor(asset.condition)} drop-shadow-sm`} />
                              <div className="absolute inset-0 animate-ping">
                                <CheckCircle className={`h-5 w-5 ${getConditionColor(asset.condition)} opacity-20`} />
                              </div>
                            </div>
                            <div>
                              <span className={`font-semibold text-base ${getConditionColor(asset.condition)}`}>
                                {asset.condition}
                              </span>
                              <p className="text-xs text-muted-foreground">Current asset condition</p>
                            </div>
                          </div>
                        </div>

                        {asset.notes && (
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Notes</label>
                            <div className="p-3 bg-gradient-to-br from-muted/20 to-muted/12 rounded-xl border border-white/8 backdrop-blur-2xl">
                              <p className="text-sm leading-relaxed text-foreground/90">{asset.notes}</p>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Location & Assignment */}
                    <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-3 text-base font-semibold">
                          <div className="p-2 bg-gradient-to-br from-green-500/12 to-green-600/8 rounded-lg border border-white/12 backdrop-blur-2xl group-hover:scale-110 transition-transform duration-300">
                            <MapPin className="h-4 w-4 text-green-600" />
                          </div>
                          Location & Assignment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Physical Location</label>
                          <div className="p-3 bg-gradient-to-br from-muted/20 to-muted/12 rounded-xl border border-white/8 backdrop-blur-2xl">
                            <div className="space-y-1">
                              <p className="font-semibold text-base text-foreground">{asset.location.site}</p>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span>{asset.location.building}</span>
                                <span>•</span>
                                <span>{asset.location.floor}</span>
                                <span>•</span>
                                <span>{asset.location.room}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Department</label>
                          <div className="p-2 bg-gradient-to-r from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                            <p className="font-medium text-blue-600 text-sm">{asset.department}</p>
                          </div>
                        </div>

                        {asset.assignedUser && (
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Assigned User</label>
                            <div className="p-3 bg-gradient-to-br from-purple-500/8 to-purple-600/8 rounded-xl border border-purple-500/12 backdrop-blur-2xl hover:bg-purple-500/12 transition-all duration-300">
                              <div className="flex items-center gap-3">
                                <div className="relative">
                                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-full blur-sm"></div>
                                  <div className="relative flex items-center justify-center w-10 h-10 bg-gradient-to-br from-purple-500/12 to-purple-600/12 rounded-full border border-white/12 backdrop-blur-2xl">
                                    <User className="h-5 w-5 text-purple-600" />
                                  </div>
                                </div>
                                <div className="space-y-1">
                                  <p className="font-semibold text-foreground text-sm">{asset.assignedUser.name}</p>
                                  <p className="text-xs text-muted-foreground">{asset.assignedUser.email}</p>
                                  <p className="text-xs text-muted-foreground bg-muted/20 px-2 py-1 rounded-md inline-block">
                                    Assigned: {formatDate(asset.assignedUser.assignedDate)}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Financial Tab */}
                <TabsContent value="financial" className="mt-0 space-y-6 animate-in fade-in-50 duration-500">
                  <div className="grid grid-cols-1 gap-6">
                    {/* Purchase Information */}
                    <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-3 text-base font-semibold">
                          <div className="p-2 bg-gradient-to-br from-emerald-500/12 to-emerald-600/8 rounded-lg border border-white/12 backdrop-blur-2xl group-hover:scale-110 transition-transform duration-300">
                            <DollarSign className="h-4 w-4 text-emerald-600" />
                          </div>
                          Purchase Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Purchase Date</label>
                            <div className="p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <p className="font-medium text-foreground text-sm">{formatDate(asset.purchaseDate)}</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Purchase Cost</label>
                            <div className="p-2 bg-gradient-to-br from-emerald-500/8 to-emerald-600/8 rounded-lg border border-emerald-500/12 backdrop-blur-2xl">
                              <p className="font-bold text-lg text-emerald-600">{formatCurrency(asset.purchaseCost)}</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Current Value</label>
                            <div className="p-2 bg-gradient-to-br from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                              <p className="font-bold text-lg text-blue-600">{formatCurrency(asset.currentValue)}</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Salvage Value</label>
                            <div className="p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <p className="font-medium text-foreground text-sm">{formatCurrency(asset.salvageValue)}</p>
                            </div>
                          </div>
                        </div>

                        {asset.warrantyExpiration && (
                          <div className="space-y-2">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Warranty Status</label>
                            <div className="p-3 bg-gradient-to-br from-muted/20 to-muted/12 rounded-xl border border-white/8 backdrop-blur-2xl">
                              <div className="flex items-center gap-3">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                  <p className="font-medium text-foreground text-sm">{formatDate(asset.warrantyExpiration)}</p>
                                  <p className="text-xs text-muted-foreground">Warranty expiration date</p>
                                </div>
                                {asset.warrantyExpiration > new Date() ? (
                                  <Badge className="bg-gradient-to-r from-green-500/12 to-green-600/12 text-green-600 border border-green-500/20 shadow-sm backdrop-blur-2xl">
                                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                    Active
                                  </Badge>
                                ) : (
                                  <Badge className="bg-gradient-to-r from-red-500/12 to-red-600/12 text-red-600 border border-red-500/20 shadow-sm backdrop-blur-2xl">
                                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                    Expired
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Depreciation Analysis */}
                    <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-3 text-base font-semibold">
                          <div className="p-2 bg-gradient-to-br from-orange-500/12 to-orange-600/8 rounded-lg border border-white/12 backdrop-blur-2xl group-hover:scale-110 transition-transform duration-300">
                            <TrendingDown className="h-4 w-4 text-orange-600" />
                          </div>
                          Depreciation Analysis
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Method</label>
                            <div className="p-2 bg-gradient-to-r from-orange-500/8 to-orange-600/8 rounded-lg border border-orange-500/12 backdrop-blur-2xl">
                              <p className="font-medium text-orange-600 text-sm">{asset.depreciationMethod}</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Rate</label>
                            <div className="p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <p className="font-medium text-foreground text-sm">{asset.depreciationRate}%</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Useful Life</label>
                            <div className="p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <p className="font-medium text-foreground text-sm">{asset.usefulLife} years</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Asset Age</label>
                            <div className="p-2 bg-gradient-to-r from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                              <p className="font-medium text-blue-600 text-sm">{assetAge} years</p>
                            </div>
                          </div>
                        </div>

                        <Separator className="bg-white/12" />

                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-2 bg-gradient-to-r from-orange-500/8 to-orange-600/8 rounded-lg border border-orange-500/12 backdrop-blur-2xl">
                            <span className="text-xs font-semibold text-muted-foreground">Total Depreciation</span>
                            <span className="font-bold text-base text-orange-600">{formatCurrency(depreciationAmount)}</span>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                            <span className="text-xs font-semibold text-muted-foreground">Depreciation %</span>
                            <span className="font-bold text-base text-foreground">{Math.round(depreciationPercentage)}%</span>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-gradient-to-r from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                            <span className="text-xs font-semibold text-muted-foreground">Remaining Life</span>
                            <span className="font-bold text-base text-blue-600">{remainingLife} years</span>
                          </div>
                        </div>

                        {/* Enhanced Progress Bar */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Progress</span>
                            <span className="text-sm font-bold text-orange-600">{Math.round(depreciationPercentage)}%</span>
                          </div>
                          <div className="relative">
                            <div className="w-full h-2 bg-gradient-to-r from-gray-200/30 to-gray-300/30 rounded-full border border-white/12 backdrop-blur-2xl overflow-hidden">
                              <div
                                className="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden"
                                style={{ width: `${Math.min(depreciationPercentage, 100)}%` }}
                              >
                                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                                <div className="absolute inset-0 animate-pulse bg-white/10"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Maintenance Tab */}
                <TabsContent value="maintenance" className="mt-0 space-y-6 animate-in fade-in-50 duration-500">
                  {/* Maintenance Schedule */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-yellow-500/8 to-yellow-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <Wrench className="h-4 w-4 text-yellow-600" />
                        </div>
                        Maintenance Schedule
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {asset.maintenanceSchedule ? (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-1">
                              <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Frequency</label>
                              <div className="p-2 bg-gradient-to-r from-yellow-500/8 to-yellow-600/8 rounded-lg border border-yellow-500/12 backdrop-blur-2xl">
                                <p className="font-medium text-yellow-600 text-sm capitalize">{asset.maintenanceSchedule.frequency}</p>
                              </div>
                            </div>
                            <div className="space-y-1">
                              <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Type</label>
                              <div className="p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                                <p className="font-medium text-foreground text-sm capitalize">{asset.maintenanceSchedule.maintenanceType}</p>
                              </div>
                            </div>
                            <div className="space-y-1">
                              <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Cost per Service</label>
                              <div className="p-2 bg-gradient-to-r from-emerald-500/8 to-emerald-600/8 rounded-lg border border-emerald-500/12 backdrop-blur-2xl">
                                <p className="font-bold text-base text-emerald-600">{formatCurrency(asset.maintenanceSchedule.maintenanceCost)}</p>
                              </div>
                            </div>
                            <div className="space-y-1">
                              <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Next Maintenance</label>
                              <div className="p-2 bg-gradient-to-r from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                                <p className="font-medium text-blue-600 text-sm">{formatDate(asset.maintenanceSchedule.nextMaintenance)}</p>
                              </div>
                            </div>
                          </div>

                          <Separator className="bg-white/12" />

                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Last Maintenance</span>
                              <span className="font-medium text-sm">{formatDate(performanceData.lastMaintenance)}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-orange-500/8 to-orange-600/8 rounded-lg border border-orange-500/12 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Days Since Last</span>
                              <span className="font-bold text-base text-orange-600">
                                {Math.floor((new Date().getTime() - performanceData.lastMaintenance.getTime()) / (1000 * 60 * 60 * 24))} days
                              </span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Days Until Next</span>
                              <span className="font-bold text-base text-blue-600">
                                {Math.floor((performanceData.nextMaintenance.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                              </span>
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="text-center py-8">
                          <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-2 opacity-50" />
                          <p className="text-muted-foreground">No maintenance schedule configured</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Maintenance History */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-blue-500/8 to-blue-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <Clock className="h-4 w-4 text-blue-600" />
                        </div>
                        Recent Maintenance History
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          { date: new Date('2024-03-15'), type: 'Preventive', cost: 1200, status: 'Completed', technician: 'Mike Wilson', duration: '4 hours' },
                          { date: new Date('2024-01-20'), type: 'Corrective', cost: 850, status: 'Completed', technician: 'Sarah Johnson', duration: '2.5 hours' },
                          { date: new Date('2023-11-10'), type: 'Preventive', cost: 1100, status: 'Completed', technician: 'David Brown', duration: '3 hours' },
                          { date: new Date('2023-09-05'), type: 'Inspection', cost: 300, status: 'Completed', technician: 'Emily Davis', duration: '1 hour' }
                        ].map((maintenance, index) => (
                          <div key={index} className="p-3 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl hover:bg-muted/30 transition-all duration-300">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <div className="p-2 bg-primary/8 rounded-lg border border-white/8 backdrop-blur-2xl">
                                  <Wrench className="h-4 w-4 text-primary" />
                                </div>
                                <div>
                                  <p className="font-semibold text-sm">{maintenance.type} Maintenance</p>
                                  <p className="text-xs text-muted-foreground">{formatDate(maintenance.date)}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-base text-emerald-600">{formatCurrency(maintenance.cost)}</p>
                                <Badge className="bg-gradient-to-r from-green-500/12 to-green-600/12 text-green-600 border border-green-500/20 text-xs">
                                  {maintenance.status}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span>Technician: {maintenance.technician}</span>
                              <span>Duration: {maintenance.duration}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Maintenance Insights */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-purple-500/8 to-purple-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <Activity className="h-4 w-4 text-purple-600" />
                        </div>
                        Maintenance Insights & Metrics
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div className="p-4 bg-gradient-to-br from-green-500/8 to-green-600/8 rounded-xl border border-green-500/12 backdrop-blur-2xl">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            <span className="font-semibold text-green-600 text-sm">Reliability Score</span>
                          </div>
                          <p className="text-2xl font-bold text-green-600">{performanceData.uptime}%</p>
                          <p className="text-xs text-green-600/80">Uptime performance</p>
                        </div>

                        <div className="p-4 bg-gradient-to-br from-yellow-500/8 to-yellow-600/8 rounded-xl border border-yellow-500/12 backdrop-blur-2xl">
                          <div className="flex items-center gap-2 mb-2">
                            <AlertTriangle className="h-5 w-5 text-yellow-600" />
                            <span className="font-semibold text-yellow-600 text-sm">Downtime</span>
                          </div>
                          <p className="text-2xl font-bold text-yellow-600">{performanceData.downtimeHours}h</p>
                          <p className="text-xs text-yellow-600/80">This quarter</p>
                        </div>

                        <div className="p-4 bg-gradient-to-br from-blue-500/8 to-blue-600/8 rounded-xl border border-blue-500/12 backdrop-blur-2xl">
                          <div className="flex items-center gap-2 mb-2">
                            <DollarSign className="h-5 w-5 text-blue-600" />
                            <span className="font-semibold text-blue-600 text-sm">Annual Cost</span>
                          </div>
                          <p className="text-2xl font-bold text-blue-600">{formatCurrency(performanceData.maintenanceCost)}</p>
                          <p className="text-xs text-blue-600/80">Maintenance budget</p>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-semibold text-sm">Maintenance Recommendations</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 p-2 bg-blue-50/50 dark:bg-blue-950/20 rounded border border-blue-500/20">
                            <Target className="h-4 w-4 text-blue-600" />
                            <span className="text-sm">Schedule preventive maintenance in next 30 days</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-green-50/50 dark:bg-green-950/20 rounded border border-green-500/20">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Asset performance is within optimal range</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-yellow-50/50 dark:bg-yellow-950/20 rounded border border-yellow-500/20">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm">Monitor efficiency trends for potential issues</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Performance Tab */}
                <TabsContent value="performance" className="mt-0 space-y-6 animate-in fade-in-50 duration-500">
                  {/* Performance Metrics */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-purple-500/8 to-purple-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <Activity className="h-4 w-4 text-purple-600" />
                        </div>
                        Performance Metrics
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Utilization Rate</label>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{performanceData.utilization}%</span>
                              <span className="text-xs text-muted-foreground">Target: 85%</span>
                            </div>
                            <div className="relative">
                              <div className="w-full h-2 bg-gradient-to-r from-gray-200/50 to-gray-300/50 rounded-full border border-white/12 backdrop-blur-sm overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                                  style={{ width: `${performanceData.utilization}%` }}
                                >
                                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Efficiency Score</label>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{performanceData.efficiency}%</span>
                              <span className="text-xs text-muted-foreground">Target: 90%</span>
                            </div>
                            <div className="relative">
                              <div className="w-full h-2 bg-gradient-to-r from-gray-200/50 to-gray-300/50 rounded-full border border-white/12 backdrop-blur-sm overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                                  style={{ width: `${performanceData.efficiency}%` }}
                                >
                                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Uptime</label>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{performanceData.uptime}%</span>
                              <span className="text-xs text-muted-foreground">Target: 95%</span>
                            </div>
                            <div className="relative">
                              <div className="w-full h-2 bg-gradient-to-r from-gray-200/50 to-gray-300/50 rounded-full border border-white/12 backdrop-blur-sm overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-purple-500 to-purple-600 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                                  style={{ width: `${performanceData.uptime}%` }}
                                >
                                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">Overall Score</label>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">{Math.round((performanceData.utilization + performanceData.efficiency + performanceData.uptime) / 3)}%</span>
                              <span className="text-xs text-muted-foreground">Composite</span>
                            </div>
                            <div className="relative">
                              <div className="w-full h-2 bg-gradient-to-r from-gray-200/50 to-gray-300/50 rounded-full border border-white/12 backdrop-blur-sm overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full transition-all duration-1000 ease-out shadow-sm relative overflow-hidden"
                                  style={{ width: `${Math.round((performanceData.utilization + performanceData.efficiency + performanceData.uptime) / 3)}%` }}
                                >
                                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Trends */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-indigo-500/8 to-indigo-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <BarChart3 className="h-4 w-4 text-indigo-600" />
                        </div>
                        Performance Trends
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="p-4 bg-gradient-to-br from-green-500/8 to-green-600/8 rounded-xl border border-green-500/12 backdrop-blur-2xl">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-semibold text-green-600 text-sm">This Month</span>
                            <Badge className="bg-gradient-to-r from-green-500/12 to-green-600/12 text-green-600 border border-green-500/20 text-xs">
                              +5.2%
                            </Badge>
                          </div>
                          <p className="text-xs text-green-600/80">
                            Performance improved compared to last month
                          </p>
                        </div>

                        <div className="p-4 bg-gradient-to-br from-blue-500/8 to-blue-600/8 rounded-xl border border-blue-500/12 backdrop-blur-2xl">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-semibold text-blue-600 text-sm">This Quarter</span>
                            <Badge className="bg-gradient-to-r from-blue-500/12 to-blue-600/12 text-blue-600 border border-blue-500/20 text-xs">
                              +12.8%
                            </Badge>
                          </div>
                          <p className="text-xs text-blue-600/80">
                            Significant improvement in utilization rate
                          </p>
                        </div>

                        <div className="p-4 bg-gradient-to-br from-purple-500/8 to-purple-600/8 rounded-xl border border-purple-500/12 backdrop-blur-2xl">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-semibold text-purple-600 text-sm">Year to Date</span>
                            <Badge className="bg-gradient-to-r from-purple-500/12 to-purple-600/12 text-purple-600 border border-purple-500/20 text-xs">
                              +8.4%
                            </Badge>
                          </div>
                          <p className="text-xs text-purple-600/80">
                            Consistent performance growth throughout the year
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Insights */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-amber-500/8 to-amber-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <Zap className="h-4 w-4 text-amber-600" />
                        </div>
                        Performance Insights & Recommendations
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <h4 className="font-semibold text-sm text-green-600">Strengths</h4>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 p-2 bg-green-50/50 dark:bg-green-950/20 rounded border border-green-500/20">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-xs">High reliability score ({performanceData.uptime}%)</span>
                            </div>
                            <div className="flex items-center gap-2 p-2 bg-green-50/50 dark:bg-green-950/20 rounded border border-green-500/20">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-xs">Consistent utilization rate</span>
                            </div>
                            <div className="flex items-center gap-2 p-2 bg-green-50/50 dark:bg-green-950/20 rounded border border-green-500/20">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-xs">Low maintenance costs</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-semibold text-sm text-yellow-600">Improvement Areas</h4>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 p-2 bg-yellow-50/50 dark:bg-yellow-950/20 rounded border border-yellow-500/20">
                              <AlertTriangle className="h-4 w-4 text-yellow-600" />
                              <span className="text-xs">Optimize scheduling for higher utilization</span>
                            </div>
                            <div className="flex items-center gap-2 p-2 bg-yellow-50/50 dark:bg-yellow-950/20 rounded border border-yellow-500/20">
                              <AlertTriangle className="h-4 w-4 text-yellow-600" />
                              <span className="text-xs">Consider preventive maintenance</span>
                            </div>
                            <div className="flex items-center gap-2 p-2 bg-blue-50/50 dark:bg-blue-950/20 rounded border border-blue-500/20">
                              <Target className="h-4 w-4 text-blue-600" />
                              <span className="text-xs">Monitor efficiency trends closely</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* History Tab */}
                <TabsContent value="history" className="mt-0 space-y-6 animate-in fade-in-50 duration-500">
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-indigo-500/12 to-indigo-600/8 rounded-lg border border-white/12 backdrop-blur-2xl">
                          <Clock className="h-4 w-4 text-indigo-600" />
                        </div>
                        Asset History
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {historyData.map((entry, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 border border-white/8 rounded-lg hover:bg-white/5 transition-colors backdrop-blur-2xl">
                            <div className="flex items-center justify-center w-8 h-8 bg-primary/8 rounded-full flex-shrink-0 border border-white/12 backdrop-blur-2xl">
                              {entry.action === 'Asset Created' && <FileText className="h-4 w-4 text-primary" />}
                              {entry.action === 'Assigned' && <User className="h-4 w-4 text-primary" />}
                              {entry.action === 'Maintenance' && <Wrench className="h-4 w-4 text-primary" />}
                              {entry.action === 'Location Update' && <MapPin className="h-4 w-4 text-primary" />}
                              {entry.action === 'Value Update' && <DollarSign className="h-4 w-4 text-primary" />}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="font-medium text-sm">{entry.action}</h4>
                                <span className="text-xs text-muted-foreground">{formatDate(entry.date)}</span>
                              </div>
                              <p className="text-xs text-muted-foreground mb-1">{entry.details}</p>
                              <div className="flex items-center gap-2">
                                <div className="flex items-center justify-center w-4 h-4 bg-muted/20 rounded-full border border-white/8 backdrop-blur-2xl">
                                  <User className="h-2 w-2 text-muted-foreground" />
                                </div>
                                <span className="text-xs text-muted-foreground">{entry.user}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Asset Metadata */}
                  <Card className="bg-white/3 backdrop-blur-3xl border border-white/8 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:bg-white/5 group">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-3 text-base font-semibold">
                        <div className="p-2 bg-gradient-to-br from-slate-500/8 to-slate-600/5 rounded-lg border border-white/8 backdrop-blur-3xl group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-4 w-4 text-slate-600" />
                        </div>
                        Asset Metadata & System Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h4 className="font-semibold text-sm">Creation Information</h4>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Created At</span>
                              <span className="text-xs font-medium">{formatDate(asset.createdAt)}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Created By</span>
                              <span className="text-xs font-medium">{asset.createdBy}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-blue-500/8 to-blue-600/8 rounded-lg border border-blue-500/12 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Asset ID</span>
                              <span className="text-xs font-mono font-medium text-blue-600">{asset.assetId}</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h4 className="font-semibold text-sm">Last Modified</h4>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Updated At</span>
                              <span className="text-xs font-medium">{formatDate(asset.updatedAt)}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-muted/20 to-muted/12 rounded-lg border border-white/8 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">Modified By</span>
                              <span className="text-xs font-medium">{asset.lastModifiedBy}</span>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-gradient-to-r from-orange-500/8 to-orange-600/8 rounded-lg border border-orange-500/12 backdrop-blur-2xl">
                              <span className="text-xs font-semibold text-muted-foreground">QR Code</span>
                              <span className="text-xs font-mono font-medium text-orange-600">{asset.qrCode}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </ScrollArea>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
