import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  Edit,
  Download,
  Share2,
  MapPin,
  User,
  Calendar,
  DollarSign,
  TrendingDown,
  Wrench,
  Activity,
  FileText,
  QrCode,
  Camera,
  Clock,
  AlertTriangle,
  CheckCircle,
  Target,
  BarChart3,
  Zap,
  Award
} from 'lucide-react';
import { Asset } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';

interface AssetDetailModalProps {
  asset: Asset | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (asset: Asset) => void;
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    case 'maintenance': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'retired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
  }
};

const getConditionColor = (condition: string) => {
  switch (condition.toLowerCase()) {
    case 'excellent': return 'text-green-600';
    case 'good': return 'text-blue-600';
    case 'fair': return 'text-yellow-600';
    case 'poor': return 'text-orange-600';
    case 'critical': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export const AssetDetailModal: React.FC<AssetDetailModalProps> = ({
  asset,
  isOpen,
  onClose,
  onEdit
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!asset) return null;

  // Calculate depreciation data
  const currentYear = new Date().getFullYear();
  const assetAge = currentYear - asset.purchaseDate.getFullYear();
  const depreciationAmount = asset.purchaseCost - asset.currentValue;
  const depreciationPercentage = (depreciationAmount / asset.purchaseCost) * 100;
  const remainingLife = Math.max(0, asset.usefulLife - assetAge);

  // Generate sample performance data
  const performanceData = {
    utilization: asset.utilizationRate || Math.floor(Math.random() * 40 + 60),
    efficiency: Math.floor(Math.random() * 30 + 70),
    uptime: Math.floor(Math.random() * 20 + 80),
    maintenanceCost: asset.maintenanceSchedule?.maintenanceCost || Math.floor(Math.random() * 5000 + 1000),
    downtimeHours: Math.floor(Math.random() * 50 + 10),
    lastMaintenance: asset.lastMaintenanceDate || new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    nextMaintenance: asset.nextMaintenanceDate || new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000)
  };

  // Generate sample history data
  const historyData = [
    { date: new Date('2024-01-15'), action: 'Asset Created', user: 'John Smith', details: 'Initial asset registration' },
    { date: new Date('2024-02-01'), action: 'Assigned', user: 'Sarah Johnson', details: `Assigned to ${asset.assignedUser?.name || 'Department'}` },
    { date: new Date('2024-03-15'), action: 'Maintenance', user: 'Mike Wilson', details: 'Routine preventive maintenance completed' },
    { date: new Date('2024-04-10'), action: 'Location Update', user: 'Emily Davis', details: `Moved to ${asset.location.building}` },
    { date: new Date('2024-05-20'), action: 'Value Update', user: 'David Brown', details: 'Depreciation calculation updated' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] p-0 overflow-hidden">
        <DialogHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg">
                <BarChart3 className="h-6 w-6 text-primary" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold">{asset.name}</DialogTitle>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{asset.assetId}</span>
                  <span>•</span>
                  <Badge variant="outline" className="text-xs">
                    {asset.category}
                  </Badge>
                  <span>•</span>
                  <Badge className={getStatusColor(asset.status)}>
                    {asset.status}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ContextTooltip content={{
                title: 'Edit Asset',
                description: 'Open asset editor to modify details',
                type: 'info'
              }}>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  onClick={() => onEdit?.(asset)}
                >
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
              </ContextTooltip>

              <ContextTooltip content={{
                title: 'Download Report',
                description: 'Generate and download asset report',
                type: 'info'
              }}>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              </ContextTooltip>

              <Button variant="outline" size="sm" className="gap-2">
                <Share2 className="h-4 w-4" />
                Share
              </Button>

              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="px-6 py-2 border-b border-border">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="financial">Financial</TabsTrigger>
                <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
              </TabsList>
            </div>

            <ScrollArea className="flex-1 px-6 py-4">
              {/* Overview Tab */}
              <TabsContent value="overview" className="mt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Basic Information */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Basic Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Asset ID</label>
                          <p className="font-medium">{asset.assetId}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Serial Number</label>
                          <p className="font-medium">{asset.serialNumber}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Manufacturer</label>
                          <p className="font-medium">{asset.manufacturer}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Model</label>
                          <p className="font-medium">{asset.model}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Category</label>
                          <p className="font-medium">{asset.category}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Sub-Category</label>
                          <p className="font-medium">{asset.subCategory}</p>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Condition</label>
                        <div className="flex items-center gap-2 mt-1">
                          <CheckCircle className={`h-4 w-4 ${getConditionColor(asset.condition)}`} />
                          <span className={`font-medium ${getConditionColor(asset.condition)}`}>
                            {asset.condition}
                          </span>
                        </div>
                      </div>

                      {asset.notes && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Notes</label>
                          <p className="text-sm mt-1 p-3 bg-muted/50 rounded-lg">{asset.notes}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Location & Assignment */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        Location & Assignment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Location</label>
                        <div className="mt-1 space-y-1">
                          <p className="font-medium">{asset.location.site}</p>
                          <p className="text-sm text-muted-foreground">
                            {asset.location.building} • {asset.location.floor} • {asset.location.room}
                          </p>
                        </div>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Department</label>
                        <p className="font-medium">{asset.department}</p>
                      </div>

                      {asset.assignedUser && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Assigned User</label>
                          <div className="flex items-center gap-3 mt-2 p-3 bg-muted/50 rounded-lg">
                            <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                              <User className="h-4 w-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium">{asset.assignedUser.name}</p>
                              <p className="text-sm text-muted-foreground">{asset.assignedUser.email}</p>
                              <p className="text-xs text-muted-foreground">
                                Assigned: {formatDate(asset.assignedUser.assignedDate)}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* QR Code & Tags */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <QrCode className="h-5 w-5" />
                        QR Code & Identification
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center p-8 bg-muted/50 rounded-lg">
                        <div className="text-center space-y-2">
                          <QrCode className="h-16 w-16 text-muted-foreground mx-auto" />
                          <p className="text-sm font-medium">QR Code</p>
                          <p className="text-xs text-muted-foreground">{asset.qrCode}</p>
                          <Button variant="outline" size="sm" className="gap-2">
                            <Download className="h-3 w-3" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Tags & Classification
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Tags</label>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {asset.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Attachments</label>
                          <div className="mt-2 space-y-2">
                            {asset.attachments.length > 0 ? (
                              asset.attachments.map((attachment) => (
                                <div key={attachment.id} className="flex items-center gap-2 p-2 bg-muted/50 rounded">
                                  <FileText className="h-4 w-4 text-muted-foreground" />
                                  <span className="text-sm font-medium">{attachment.name}</span>
                                  <Button variant="ghost" size="sm">
                                    <Download className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground">No attachments</p>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Financial Tab */}
              <TabsContent value="financial" className="mt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Purchase Information */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5" />
                        Purchase Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Purchase Date</label>
                          <p className="font-medium">{formatDate(asset.purchaseDate)}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Purchase Cost</label>
                          <p className="font-medium text-lg">{formatCurrency(asset.purchaseCost)}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Current Value</label>
                          <p className="font-medium text-lg text-blue-600">{formatCurrency(asset.currentValue)}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Salvage Value</label>
                          <p className="font-medium">{formatCurrency(asset.salvageValue)}</p>
                        </div>
                      </div>

                      {asset.warrantyExpiration && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Warranty Expiration</label>
                          <div className="flex items-center gap-2 mt-1">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{formatDate(asset.warrantyExpiration)}</span>
                            {asset.warrantyExpiration > new Date() ? (
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Active
                              </Badge>
                            ) : (
                              <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Expired
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Depreciation Analysis */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingDown className="h-5 w-5" />
                        Depreciation Analysis
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Method</label>
                          <p className="font-medium">{asset.depreciationMethod}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Rate</label>
                          <p className="font-medium">{asset.depreciationRate}%</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Useful Life</label>
                          <p className="font-medium">{asset.usefulLife} years</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Asset Age</label>
                          <p className="font-medium">{assetAge} years</p>
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Total Depreciation</span>
                          <span className="font-medium text-orange-600">{formatCurrency(depreciationAmount)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Depreciation %</span>
                          <span className="font-medium">{Math.round(depreciationPercentage)}%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">Remaining Life</span>
                          <span className="font-medium text-blue-600">{remainingLife} years</span>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                        <div
                          className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(depreciationPercentage, 100)}%` }}
                        ></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Financial Performance */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Financial Performance Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center gap-2 mb-2">
                          <Award className="h-5 w-5 text-blue-600" />
                          <span className="font-semibold text-blue-800 dark:text-blue-200">ROI Analysis</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-600">
                          {(((asset.currentValue - asset.purchaseCost) / asset.purchaseCost) * 100).toFixed(1)}%
                        </p>
                        <p className="text-sm text-blue-700 dark:text-blue-300">Return on Investment</p>
                      </div>

                      <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="flex items-center gap-2 mb-2">
                          <DollarSign className="h-5 w-5 text-green-600" />
                          <span className="font-semibold text-green-800 dark:text-green-200">Annual TCO</span>
                        </div>
                        <p className="text-2xl font-bold text-green-600">
                          {formatCurrency((asset.purchaseCost + (performanceData.maintenanceCost * assetAge)) / Math.max(assetAge, 1))}
                        </p>
                        <p className="text-sm text-green-700 dark:text-green-300">Total Cost of Ownership</p>
                      </div>

                      <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
                        <div className="flex items-center gap-2 mb-2">
                          <Target className="h-5 w-5 text-purple-600" />
                          <span className="font-semibold text-purple-800 dark:text-purple-200">Value Retention</span>
                        </div>
                        <p className="text-2xl font-bold text-purple-600">
                          {Math.round((asset.currentValue / asset.purchaseCost) * 100)}%
                        </p>
                        <p className="text-sm text-purple-700 dark:text-purple-300">Of original value</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Maintenance Tab */}
              <TabsContent value="maintenance" className="mt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Maintenance Schedule */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Wrench className="h-5 w-5" />
                        Maintenance Schedule
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {asset.maintenanceSchedule ? (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Frequency</label>
                              <p className="font-medium capitalize">{asset.maintenanceSchedule.frequency}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Type</label>
                              <p className="font-medium capitalize">{asset.maintenanceSchedule.maintenanceType}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Cost per Service</label>
                              <p className="font-medium">{formatCurrency(asset.maintenanceSchedule.maintenanceCost)}</p>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Next Maintenance</label>
                              <p className="font-medium">{formatDate(asset.maintenanceSchedule.nextMaintenance)}</p>
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-muted-foreground">Last Maintenance</span>
                              <span className="font-medium">{formatDate(performanceData.lastMaintenance)}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-muted-foreground">Days Since Last</span>
                              <span className="font-medium">
                                {Math.floor((new Date().getTime() - performanceData.lastMaintenance.getTime()) / (1000 * 60 * 60 * 24))} days
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-muted-foreground">Days Until Next</span>
                              <span className="font-medium text-blue-600">
                                {Math.floor((performanceData.nextMaintenance.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                              </span>
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="text-center py-8">
                          <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                          <p className="text-muted-foreground">No maintenance schedule configured</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Maintenance History */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        Recent Maintenance
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          { date: new Date('2024-03-15'), type: 'Preventive', cost: 1200, status: 'Completed' },
                          { date: new Date('2024-01-20'), type: 'Corrective', cost: 850, status: 'Completed' },
                          { date: new Date('2023-11-10'), type: 'Preventive', cost: 1100, status: 'Completed' },
                          { date: new Date('2023-09-05'), type: 'Inspection', cost: 300, status: 'Completed' }
                        ].map((maintenance, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                                <Wrench className="h-4 w-4 text-primary" />
                              </div>
                              <div>
                                <p className="font-medium">{maintenance.type}</p>
                                <p className="text-sm text-muted-foreground">{formatDate(maintenance.date)}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">{formatCurrency(maintenance.cost)}</p>
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs">
                                {maintenance.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Maintenance Insights */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Maintenance Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="font-semibold text-green-800 dark:text-green-200">Reliability Score</span>
                        </div>
                        <p className="text-2xl font-bold text-green-600">{performanceData.uptime}%</p>
                        <p className="text-sm text-green-700 dark:text-green-300">Uptime performance</p>
                      </div>

                      <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="h-5 w-5 text-yellow-600" />
                          <span className="font-semibold text-yellow-800 dark:text-yellow-200">Downtime</span>
                        </div>
                        <p className="text-2xl font-bold text-yellow-600">{performanceData.downtimeHours}h</p>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300">This quarter</p>
                      </div>

                      <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center gap-2 mb-2">
                          <DollarSign className="h-5 w-5 text-blue-600" />
                          <span className="font-semibold text-blue-800 dark:text-blue-200">Maintenance Cost</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-600">{formatCurrency(performanceData.maintenanceCost)}</p>
                        <p className="text-sm text-blue-700 dark:text-blue-300">Annual average</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Performance Tab */}
              <TabsContent value="performance" className="mt-0 space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Performance Metrics */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        Performance Metrics
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Utilization Rate</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${performanceData.utilization}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-blue-600">{performanceData.utilization}%</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Efficiency Score</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${performanceData.efficiency}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-green-600">{performanceData.efficiency}%</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Uptime</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${performanceData.uptime}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-purple-600">{performanceData.uptime}%</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Overall Score</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                              <div
                                className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.round((performanceData.utilization + performanceData.efficiency + performanceData.uptime) / 3)}%` }}
                              ></div>
                            </div>
                            <span className="font-medium text-orange-600">
                              {Math.round((performanceData.utilization + performanceData.efficiency + performanceData.uptime) / 3)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Trends */}
                  <Card className="glass-card">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Performance Trends
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-green-800 dark:text-green-200">This Month</span>
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              +5.2%
                            </Badge>
                          </div>
                          <p className="text-sm text-green-700 dark:text-green-300">
                            Performance improved compared to last month
                          </p>
                        </div>

                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-blue-800 dark:text-blue-200">This Quarter</span>
                            <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              +12.8%
                            </Badge>
                          </div>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            Significant improvement in utilization rate
                          </p>
                        </div>

                        <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-purple-800 dark:text-purple-200">Year to Date</span>
                            <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                              +8.4%
                            </Badge>
                          </div>
                          <p className="text-sm text-purple-700 dark:text-purple-300">
                            Consistent performance growth throughout the year
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Performance Insights */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Performance Insights & Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">Strengths</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">High reliability score ({performanceData.uptime}%)</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Consistent utilization rate</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Low maintenance costs</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-medium">Improvement Areas</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-950/20 rounded">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm">Optimize scheduling for higher utilization</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-950/20 rounded">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm">Consider preventive maintenance</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-950/20 rounded">
                            <Target className="h-4 w-4 text-blue-600" />
                            <span className="text-sm">Monitor efficiency trends closely</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* History Tab */}
              <TabsContent value="history" className="mt-0 space-y-6">
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Asset History & Activity Log
                    </CardTitle>
                    <CardDescription>
                      Complete timeline of asset activities and changes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {historyData.map((entry, index) => (
                        <div key={index} className="flex items-start gap-4 p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                          <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full flex-shrink-0">
                            {entry.action === 'Asset Created' && <FileText className="h-5 w-5 text-primary" />}
                            {entry.action === 'Assigned' && <User className="h-5 w-5 text-primary" />}
                            {entry.action === 'Maintenance' && <Wrench className="h-5 w-5 text-primary" />}
                            {entry.action === 'Location Update' && <MapPin className="h-5 w-5 text-primary" />}
                            {entry.action === 'Value Update' && <DollarSign className="h-5 w-5 text-primary" />}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium">{entry.action}</h4>
                              <span className="text-sm text-muted-foreground">{formatDate(entry.date)}</span>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">{entry.details}</p>
                            <div className="flex items-center gap-2">
                              <div className="flex items-center justify-center w-6 h-6 bg-muted rounded-full">
                                <User className="h-3 w-3 text-muted-foreground" />
                              </div>
                              <span className="text-xs text-muted-foreground">{entry.user}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Asset Metadata */}
                <Card className="glass-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Asset Metadata
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <h4 className="font-medium">Creation Information</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Created At</span>
                            <span className="text-sm font-medium">{formatDate(asset.createdAt)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Created By</span>
                            <span className="text-sm font-medium">{asset.createdBy}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Asset ID</span>
                            <span className="text-sm font-medium font-mono">{asset.assetId}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-medium">Last Modified</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Updated At</span>
                            <span className="text-sm font-medium">{formatDate(asset.updatedAt)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Modified By</span>
                            <span className="text-sm font-medium">{asset.lastModifiedBy}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">QR Code</span>
                            <span className="text-sm font-medium font-mono">{asset.qrCode}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
