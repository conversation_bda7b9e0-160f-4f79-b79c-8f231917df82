
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { StatCard } from "@/components/dashboard/StatCard";
import { Plus, Search, Filter, Calendar, User, Wrench, AlertTriangle, Clock, CheckCircle } from "lucide-react";
import { useState } from "react";

const WorkOrders = () => {
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPriority, setSelectedPriority] = useState("all");

  const workOrders = [
    {
      id: "WO-001",
      title: "Pump Station A - Bearing Replacement",
      asset: "AST-001",
      assetName: "Pump Station A",
      priority: "high",
      status: "in-progress",
      assignedTo: "John <PERSON>",
      createdDate: "2024-01-15",
      dueDate: "2024-01-17",
      estimatedHours: 8,
      actualHours: 6,
      description: "Replace worn bearings in main pump unit"
    },
    {
      id: "WO-002",
      title: "HVAC Unit 12 - Filter Replacement",
      asset: "AST-004",
      assetName: "HVAC Unit 12",
      priority: "medium",
      status: "scheduled",
      assignedTo: "Mike Johnson",
      createdDate: "2024-01-14",
      dueDate: "2024-01-20",
      estimatedHours: 2,
      actualHours: 0,
      description: "Quarterly filter replacement and system check"
    },
    {
      id: "WO-003",
      title: "Conveyor Belt B2 - Emergency Repair",
      asset: "AST-003",
      assetName: "Conveyor Belt B2",
      priority: "critical",
      status: "open",
      assignedTo: "Emergency Team",
      createdDate: "2024-01-15",
      dueDate: "2024-01-15",
      estimatedHours: 4,
      actualHours: 0,
      description: "Belt motor failure - immediate attention required"
    },
    {
      id: "WO-004",
      title: "Generator Unit 3 - Preventive Maintenance",
      asset: "AST-002",
      assetName: "Generator Unit 3",
      priority: "medium",
      status: "completed",
      assignedTo: "Sarah Wilson",
      createdDate: "2024-01-12",
      dueDate: "2024-01-14",
      estimatedHours: 6,
      actualHours: 5.5,
      description: "Monthly preventive maintenance and oil change"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open": return "text-blue-600 bg-blue-100";
      case "in-progress": return "text-yellow-600 bg-yellow-100";
      case "scheduled": return "text-purple-600 bg-purple-100";
      case "completed": return "text-green-600 bg-green-100";
      case "cancelled": return "text-gray-600 bg-gray-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical": return "text-red-600 bg-red-100";
      case "high": return "text-orange-600 bg-orange-100";
      case "medium": return "text-yellow-600 bg-yellow-100";
      case "low": return "text-green-600 bg-green-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Work Orders</h1>
            <p className="text-muted-foreground">Manage maintenance tasks and requests</p>
          </div>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            New Work Order
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Work Orders"
            value="156"
            icon={<Wrench className="text-blue-600 h-4 w-4" />}
            description="This month"
          />
          <StatCard
            title="In Progress"
            value="24"
            icon={<Clock className="text-yellow-600 h-4 w-4" />}
            description="Active tasks"
          />
          <StatCard
            title="Completed"
            value="98"
            icon={<CheckCircle className="text-green-600 h-4 w-4" />}
            description="This month"
          />
          <StatCard
            title="Critical/High"
            value="12"
            icon={<AlertTriangle className="text-red-600 h-4 w-4" />}
            description="Priority items"
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Work Order Filters</CardTitle>
            <CardDescription>Filter work orders by status, priority, and assignee</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search work orders..." className="pl-10" />
                </div>
              </div>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Work Order Management</CardTitle>
            <CardDescription>Current maintenance work orders and their status</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Work Order</TableHead>
                  <TableHead>Asset</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Assigned To</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {workOrders.map((wo) => (
                  <TableRow key={wo.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{wo.id}</div>
                        <div className="text-sm text-muted-foreground">{wo.title}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{wo.asset}</div>
                        <div className="text-sm text-muted-foreground">{wo.assetName}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPriorityColor(wo.priority)}>
                        {wo.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(wo.status)}>
                        {wo.status.replace('-', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        {wo.assignedTo}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {wo.dueDate}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(wo.actualHours / wo.estimatedHours) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm">{wo.actualHours}/{wo.estimatedHours}h</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">View</Button>
                        <Button variant="ghost" size="sm">Edit</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default WorkOrders;
