
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { StatCard } from "@/components/dashboard/StatCard";
import { BarChart } from "@/components/dashboard/BarChart";
import { AreaChart } from "@/components/dashboard/AreaChart";
import { DonutChart } from "@/components/dashboard/DonutChart";
import { ContextTooltip, tooltipContent } from "@/components/ui/ContextTooltip";
import { useNotifications, notificationHelpers } from "@/components/ui/NotificationSystem";
import { useCommandPalette } from "@/hooks/use-command-palette";
import {
  ChevronRight,
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Package,
  Wrench,
  Activity,
  AlertTriangle,
  Zap,
  Shield,
  Command,
  Bell,
  Sparkles
} from "lucide-react";

const Index = () => {
  const navigate = useNavigate();
  const { addNotification } = useNotifications();
  const { open: openCommandPalette } = useCommandPalette();

  // Demo notification function
  const showDemoNotification = () => {
    addNotification(notificationHelpers.success(
      'Welcome to Toshka!',
      'Your advanced financial insight hub is ready to use.',
      {
        actions: [
          {
            label: 'Get Started',
            action: () => navigate('/financial'),
            variant: 'default'
          },
          {
            label: 'Learn More',
            action: () => { },
            variant: 'outline'
          }
        ],
        category: 'Welcome',
        source: 'Dashboard'
      }
    ));
  };

  const performanceData = [
    { name: "Jan", revenue: 2400000, expenses: 1800000, profit: 600000 },
    { name: "Feb", revenue: 2800000, expenses: 2000000, profit: 800000 },
    { name: "Mar", revenue: 3200000, expenses: 2200000, profit: 1000000 },
    { name: "Apr", revenue: 3100000, expenses: 2100000, profit: 1000000 },
    { name: "May", revenue: 3600000, expenses: 2400000, profit: 1200000 },
    { name: "Jun", revenue: 4200000, expenses: 2800000, profit: 1400000 }
  ];

  const assetUtilization = [
    { name: "Production", utilization: 88, target: 90, efficiency: 92 },
    { name: "Maintenance", utilization: 75, target: 80, efficiency: 85 },
    { name: "Quality", utilization: 95, target: 95, efficiency: 96 },
    { name: "Safety", utilization: 98, target: 100, efficiency: 99 }
  ];

  const departmentPerformance = [
    { name: "Operations", value: 4200000, color: "#8B5CF6" },
    { name: "Maintenance", value: 2800000, color: "#10B981" },
    { name: "Administration", value: 1800000, color: "#F59E0B" },
    { name: "R&D", value: 1200000, color: "#EF4444" },
    { name: "Sales", value: 2400000, color: "#3B82F6" }
  ];

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary via-primary/90 to-primary/80 p-8 text-white">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
              <div className="space-y-4">
                <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  System Status: Operational
                </div>
                <h1 className="text-4xl lg:text-5xl font-bold leading-tight text-shadow-lg">
                  Toshka Financial
                  <br />
                  <span className="bg-gradient-to-r from-yellow-200 to-orange-200 bg-clip-text text-transparent">
                    Insight Hub
                  </span>
                </h1>
                <p className="text-xl text-white/90 max-w-2xl leading-relaxed">
                  Advanced enterprise management platform with real-time analytics,
                  comprehensive reporting, and intelligent automation for optimal business performance.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-white text-primary hover:bg-white/90 font-semibold gap-2 shadow-lg"
                  onClick={() => navigate('/financial')}
                >
                  <PieChart size={20} />
                  Launch Dashboard
                  <ChevronRight size={16} />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                  onClick={() => navigate('/analytics/dashboards')}
                >
                  View Analytics
                </Button>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        {/* Key Metrics with Enhanced Tooltips */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ContextTooltip content={tooltipContent.financial.revenue}>
            <div className="glass-morphism">
              <StatCard
                title="Total Revenue"
                value="$4.2M"
                icon={<DollarSign className="text-green-600 h-5 w-5" />}
                description="+12.5% from last month"
                trend="up"
              />
            </div>
          </ContextTooltip>

          <ContextTooltip content={tooltipContent.assets.utilization}>
            <div className="neumorphism">
              <StatCard
                title="Active Assets"
                value="2,847"
                icon={<Package className="text-blue-600 h-5 w-5" />}
                description="98.5% operational"
                trend="up"
              />
            </div>
          </ContextTooltip>

          <ContextTooltip content={tooltipContent.performance.efficiency}>
            <div className="floating-shadow">
              <StatCard
                title="Efficiency Rate"
                value="94.2%"
                icon={<Zap className="text-yellow-600 h-5 w-5" />}
                description="****% from target"
                trend="up"
              />
            </div>
          </ContextTooltip>

          <ContextTooltip content={tooltipContent.safety.compliance}>
            <div className="gradient-border">
              <StatCard
                title="Safety Score"
                value="99.7%"
                icon={<Shield className="text-green-600 h-5 w-5" />}
                description="Excellent rating"
                trend="stable"
              />
            </div>
          </ContextTooltip>
        </div>

        {/* Enhanced Quick Actions */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              Quick Actions
              <div className="ml-auto flex gap-2">
                <ContextTooltip content={{
                  description: "Open command palette for quick navigation and actions",
                  type: "tip",
                  shortcut: "Ctrl+K"
                }}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openCommandPalette}
                    className="gap-2"
                  >
                    <Command className="h-4 w-4" />
                    <span className="hidden sm:inline">Command Palette</span>
                  </Button>
                </ContextTooltip>

                <ContextTooltip content={{
                  description: "Show a demo notification with advanced features",
                  type: "info"
                }}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={showDemoNotification}
                    className="gap-2"
                  >
                    <Bell className="h-4 w-4" />
                    <span className="hidden sm:inline">Demo Alert</span>
                  </Button>
                </ContextTooltip>
              </div>
            </CardTitle>
            <CardDescription>
              Frequently used operations and shortcuts with enhanced interactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {[
                { icon: Wrench, label: "New Work Order", path: "/maintenance/work-orders", color: "bg-orange-500", effect: "magnetic-hover" },
                { icon: Users, label: "Employee Portal", path: "/hr/employees", color: "bg-blue-500", effect: "tilt-hover" },
                { icon: Package, label: "Asset Tracking", path: "/assets/tracking", color: "bg-green-500", effect: "floating-shadow" },
                { icon: DollarSign, label: "Financial Reports", path: "/analytics/financial", color: "bg-purple-500", effect: "animate-glow" },
                { icon: AlertTriangle, label: "Safety Alerts", path: "/hse/incidents", color: "bg-red-500", effect: "animate-pulse-slow" },
                { icon: PieChart, label: "Analytics", path: "/analytics/dashboards", color: "bg-indigo-500", effect: "holographic" }
              ].map((action, index) => (
                <ContextTooltip
                  key={index}
                  content={{
                    title: action.label,
                    description: `Navigate to ${action.label.toLowerCase()} section`,
                    type: "info",
                    shortcut: `Alt+${index + 1}`
                  }}
                  placement="top"
                >
                  <Button
                    variant="ghost"
                    className={`h-20 flex-col gap-2 hover:bg-accent/50 transition-all duration-200 hover:scale-105 ${action.effect}`}
                    onClick={() => navigate(action.path)}
                  >
                    <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center text-white`}>
                      <action.icon size={16} />
                    </div>
                    <span className="text-xs font-medium text-center">{action.label}</span>
                  </Button>
                </ContextTooltip>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Performance Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AreaChart
            title="Financial Performance"
            description="Monthly revenue, expenses, and profit trends"
            data={performanceData}
            categories={[
              { name: "revenue", color: "#3B82F6" },
              { name: "profit", color: "#10B981" },
              { name: "expenses", color: "#EF4444" }
            ]}
            height={350}
          />
          <BarChart
            title="Operational Metrics"
            description="Key performance indicators across departments"
            data={assetUtilization}
            categories={[
              { name: "utilization", color: "#8B5CF6" },
              { name: "target", color: "#E5E7EB" },
              { name: "efficiency", color: "#10B981" }
            ]}
            height={350}
          />
        </div>

        {/* Department Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card className="chart-container">
              <CardHeader>
                <CardTitle>System Alerts & Notifications</CardTitle>
                <CardDescription>Recent system events and status updates</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  {
                    type: "success",
                    title: "Maintenance Completed",
                    description: "Pump unit PU-001 maintenance completed successfully",
                    time: "2 minutes ago",
                    icon: Wrench,
                    color: "text-green-600"
                  },
                  {
                    type: "warning",
                    title: "Budget Alert",
                    description: "Q4 operational budget reached 85% threshold",
                    time: "15 minutes ago",
                    icon: AlertTriangle,
                    color: "text-yellow-600"
                  },
                  {
                    type: "info",
                    title: "Asset Update",
                    description: "New equipment registered in inventory system",
                    time: "1 hour ago",
                    icon: Package,
                    color: "text-blue-600"
                  },
                  {
                    type: "success",
                    title: "Safety Milestone",
                    description: "500 days without incidents achieved",
                    time: "3 hours ago",
                    icon: Shield,
                    color: "text-green-600"
                  }
                ].map((alert, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-accent/30 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer">
                    <div className={`mt-1 ${alert.color}`}>
                      <alert.icon size={16} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm">{alert.title}</div>
                      <div className="text-sm text-muted-foreground">{alert.description}</div>
                      <div className="text-xs text-muted-foreground mt-1">{alert.time}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
          <DonutChart
            title="Department Budget"
            description="Annual budget allocation by department"
            data={departmentPerformance}
            height={400}
          />
        </div>
      </div>
    </AppLayout>
  );
};

export default Index;
