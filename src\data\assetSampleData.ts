import { Asset, AssetCategory, AssetStatus, AssetCondition, DepreciationMethod, AssetLocation, AssignedUser } from '@/types/asset';

// Sample data generators for realistic asset management
export const ASSET_CATEGORIES: AssetCategory[] = [
  'IT Equipment',
  'Machinery',
  'Vehicles',
  'Furniture',
  'Tools',
  'Safety Equipment',
  'Office Equipment',
  'Medical Equipment',
  'Laboratory Equipment',
  'HVAC Systems',
  'Security Systems',
  'Communication Equipment',
  'Manufacturing Equipment',
  'Warehouse Equipment',
  'Facilities',
  'Software Licenses',
  'Other'
];

export const ASSET_SUBCATEGORIES: Record<AssetCategory, string[]> = {
  'IT Equipment': ['Laptops', 'Desktops', 'Servers', 'Monitors', 'Printers', 'Tablets', 'Smartphones', 'Network Equipment', 'Storage Devices'],
  'Machinery': ['CNC Machines', 'Lathes', 'Milling Machines', 'Presses', 'Welding Equipment', 'Compressors', 'Generators'],
  'Vehicles': ['Cars', 'Trucks', 'Vans', 'Forklifts', 'Motorcycles', 'Trailers', 'Buses'],
  'Furniture': ['Desks', 'Chairs', 'Tables', 'Cabinets', 'Shelving', 'Sofas', 'Conference Tables'],
  'Tools': ['Hand Tools', 'Power Tools', 'Measuring Instruments', 'Cutting Tools', 'Lifting Equipment'],
  'Safety Equipment': ['Fire Extinguishers', 'Safety Helmets', 'Protective Clothing', 'Emergency Equipment', 'First Aid Kits'],
  'Office Equipment': ['Copiers', 'Scanners', 'Shredders', 'Projectors', 'Whiteboards', 'Telephones'],
  'Medical Equipment': ['X-Ray Machines', 'Ultrasound', 'Patient Monitors', 'Defibrillators', 'Surgical Instruments'],
  'Laboratory Equipment': ['Microscopes', 'Centrifuges', 'Spectrometers', 'Incubators', 'Balances'],
  'HVAC Systems': ['Air Conditioners', 'Heaters', 'Ventilation Systems', 'Chillers', 'Boilers'],
  'Security Systems': ['Cameras', 'Access Control', 'Alarm Systems', 'Metal Detectors', 'Safes'],
  'Communication Equipment': ['Radios', 'Intercoms', 'PA Systems', 'Satellite Equipment', 'Antennas'],
  'Manufacturing Equipment': ['Assembly Lines', 'Conveyor Belts', 'Packaging Machines', 'Quality Control Equipment'],
  'Warehouse Equipment': ['Pallet Jacks', 'Shelving Systems', 'Loading Docks', 'Sorting Equipment'],
  'Facilities': ['Buildings', 'Parking Lots', 'Landscaping', 'Infrastructure'],
  'Software Licenses': ['Operating Systems', 'Productivity Software', 'Design Software', 'Security Software'],
  'Other': ['Miscellaneous', 'Specialized Equipment', 'Temporary Assets']
};

export const MANUFACTURERS = [
  'Dell', 'HP', 'Lenovo', 'Apple', 'Microsoft', 'Cisco', 'IBM', 'Oracle',
  'Caterpillar', 'John Deere', 'Toyota', 'Ford', 'Mercedes-Benz', 'Volvo',
  'Steelcase', 'Herman Miller', 'IKEA', 'Haworth', 'Knoll',
  'Bosch', 'Makita', 'DeWalt', 'Milwaukee', 'Snap-on',
  'Honeywell', 'Johnson Controls', 'Carrier', 'Trane', 'Daikin',
  'Siemens', 'GE', 'Philips', 'Canon', 'Xerox', 'Epson'
];

export const DEPARTMENTS = [
  'Information Technology',
  'Human Resources',
  'Finance & Accounting',
  'Operations',
  'Manufacturing',
  'Research & Development',
  'Sales & Marketing',
  'Customer Service',
  'Quality Assurance',
  'Facilities Management',
  'Security',
  'Legal',
  'Procurement',
  'Logistics',
  'Maintenance'
];

export const LOCATIONS: AssetLocation[] = [
  { site: 'Headquarters', building: 'Main Building', floor: '1st Floor', room: 'Lobby' },
  { site: 'Headquarters', building: 'Main Building', floor: '1st Floor', room: 'Reception' },
  { site: 'Headquarters', building: 'Main Building', floor: '2nd Floor', room: 'IT Department' },
  { site: 'Headquarters', building: 'Main Building', floor: '2nd Floor', room: 'Server Room' },
  { site: 'Headquarters', building: 'Main Building', floor: '3rd Floor', room: 'Executive Offices' },
  { site: 'Headquarters', building: 'Main Building', floor: '3rd Floor', room: 'Conference Room A' },
  { site: 'Headquarters', building: 'Main Building', floor: '4th Floor', room: 'HR Department' },
  { site: 'Headquarters', building: 'Main Building', floor: '4th Floor', room: 'Finance Department' },
  { site: 'Manufacturing Plant', building: 'Production Facility', floor: 'Ground Floor', room: 'Assembly Line 1' },
  { site: 'Manufacturing Plant', building: 'Production Facility', floor: 'Ground Floor', room: 'Assembly Line 2' },
  { site: 'Manufacturing Plant', building: 'Production Facility', floor: 'Ground Floor', room: 'Quality Control' },
  { site: 'Manufacturing Plant', building: 'Warehouse', floor: 'Ground Floor', room: 'Storage Area A' },
  { site: 'Manufacturing Plant', building: 'Warehouse', floor: 'Ground Floor', room: 'Storage Area B' },
  { site: 'Manufacturing Plant', building: 'Warehouse', floor: 'Ground Floor', room: 'Shipping Dock' },
  { site: 'Branch Office', building: 'Sales Center', floor: '1st Floor', room: 'Sales Floor' },
  { site: 'Branch Office', building: 'Sales Center', floor: '2nd Floor', room: 'Training Room' },
  { site: 'Research Facility', building: 'Lab Building', floor: '1st Floor', room: 'Lab 1' },
  { site: 'Research Facility', building: 'Lab Building', floor: '1st Floor', room: 'Lab 2' },
  { site: 'Research Facility', building: 'Lab Building', floor: '2nd Floor', room: 'Clean Room' },
  { site: 'Remote Site', building: 'Field Office', floor: 'Ground Floor', room: 'Operations Center' }
];

export const SAMPLE_USERS: AssignedUser[] = [
  { id: '1', name: 'John Smith', email: '<EMAIL>', department: 'Information Technology', assignedDate: new Date('2024-01-15') },
  { id: '2', name: 'Sarah Johnson', email: '<EMAIL>', department: 'Human Resources', assignedDate: new Date('2024-02-01') },
  { id: '3', name: 'Michael Brown', email: '<EMAIL>', department: 'Finance & Accounting', assignedDate: new Date('2024-01-20') },
  { id: '4', name: 'Emily Davis', email: '<EMAIL>', department: 'Operations', assignedDate: new Date('2024-02-10') },
  { id: '5', name: 'David Wilson', email: '<EMAIL>', department: 'Manufacturing', assignedDate: new Date('2024-01-25') },
  { id: '6', name: 'Lisa Anderson', email: '<EMAIL>', department: 'Research & Development', assignedDate: new Date('2024-02-05') },
  { id: '7', name: 'Robert Taylor', email: '<EMAIL>', department: 'Sales & Marketing', assignedDate: new Date('2024-01-30') },
  { id: '8', name: 'Jennifer Martinez', email: '<EMAIL>', department: 'Customer Service', assignedDate: new Date('2024-02-15') },
  { id: '9', name: 'Christopher Lee', email: '<EMAIL>', department: 'Quality Assurance', assignedDate: new Date('2024-01-10') },
  { id: '10', name: 'Amanda White', email: '<EMAIL>', department: 'Facilities Management', assignedDate: new Date('2024-02-20') }
];

// Utility functions for generating realistic data
export const generateAssetId = (index: number): string => {
  const year = new Date().getFullYear();
  const paddedIndex = index.toString().padStart(4, '0');
  return `AST-${year}-${paddedIndex}`;
};

export const generateQRCode = (assetId: string): string => {
  return `QR-${assetId}`;
};

export const getRandomElement = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

export const getRandomElements = <T>(array: T[], count: number): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const generateRandomDate = (start: Date, end: Date): Date => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

export const generateRandomPrice = (min: number, max: number): number => {
  return Math.round((Math.random() * (max - min) + min) * 100) / 100;
};

export const calculateCurrentValue = (purchaseCost: number, purchaseDate: Date, depreciationRate: number): number => {
  const yearsOld = (new Date().getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
  const depreciatedValue = purchaseCost * Math.pow(1 - depreciationRate / 100, yearsOld);
  return Math.max(depreciatedValue, purchaseCost * 0.1); // Minimum 10% of original value
};

export const generateMaintenanceDate = (purchaseDate: Date): Date => {
  const monthsToAdd = Math.floor(Math.random() * 12) + 1;
  const maintenanceDate = new Date(purchaseDate);
  maintenanceDate.setMonth(maintenanceDate.getMonth() + monthsToAdd);
  return maintenanceDate;
};

export const generateWarrantyDate = (purchaseDate: Date): Date => {
  const yearsToAdd = Math.floor(Math.random() * 5) + 1; // 1-5 years warranty
  const warrantyDate = new Date(purchaseDate);
  warrantyDate.setFullYear(warrantyDate.getFullYear() + yearsToAdd);
  return warrantyDate;
};

// Asset generation templates by category
export const ASSET_TEMPLATES: Record<AssetCategory, {
  priceRange: [number, number];
  depreciationRate: number;
  usefulLife: number;
  commonTags: string[];
}> = {
  'IT Equipment': { priceRange: [500, 5000], depreciationRate: 25, usefulLife: 4, commonTags: ['Technology', 'Computing', 'Digital'] },
  'Machinery': { priceRange: [10000, 100000], depreciationRate: 10, usefulLife: 15, commonTags: ['Industrial', 'Production', 'Heavy Equipment'] },
  'Vehicles': { priceRange: [20000, 80000], depreciationRate: 15, usefulLife: 8, commonTags: ['Transportation', 'Fleet', 'Mobile'] },
  'Furniture': { priceRange: [200, 2000], depreciationRate: 8, usefulLife: 10, commonTags: ['Office', 'Workspace', 'Comfort'] },
  'Tools': { priceRange: [50, 1000], depreciationRate: 12, usefulLife: 7, commonTags: ['Manual', 'Precision', 'Maintenance'] },
  'Safety Equipment': { priceRange: [100, 5000], depreciationRate: 15, usefulLife: 5, commonTags: ['Safety', 'Protection', 'Emergency'] },
  'Office Equipment': { priceRange: [300, 3000], depreciationRate: 20, usefulLife: 6, commonTags: ['Office', 'Productivity', 'Business'] },
  'Medical Equipment': { priceRange: [5000, 50000], depreciationRate: 12, usefulLife: 10, commonTags: ['Medical', 'Healthcare', 'Diagnostic'] },
  'Laboratory Equipment': { priceRange: [2000, 25000], depreciationRate: 15, usefulLife: 8, commonTags: ['Research', 'Scientific', 'Analysis'] },
  'HVAC Systems': { priceRange: [3000, 30000], depreciationRate: 8, usefulLife: 15, commonTags: ['Climate', 'Comfort', 'Energy'] },
  'Security Systems': { priceRange: [500, 10000], depreciationRate: 18, usefulLife: 7, commonTags: ['Security', 'Surveillance', 'Protection'] },
  'Communication Equipment': { priceRange: [200, 5000], depreciationRate: 20, usefulLife: 6, commonTags: ['Communication', 'Network', 'Connectivity'] },
  'Manufacturing Equipment': { priceRange: [15000, 200000], depreciationRate: 10, usefulLife: 20, commonTags: ['Manufacturing', 'Production', 'Automation'] },
  'Warehouse Equipment': { priceRange: [1000, 15000], depreciationRate: 12, usefulLife: 10, commonTags: ['Storage', 'Logistics', 'Material Handling'] },
  'Facilities': { priceRange: [50000, 500000], depreciationRate: 3, usefulLife: 30, commonTags: ['Infrastructure', 'Building', 'Facility'] },
  'Software Licenses': { priceRange: [100, 10000], depreciationRate: 33, usefulLife: 3, commonTags: ['Software', 'License', 'Digital'] },
  'Other': { priceRange: [100, 5000], depreciationRate: 15, usefulLife: 7, commonTags: ['Miscellaneous', 'General', 'Utility'] }
};

// Generate comprehensive sample asset data
export const generateSampleAssets = (count: number = 75): Asset[] => {
  const assets: Asset[] = [];
  const currentDate = new Date();

  for (let i = 0; i < count; i++) {
    const category = getRandomElement(ASSET_CATEGORIES);
    const subCategory = getRandomElement(ASSET_SUBCATEGORIES[category]);
    const manufacturer = getRandomElement(MANUFACTURERS);
    const template = ASSET_TEMPLATES[category];

    // Generate realistic purchase date (last 5 years)
    const purchaseDate = generateRandomDate(
      new Date(currentDate.getFullYear() - 5, 0, 1),
      currentDate
    );

    // Generate costs based on category
    const purchaseCost = generateRandomPrice(template.priceRange[0], template.priceRange[1]);
    const currentValue = calculateCurrentValue(purchaseCost, purchaseDate, template.depreciationRate);

    // Generate asset
    const asset: Asset = {
      id: `asset-${i + 1}`,
      assetId: generateAssetId(i + 1),
      name: `${manufacturer} ${subCategory} ${Math.floor(Math.random() * 1000) + 1}`,
      category,
      subCategory,
      manufacturer,
      model: `Model-${Math.floor(Math.random() * 9000) + 1000}`,
      serialNumber: `SN${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
      location: getRandomElement(LOCATIONS),
      department: getRandomElement(DEPARTMENTS),
      assignedUser: Math.random() > 0.3 ? getRandomElement(SAMPLE_USERS) : undefined,
      purchaseDate,
      purchaseCost,
      currentValue: Math.round(currentValue * 100) / 100,
      warrantyExpiration: Math.random() > 0.2 ? generateWarrantyDate(purchaseDate) : undefined,
      status: getRandomElement(['Active', 'Active', 'Active', 'Active', 'Inactive', 'Maintenance'] as AssetStatus[]),
      condition: getRandomElement(['Excellent', 'Good', 'Good', 'Fair', 'Poor'] as AssetCondition[]),
      tags: getRandomElements([...template.commonTags, 'Important', 'Tracked', 'Monitored', 'Critical'], Math.floor(Math.random() * 4) + 1),
      notes: Math.random() > 0.5 ? `Asset notes for ${category.toLowerCase()} equipment. Regular maintenance required.` : '',
      attachments: [],
      createdAt: purchaseDate,
      updatedAt: generateRandomDate(purchaseDate, currentDate),
      createdBy: getRandomElement(SAMPLE_USERS).name,
      lastModifiedBy: getRandomElement(SAMPLE_USERS).name,
      qrCode: generateQRCode(generateAssetId(i + 1)),
      depreciationMethod: getRandomElement(['Straight Line', 'Declining Balance', 'Double Declining Balance'] as DepreciationMethod[]),
      depreciationRate: template.depreciationRate,
      salvageValue: Math.round(purchaseCost * 0.1 * 100) / 100,
      usefulLife: template.usefulLife,
      maintenanceSchedule: {
        frequency: getRandomElement(['monthly', 'quarterly', 'annually'] as const),
        nextMaintenance: generateMaintenanceDate(purchaseDate),
        maintenanceCost: Math.round(purchaseCost * 0.05 * 100) / 100,
        maintenanceType: getRandomElement(['preventive', 'corrective'] as const)
      },
      utilizationRate: Math.round((Math.random() * 40 + 60) * 100) / 100, // 60-100%
      lastMaintenanceDate: Math.random() > 0.3 ? generateRandomDate(purchaseDate, currentDate) : undefined,
      nextMaintenanceDate: generateMaintenanceDate(currentDate)
    };

    assets.push(asset);
  }

  return assets.sort((a, b) => a.assetId.localeCompare(b.assetId));
};

// Generate sample asset movements
export const generateSampleMovements = (assets: Asset[], count: number = 25) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `movement-${i + 1}`,
    assetId: getRandomElement(assets).id,
    fromLocation: getRandomElement(LOCATIONS),
    toLocation: getRandomElement(LOCATIONS),
    movedBy: getRandomElement(SAMPLE_USERS).name,
    movedAt: generateRandomDate(new Date(2024, 0, 1), new Date()),
    reason: getRandomElement([
      'Department relocation',
      'Maintenance requirement',
      'User assignment change',
      'Office renovation',
      'Equipment upgrade',
      'Temporary assignment'
    ]),
    approvedBy: getRandomElement(SAMPLE_USERS).name,
    status: getRandomElement(['completed', 'completed', 'pending', 'approved'] as const)
  }));
};

// Generate sample checkouts
export const generateSampleCheckouts = (assets: Asset[], count: number = 20) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `checkout-${i + 1}`,
    assetId: getRandomElement(assets).id,
    checkedOutBy: getRandomElement(SAMPLE_USERS),
    checkedOutAt: generateRandomDate(new Date(2024, 0, 1), new Date()),
    expectedReturnDate: generateRandomDate(new Date(), new Date(2024, 11, 31)),
    actualReturnDate: Math.random() > 0.6 ? generateRandomDate(new Date(2024, 0, 1), new Date()) : undefined,
    purpose: getRandomElement([
      'Field work assignment',
      'Remote work setup',
      'Training session',
      'Client presentation',
      'Maintenance task',
      'Project requirement'
    ]),
    status: getRandomElement(['checked-out', 'returned', 'overdue'] as const),
    condition: getRandomElement(['Excellent', 'Good', 'Fair'] as AssetCondition[]),
    notes: Math.random() > 0.5 ? 'Equipment in good condition upon checkout' : undefined
  }));
};

// Export the main sample data
export const SAMPLE_ASSETS = generateSampleAssets(75);
export const SAMPLE_MOVEMENTS = generateSampleMovements(SAMPLE_ASSETS, 25);
export const SAMPLE_CHECKOUTS = generateSampleCheckouts(SAMPLE_ASSETS, 20);
