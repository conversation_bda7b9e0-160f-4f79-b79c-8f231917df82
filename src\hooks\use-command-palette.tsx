import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

export interface Command {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  action: () => void;
  category: string;
  keywords: string[];
  shortcut?: string;
  priority?: number;
  enabled?: boolean;
  badge?: string | number;
  group?: string;
}

export interface CommandGroup {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  commands: Command[];
  priority?: number;
}

interface CommandPaletteContextType {
  isOpen: boolean;
  commands: Command[];
  recentCommands: string[];
  favoriteCommands: string[];
  open: () => void;
  close: () => void;
  toggle: () => void;
  registerCommand: (command: Command) => void;
  unregisterCommand: (id: string) => void;
  executeCommand: (id: string) => void;
  addToFavorites: (id: string) => void;
  removeFromFavorites: (id: string) => void;
  searchCommands: (query: string) => Command[];
  getCommandsByCategory: (category: string) => Command[];
  settings: CommandPaletteSettings;
  updateSettings: (settings: Partial<CommandPaletteSettings>) => void;
}

interface CommandPaletteSettings {
  enabled: boolean;
  shortcut: string;
  maxRecentCommands: number;
  showCategories: boolean;
  showShortcuts: boolean;
  fuzzySearch: boolean;
  caseSensitive: boolean;
  showIcons: boolean;
  theme: 'auto' | 'light' | 'dark';
}

const defaultSettings: CommandPaletteSettings = {
  enabled: true,
  shortcut: 'ctrl+k',
  maxRecentCommands: 10,
  showCategories: true,
  showShortcuts: true,
  fuzzySearch: true,
  caseSensitive: false,
  showIcons: true,
  theme: 'auto',
};

const CommandPaletteContext = createContext<CommandPaletteContextType | null>(null);

export const useCommandPalette = () => {
  const context = useContext(CommandPaletteContext);
  if (!context) {
    throw new Error('useCommandPalette must be used within CommandPaletteProvider');
  }
  return context;
};

interface CommandPaletteProviderProps {
  children: ReactNode;
}

export const CommandPaletteProvider: React.FC<CommandPaletteProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [commands, setCommands] = useState<Command[]>([]);
  const [recentCommands, setRecentCommands] = useState<string[]>([]);
  const [favoriteCommands, setFavoriteCommands] = useState<string[]>([]);
  const [settings, setSettings] = useState<CommandPaletteSettings>(() => {
    const saved = localStorage.getItem('command-palette-settings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  const navigate = useNavigate();

  // Load saved data
  useEffect(() => {
    const savedRecent = localStorage.getItem('command-palette-recent');
    const savedFavorites = localStorage.getItem('command-palette-favorites');
    
    if (savedRecent) {
      setRecentCommands(JSON.parse(savedRecent));
    }
    if (savedFavorites) {
      setFavoriteCommands(JSON.parse(savedFavorites));
    }
  }, []);

  // Register default commands
  useEffect(() => {
    const defaultCommands: Command[] = [
      // Navigation Commands
      {
        id: 'nav-dashboard',
        title: 'Go to Dashboard',
        description: 'Navigate to the main dashboard',
        action: () => navigate('/'),
        category: 'Navigation',
        keywords: ['home', 'main', 'overview', 'dashboard'],
        shortcut: 'ctrl+shift+h',
        priority: 10,
      },
      {
        id: 'nav-financial',
        title: 'Financial Overview',
        description: 'View financial dashboard and reports',
        action: () => navigate('/financial'),
        category: 'Navigation',
        keywords: ['financial', 'money', 'revenue', 'profit', 'finance'],
        priority: 9,
      },
      {
        id: 'nav-assets',
        title: 'Asset Management',
        description: 'Manage company assets and equipment',
        action: () => navigate('/assets/registry'),
        category: 'Navigation',
        keywords: ['assets', 'equipment', 'inventory', 'registry'],
        priority: 8,
      },
      {
        id: 'nav-hr',
        title: 'Human Resources',
        description: 'Employee management and HR functions',
        action: () => navigate('/hr/employees'),
        category: 'Navigation',
        keywords: ['hr', 'human resources', 'employees', 'staff', 'people'],
        priority: 8,
      },
      {
        id: 'nav-maintenance',
        title: 'Maintenance',
        description: 'Work orders and maintenance management',
        action: () => navigate('/maintenance/work-orders'),
        category: 'Navigation',
        keywords: ['maintenance', 'work orders', 'repair', 'service'],
        priority: 7,
      },
      {
        id: 'nav-safety',
        title: 'Safety & Compliance',
        description: 'HSE management and safety protocols',
        action: () => navigate('/hse/incidents'),
        category: 'Navigation',
        keywords: ['safety', 'hse', 'compliance', 'incidents', 'health'],
        priority: 7,
      },

      // Quick Actions
      {
        id: 'action-new-workorder',
        title: 'Create Work Order',
        description: 'Start a new maintenance work order',
        action: () => navigate('/maintenance/work-orders?new=true'),
        category: 'Quick Actions',
        keywords: ['create', 'new', 'work order', 'maintenance'],
        priority: 9,
      },
      {
        id: 'action-add-employee',
        title: 'Add Employee',
        description: 'Register a new team member',
        action: () => navigate('/hr/employees?new=true'),
        category: 'Quick Actions',
        keywords: ['add', 'new', 'employee', 'hire', 'staff'],
        priority: 8,
      },
      {
        id: 'action-financial-report',
        title: 'Generate Financial Report',
        description: 'Create a new financial analysis report',
        action: () => navigate('/analytics/financial'),
        category: 'Quick Actions',
        keywords: ['report', 'financial', 'analysis', 'generate'],
        priority: 8,
      },

      // System Commands
      {
        id: 'system-refresh',
        title: 'Refresh Page',
        description: 'Reload the current page',
        action: () => window.location.reload(),
        category: 'System',
        keywords: ['refresh', 'reload', 'update'],
        shortcut: 'ctrl+r',
        priority: 5,
      },
      {
        id: 'system-logout',
        title: 'Sign Out',
        description: 'Log out of your account',
        action: () => {
          // Implement logout logic
          console.log('Logging out...');
        },
        category: 'System',
        keywords: ['logout', 'sign out', 'exit'],
        priority: 3,
      },

      // Search Commands
      {
        id: 'search-global',
        title: 'Global Search',
        description: 'Search across all modules and data',
        action: () => {
          // Implement global search
          console.log('Opening global search...');
        },
        category: 'Search',
        keywords: ['search', 'find', 'global'],
        shortcut: 'ctrl+shift+f',
        priority: 6,
      },

      // Settings Commands
      {
        id: 'settings-preferences',
        title: 'User Preferences',
        description: 'Customize your app settings',
        action: () => {
          // Open preferences modal
          console.log('Opening preferences...');
        },
        category: 'Settings',
        keywords: ['settings', 'preferences', 'config', 'customize'],
        shortcut: 'ctrl+,',
        priority: 4,
      },
      {
        id: 'settings-theme',
        title: 'Change Theme',
        description: 'Switch between light and dark themes',
        action: () => {
          // Toggle theme
          console.log('Toggling theme...');
        },
        category: 'Settings',
        keywords: ['theme', 'dark', 'light', 'appearance'],
        shortcut: 'ctrl+shift+t',
        priority: 5,
      },
    ];

    setCommands(defaultCommands);
  }, [navigate]);

  // Keyboard shortcut handler
  useEffect(() => {
    if (!settings.enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const { key, ctrlKey, metaKey, shiftKey, altKey } = event;
      const modifier = ctrlKey || metaKey;

      // Parse shortcut string (e.g., "ctrl+k", "ctrl+shift+p")
      const shortcutParts = settings.shortcut.toLowerCase().split('+');
      const hasCtrl = shortcutParts.includes('ctrl') || shortcutParts.includes('cmd');
      const hasShift = shortcutParts.includes('shift');
      const hasAlt = shortcutParts.includes('alt');
      const keyPart = shortcutParts[shortcutParts.length - 1];

      if (
        key.toLowerCase() === keyPart &&
        modifier === hasCtrl &&
        shiftKey === hasShift &&
        altKey === hasAlt
      ) {
        event.preventDefault();
        toggle();
      }

      // Handle escape key to close
      if (key === 'Escape' && isOpen) {
        close();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [settings.enabled, settings.shortcut, isOpen]);

  const open = useCallback(() => {
    if (settings.enabled) {
      setIsOpen(true);
    }
  }, [settings.enabled]);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const registerCommand = useCallback((command: Command) => {
    setCommands(prev => {
      const existing = prev.find(c => c.id === command.id);
      if (existing) {
        return prev.map(c => c.id === command.id ? command : c);
      }
      return [...prev, command];
    });
  }, []);

  const unregisterCommand = useCallback((id: string) => {
    setCommands(prev => prev.filter(c => c.id !== id));
  }, []);

  const executeCommand = useCallback((id: string) => {
    const command = commands.find(c => c.id === id);
    if (!command || command.enabled === false) return;

    // Execute the command
    command.action();

    // Add to recent commands
    setRecentCommands(prev => {
      const updated = [id, ...prev.filter(cid => cid !== id)].slice(0, settings.maxRecentCommands);
      localStorage.setItem('command-palette-recent', JSON.stringify(updated));
      return updated;
    });

    // Close the palette
    close();
  }, [commands, settings.maxRecentCommands, close]);

  const addToFavorites = useCallback((id: string) => {
    setFavoriteCommands(prev => {
      if (prev.includes(id)) return prev;
      const updated = [...prev, id];
      localStorage.setItem('command-palette-favorites', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const removeFromFavorites = useCallback((id: string) => {
    setFavoriteCommands(prev => {
      const updated = prev.filter(cid => cid !== id);
      localStorage.setItem('command-palette-favorites', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const searchCommands = useCallback((query: string): Command[] => {
    if (!query.trim()) return commands;

    const searchTerm = settings.caseSensitive ? query : query.toLowerCase();
    
    return commands.filter(command => {
      if (command.enabled === false) return false;

      const title = settings.caseSensitive ? command.title : command.title.toLowerCase();
      const description = settings.caseSensitive ? (command.description || '') : (command.description || '').toLowerCase();
      const keywords = command.keywords.map(k => settings.caseSensitive ? k : k.toLowerCase());

      if (settings.fuzzySearch) {
        // Simple fuzzy search implementation
        const fuzzyMatch = (text: string, pattern: string): boolean => {
          let patternIndex = 0;
          for (let i = 0; i < text.length && patternIndex < pattern.length; i++) {
            if (text[i] === pattern[patternIndex]) {
              patternIndex++;
            }
          }
          return patternIndex === pattern.length;
        };

        return (
          fuzzyMatch(title, searchTerm) ||
          fuzzyMatch(description, searchTerm) ||
          keywords.some(keyword => fuzzyMatch(keyword, searchTerm))
        );
      } else {
        return (
          title.includes(searchTerm) ||
          description.includes(searchTerm) ||
          keywords.some(keyword => keyword.includes(searchTerm))
        );
      }
    }).sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }, [commands, settings.caseSensitive, settings.fuzzySearch]);

  const getCommandsByCategory = useCallback((category: string): Command[] => {
    return commands
      .filter(command => command.category === category && command.enabled !== false)
      .sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }, [commands]);

  const updateSettings = useCallback((newSettings: Partial<CommandPaletteSettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      localStorage.setItem('command-palette-settings', JSON.stringify(updated));
      return updated;
    });
  }, []);

  const value: CommandPaletteContextType = {
    isOpen,
    commands,
    recentCommands,
    favoriteCommands,
    open,
    close,
    toggle,
    registerCommand,
    unregisterCommand,
    executeCommand,
    addToFavorites,
    removeFromFavorites,
    searchCommands,
    getCommandsByCategory,
    settings,
    updateSettings,
  };

  return (
    <CommandPaletteContext.Provider value={value}>
      {children}
    </CommandPaletteContext.Provider>
  );
};

// Helper hooks for specific command categories
export const useNavigationCommands = () => {
  const { getCommandsByCategory } = useCommandPalette();
  return getCommandsByCategory('Navigation');
};

export const useQuickActionCommands = () => {
  const { getCommandsByCategory } = useCommandPalette();
  return getCommandsByCategory('Quick Actions');
};

export const useSystemCommands = () => {
  const { getCommandsByCategory } = useCommandPalette();
  return getCommandsByCategory('System');
};

// Utility functions
export const commandUtils = {
  // Create a command from a route
  createNavigationCommand: (
    id: string,
    title: string,
    path: string,
    keywords: string[] = [],
    description?: string,
    priority = 5
  ): Omit<Command, 'action'> => ({
    id,
    title,
    description,
    category: 'Navigation',
    keywords: [...keywords, title.toLowerCase()],
    priority,
  }),

  // Create a quick action command
  createQuickActionCommand: (
    id: string,
    title: string,
    action: () => void,
    keywords: string[] = [],
    description?: string,
    priority = 7
  ): Command => ({
    id,
    title,
    description,
    action,
    category: 'Quick Actions',
    keywords: [...keywords, title.toLowerCase()],
    priority,
  }),

  // Parse keyboard shortcut
  parseShortcut: (shortcut: string): { key: string; modifiers: string[] } => {
    const parts = shortcut.toLowerCase().split('+');
    const key = parts[parts.length - 1];
    const modifiers = parts.slice(0, -1);
    return { key, modifiers };
  },

  // Format shortcut for display
  formatShortcut: (shortcut: string): string => {
    return shortcut
      .split('+')
      .map(part => {
        switch (part.toLowerCase()) {
          case 'ctrl': return '⌃';
          case 'cmd': return '⌘';
          case 'shift': return '⇧';
          case 'alt': return '⌥';
          default: return part.toUpperCase();
        }
      })
      .join('');
  },
};
