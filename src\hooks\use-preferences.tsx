import { createContext, useContext, useEffect, useState } from 'react';

export interface UserPreferences {
  // Theme & Appearance
  theme: 'light' | 'dark' | 'system';
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
  reducedMotion: boolean;
  
  // Layout
  sidebarCollapsed: boolean;
  compactMode: boolean;
  gridDensity: 'comfortable' | 'compact' | 'spacious';
  
  // Dashboard
  dashboardLayout: 'default' | 'compact' | 'detailed';
  favoriteModules: string[];
  hiddenWidgets: string[];
  widgetOrder: string[];
  
  // Notifications
  notificationsEnabled: boolean;
  soundEnabled: boolean;
  desktopNotifications: boolean;
  notificationCategories: Record<string, boolean>;
  
  // Data & Display
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  currency: 'USD' | 'EUR' | 'GBP' | 'JPY';
  numberFormat: 'US' | 'EU' | 'UK';
  
  // Advanced
  autoSave: boolean;
  autoRefresh: boolean;
  refreshInterval: number; // minutes
  debugMode: boolean;
}

const defaultPreferences: UserPreferences = {
  // Theme & Appearance
  theme: 'system',
  accentColor: '262 100% 70%',
  fontSize: 'medium',
  reducedMotion: false,
  
  // Layout
  sidebarCollapsed: false,
  compactMode: false,
  gridDensity: 'comfortable',
  
  // Dashboard
  dashboardLayout: 'default',
  favoriteModules: ['financial', 'assets', 'maintenance'],
  hiddenWidgets: [],
  widgetOrder: [],
  
  // Notifications
  notificationsEnabled: true,
  soundEnabled: true,
  desktopNotifications: true,
  notificationCategories: {
    system: true,
    maintenance: true,
    safety: true,
    financial: true,
    hr: true,
  },
  
  // Data & Display
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h',
  currency: 'USD',
  numberFormat: 'US',
  
  // Advanced
  autoSave: true,
  autoRefresh: true,
  refreshInterval: 5,
  debugMode: false,
};

type PreferencesContextType = {
  preferences: UserPreferences;
  updatePreferences: (updates: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  exportPreferences: () => string;
  importPreferences: (data: string) => boolean;
};

const PreferencesContext = createContext<PreferencesContextType | undefined>(undefined);

const PREFERENCES_KEY = 'user-preferences';

export function PreferencesProvider({ children }: { children: React.ReactNode }) {
  const [preferences, setPreferences] = useState<UserPreferences>(() => {
    try {
      const saved = localStorage.getItem(PREFERENCES_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        return { ...defaultPreferences, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load preferences:', error);
    }
    return defaultPreferences;
  });

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(PREFERENCES_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save preferences:', error);
    }
  }, [preferences]);

  // Apply theme preferences to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply accent color
    root.style.setProperty('--primary', preferences.accentColor);
    
    // Apply font size
    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px',
    };
    root.style.setProperty('font-size', fontSizeMap[preferences.fontSize]);
    
    // Apply reduced motion
    if (preferences.reducedMotion) {
      root.style.setProperty('--animation-duration', '0s');
      root.style.setProperty('--transition-duration', '0s');
    } else {
      root.style.removeProperty('--animation-duration');
      root.style.removeProperty('--transition-duration');
    }
    
    // Apply compact mode
    if (preferences.compactMode) {
      root.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
    }
  }, [preferences]);

  const updatePreferences = (updates: Partial<UserPreferences>) => {
    setPreferences(prev => ({ ...prev, ...updates }));
  };

  const resetPreferences = () => {
    setPreferences(defaultPreferences);
  };

  const exportPreferences = () => {
    return JSON.stringify(preferences, null, 2);
  };

  const importPreferences = (data: string): boolean => {
    try {
      const parsed = JSON.parse(data);
      // Validate the structure
      if (typeof parsed === 'object' && parsed !== null) {
        setPreferences({ ...defaultPreferences, ...parsed });
        return true;
      }
    } catch (error) {
      console.warn('Failed to import preferences:', error);
    }
    return false;
  };

  return (
    <PreferencesContext.Provider value={{
      preferences,
      updatePreferences,
      resetPreferences,
      exportPreferences,
      importPreferences,
    }}>
      {children}
    </PreferencesContext.Provider>
  );
}

export function usePreferences() {
  const context = useContext(PreferencesContext);
  if (context === undefined) {
    throw new Error('usePreferences must be used within a PreferencesProvider');
  }
  return context;
}

// Utility hooks for specific preference categories
export function useThemePreferences() {
  const { preferences, updatePreferences } = usePreferences();
  return {
    theme: preferences.theme,
    accentColor: preferences.accentColor,
    fontSize: preferences.fontSize,
    reducedMotion: preferences.reducedMotion,
    setTheme: (theme: UserPreferences['theme']) => updatePreferences({ theme }),
    setAccentColor: (accentColor: string) => updatePreferences({ accentColor }),
    setFontSize: (fontSize: UserPreferences['fontSize']) => updatePreferences({ fontSize }),
    setReducedMotion: (reducedMotion: boolean) => updatePreferences({ reducedMotion }),
  };
}

export function useLayoutPreferences() {
  const { preferences, updatePreferences } = usePreferences();
  return {
    sidebarCollapsed: preferences.sidebarCollapsed,
    compactMode: preferences.compactMode,
    gridDensity: preferences.gridDensity,
    setSidebarCollapsed: (collapsed: boolean) => updatePreferences({ sidebarCollapsed: collapsed }),
    setCompactMode: (compact: boolean) => updatePreferences({ compactMode: compact }),
    setGridDensity: (density: UserPreferences['gridDensity']) => updatePreferences({ gridDensity: density }),
  };
}

export function useDashboardPreferences() {
  const { preferences, updatePreferences } = usePreferences();
  return {
    dashboardLayout: preferences.dashboardLayout,
    favoriteModules: preferences.favoriteModules,
    hiddenWidgets: preferences.hiddenWidgets,
    widgetOrder: preferences.widgetOrder,
    setDashboardLayout: (layout: UserPreferences['dashboardLayout']) => updatePreferences({ dashboardLayout: layout }),
    addFavoriteModule: (module: string) => {
      if (!preferences.favoriteModules.includes(module)) {
        updatePreferences({ favoriteModules: [...preferences.favoriteModules, module] });
      }
    },
    removeFavoriteModule: (module: string) => {
      updatePreferences({ favoriteModules: preferences.favoriteModules.filter(m => m !== module) });
    },
    hideWidget: (widget: string) => {
      if (!preferences.hiddenWidgets.includes(widget)) {
        updatePreferences({ hiddenWidgets: [...preferences.hiddenWidgets, widget] });
      }
    },
    showWidget: (widget: string) => {
      updatePreferences({ hiddenWidgets: preferences.hiddenWidgets.filter(w => w !== widget) });
    },
    setWidgetOrder: (order: string[]) => updatePreferences({ widgetOrder: order }),
  };
}

export function useNotificationPreferences() {
  const { preferences, updatePreferences } = usePreferences();
  return {
    notificationsEnabled: preferences.notificationsEnabled,
    soundEnabled: preferences.soundEnabled,
    desktopNotifications: preferences.desktopNotifications,
    notificationCategories: preferences.notificationCategories,
    setNotificationsEnabled: (enabled: boolean) => updatePreferences({ notificationsEnabled: enabled }),
    setSoundEnabled: (enabled: boolean) => updatePreferences({ soundEnabled: enabled }),
    setDesktopNotifications: (enabled: boolean) => updatePreferences({ desktopNotifications: enabled }),
    setCategoryEnabled: (category: string, enabled: boolean) => {
      updatePreferences({
        notificationCategories: {
          ...preferences.notificationCategories,
          [category]: enabled,
        },
      });
    },
  };
}
