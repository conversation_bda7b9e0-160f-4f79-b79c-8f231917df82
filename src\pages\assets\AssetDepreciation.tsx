
import React, { useState, useMemo } from 'react';
import { AppLayout } from "@/components/layout/AppLayout";
import { StatCard } from "@/components/dashboard/StatCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  TrendingDown,
  Calculator,
  Search,
  Filter,
  DollarSign,
  Calendar,
  FileText,
  Download,
  RefreshCw,
  BarChart3,
  Target,
  AlertTriangle,
  Clock,
  Zap,
  Award,
  ArrowUp,
  ArrowDown,
  Minus,
  TrendingUp
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SAMPLE_ASSETS, ASSET_CATEGORIES } from '@/data/assetSampleData';
import { Asset, AssetCategory, DepreciationMethod } from '@/types/asset';
import { ContextTooltip } from '@/components/ui/ContextTooltip';
import { useDynamicColors } from '@/hooks/use-theme-colors';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';

// Chart color schemes
const CHART_COLORS = {
  primary: '#3B82F6',
  secondary: '#10B981',
  accent: '#F59E0B',
  warning: '#EF4444',
  purple: '#8B5CF6',
  teal: '#14B8A6',
  pink: '#EC4899',
  indigo: '#6366F1'
};

const CATEGORY_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#14B8A6', '#EC4899', '#6366F1', '#F97316', '#84CC16'
];

// Depreciation calculation functions
const calculateStraightLineDepreciation = (cost: number, salvageValue: number, usefulLife: number, year: number) => {
  const annualDepreciation = (cost - salvageValue) / usefulLife;
  const accumulatedDepreciation = Math.min(annualDepreciation * year, cost - salvageValue);
  const bookValue = cost - accumulatedDepreciation;
  return { annualDepreciation, accumulatedDepreciation, bookValue };
};

const calculateDecliningBalanceDepreciation = (cost: number, salvageValue: number, rate: number, year: number) => {
  let bookValue = cost;
  let totalDepreciation = 0;

  for (let i = 1; i <= year; i++) {
    const annualDepreciation = Math.min(bookValue * (rate / 100), bookValue - salvageValue);
    totalDepreciation += annualDepreciation;
    bookValue -= annualDepreciation;
  }

  return {
    annualDepreciation: year > 0 ? Math.min(bookValue * (rate / 100), bookValue - salvageValue) : 0,
    accumulatedDepreciation: totalDepreciation,
    bookValue: Math.max(bookValue, salvageValue)
  };
};

const calculateSumOfYearsDigitsDepreciation = (cost: number, salvageValue: number, usefulLife: number, year: number) => {
  const depreciableBase = cost - salvageValue;
  const sumOfYears = (usefulLife * (usefulLife + 1)) / 2;
  const remainingLife = Math.max(0, usefulLife - year + 1);
  const annualDepreciation = year <= usefulLife ? (depreciableBase * remainingLife) / sumOfYears : 0;

  let accumulatedDepreciation = 0;
  for (let i = 1; i <= Math.min(year, usefulLife); i++) {
    const yearRemainingLife = usefulLife - i + 1;
    accumulatedDepreciation += (depreciableBase * yearRemainingLife) / sumOfYears;
  }

  const bookValue = cost - accumulatedDepreciation;
  return { annualDepreciation, accumulatedDepreciation, bookValue };
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background/95 backdrop-blur-sm border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: $${entry.value?.toLocaleString()}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const getTrendIcon = (value: number) => {
  if (value > 0) return <ArrowUp className="h-3 w-3 text-green-600" />;
  if (value < 0) return <ArrowDown className="h-3 w-3 text-red-600" />;
  return <Minus className="h-3 w-3 text-gray-600" />;
};

const AssetDepreciation: React.FC = () => {
  const dynamicColors = useDynamicColors();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedMethod, setSelectedMethod] = useState<string>('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('current');
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Dynamic chart colors based on theme
  const chartColors = {
    primary: dynamicColors.getColorHsl('medium'),
    secondary: dynamicColors.getColorHsl('light'),
    accent: dynamicColors.getColorHsl('dark'),
    warning: '#EF4444',
    purple: '#8B5CF6',
    teal: '#14B8A6',
    pink: '#EC4899',
    indigo: '#6366F1'
  };

  // Calculate comprehensive depreciation data
  const depreciationData = useMemo(() => {
    const filteredAssets = SAMPLE_ASSETS.filter(asset =>
      selectedCategory === 'all' || asset.category === selectedCategory
    );

    const currentYear = new Date().getFullYear();
    const assetAge = (asset: Asset) => currentYear - asset.purchaseDate.getFullYear();

    // Calculate depreciation for each asset
    const assetDepreciations = filteredAssets.map(asset => {
      const age = assetAge(asset);
      const method = asset.depreciationMethod;

      let calculation;
      switch (method) {
        case 'Straight Line':
          calculation = calculateStraightLineDepreciation(
            asset.purchaseCost,
            asset.salvageValue,
            asset.usefulLife,
            age
          );
          break;
        case 'Declining Balance':
          calculation = calculateDecliningBalanceDepreciation(
            asset.purchaseCost,
            asset.salvageValue,
            asset.depreciationRate,
            age
          );
          break;
        case 'Sum of Years Digits':
          calculation = calculateSumOfYearsDigitsDepreciation(
            asset.purchaseCost,
            asset.salvageValue,
            asset.usefulLife,
            age
          );
          break;
        default:
          calculation = calculateStraightLineDepreciation(
            asset.purchaseCost,
            asset.salvageValue,
            asset.usefulLife,
            age
          );
      }

      return {
        ...asset,
        age,
        ...calculation,
        remainingLife: Math.max(0, asset.usefulLife - age),
        depreciationPercentage: ((asset.purchaseCost - calculation.bookValue) / asset.purchaseCost) * 100
      };
    });

    return assetDepreciations;
  }, [selectedCategory, SAMPLE_ASSETS]);

  // Calculate KPIs and summary statistics
  const kpis = useMemo(() => {
    const totalOriginalValue = depreciationData.reduce((sum, asset) => sum + asset.purchaseCost, 0);
    const totalCurrentValue = depreciationData.reduce((sum, asset) => sum + asset.bookValue, 0);
    const totalDepreciation = depreciationData.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0);
    const totalAnnualDepreciation = depreciationData.reduce((sum, asset) => sum + asset.annualDepreciation, 0);
    const averageAge = depreciationData.reduce((sum, asset) => sum + asset.age, 0) / depreciationData.length;
    const averageRemainingLife = depreciationData.reduce((sum, asset) => sum + asset.remainingLife, 0) / depreciationData.length;
    const assetsNearingReplacement = depreciationData.filter(asset => asset.remainingLife <= 2).length;
    const averageDepreciationRate = depreciationData.reduce((sum, asset) => sum + asset.depreciationPercentage, 0) / depreciationData.length;

    return {
      totalOriginalValue,
      totalCurrentValue,
      totalDepreciation,
      totalAnnualDepreciation,
      averageAge,
      averageRemainingLife,
      assetsNearingReplacement,
      averageDepreciationRate,
      depreciationThisYear: totalAnnualDepreciation,
      assetCount: depreciationData.length
    };
  }, [depreciationData]);

  // Generate depreciation trend data for charts
  const depreciationTrend = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 10 }, (_, i) => currentYear - 9 + i);

    return years.map(year => {
      const yearDepreciation = depreciationData.reduce((sum, asset) => {
        const assetYear = year - asset.purchaseDate.getFullYear();
        if (assetYear <= 0) return sum;

        let calculation;
        switch (asset.depreciationMethod) {
          case 'Straight Line':
            calculation = calculateStraightLineDepreciation(
              asset.purchaseCost,
              asset.salvageValue,
              asset.usefulLife,
              assetYear
            );
            break;
          case 'Declining Balance':
            calculation = calculateDecliningBalanceDepreciation(
              asset.purchaseCost,
              asset.salvageValue,
              asset.depreciationRate,
              assetYear
            );
            break;
          default:
            calculation = calculateStraightLineDepreciation(
              asset.purchaseCost,
              asset.salvageValue,
              asset.usefulLife,
              assetYear
            );
        }

        return sum + calculation.annualDepreciation;
      }, 0);

      return {
        year: year.toString(),
        depreciation: Math.round(yearDepreciation),
        bookValue: depreciationData.reduce((sum, asset) => {
          const assetYear = year - asset.purchaseDate.getFullYear();
          if (assetYear <= 0) return sum + asset.purchaseCost;

          let calculation;
          switch (asset.depreciationMethod) {
            case 'Straight Line':
              calculation = calculateStraightLineDepreciation(
                asset.purchaseCost,
                asset.salvageValue,
                asset.usefulLife,
                assetYear
              );
              break;
            case 'Declining Balance':
              calculation = calculateDecliningBalanceDepreciation(
                asset.purchaseCost,
                asset.salvageValue,
                asset.depreciationRate,
                assetYear
              );
              break;
            default:
              calculation = calculateStraightLineDepreciation(
                asset.purchaseCost,
                asset.salvageValue,
                asset.usefulLife,
                assetYear
              );
          }

          return sum + calculation.bookValue;
        }, 0)
      };
    });
  }, [depreciationData]);

  // Category-wise depreciation analysis
  const categoryAnalysis = useMemo(() => {
    const categories = ASSET_CATEGORIES.filter(cat =>
      depreciationData.some(asset => asset.category === cat)
    );

    return categories.map(category => {
      const categoryAssets = depreciationData.filter(asset => asset.category === category);
      const totalOriginalValue = categoryAssets.reduce((sum, asset) => sum + asset.purchaseCost, 0);
      const totalCurrentValue = categoryAssets.reduce((sum, asset) => sum + asset.bookValue, 0);
      const totalDepreciation = categoryAssets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0);
      const averageAge = categoryAssets.reduce((sum, asset) => sum + asset.age, 0) / categoryAssets.length;

      return {
        category,
        assetCount: categoryAssets.length,
        totalOriginalValue,
        totalCurrentValue,
        totalDepreciation,
        depreciationPercentage: (totalDepreciation / totalOriginalValue) * 100,
        averageAge,
        averageRemainingLife: categoryAssets.reduce((sum, asset) => sum + asset.remainingLife, 0) / categoryAssets.length
      };
    });
  }, [depreciationData]);

  // Method-wise analysis
  const methodAnalysis = useMemo(() => {
    const methods: DepreciationMethod[] = ['Straight Line', 'Declining Balance', 'Double Declining Balance', 'Sum of Years Digits'];

    return methods.map(method => {
      const methodAssets = depreciationData.filter(asset => asset.depreciationMethod === method);
      if (methodAssets.length === 0) return null;

      const totalOriginalValue = methodAssets.reduce((sum, asset) => sum + asset.purchaseCost, 0);
      const totalCurrentValue = methodAssets.reduce((sum, asset) => sum + asset.bookValue, 0);
      const totalDepreciation = methodAssets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0);

      return {
        method,
        assetCount: methodAssets.length,
        totalOriginalValue,
        totalCurrentValue,
        totalDepreciation,
        depreciationPercentage: (totalDepreciation / totalOriginalValue) * 100
      };
    }).filter(Boolean);
  }, [depreciationData]);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Depreciation</h1>
            <p className="text-muted-foreground">
              Comprehensive depreciation analysis and financial reporting
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              Export Report
            </Button>
            <Button
              className="gap-2 border"
              style={{
                ...dynamicColors.bgPrimary(0.2),
                ...dynamicColors.borderPrimary(0.3),
                ...dynamicColors.textPrimary
              }}
            >
              <Calculator className="h-4 w-4" />
              Recalculate All
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="glass-theme">
          <CardContent className="pt-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search assets..." className="pl-10" />
                </div>
              </div>

              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {ASSET_CATEGORIES.map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedMethod} onValueChange={setSelectedMethod}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All Methods" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Methods</SelectItem>
                  <SelectItem value="Straight Line">Straight Line</SelectItem>
                  <SelectItem value="Declining Balance">Declining Balance</SelectItem>
                  <SelectItem value="Double Declining Balance">Double Declining Balance</SelectItem>
                  <SelectItem value="Sum of Years Digits">Sum of Years Digits</SelectItem>
                  <SelectItem value="Units of Production">Units of Production</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Current Year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="current">Current Year</SelectItem>
                  <SelectItem value="ytd">Year to Date</SelectItem>
                  <SelectItem value="5year">5-Year History</SelectItem>
                  <SelectItem value="10year">10-Year History</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                More Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <ContextTooltip content={{
            title: 'Total Asset Value',
            description: 'Current book value of all assets after depreciation',
            type: 'info'
          }}>
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Current Value</CardTitle>
                <DollarSign className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold" style={dynamicColors.textPrimary}>{formatCurrency(kpis.totalCurrentValue)}</div>
                <p className="text-xs text-muted-foreground">
                  {kpis.assetCount} assets
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Total Depreciation',
            description: 'Accumulated depreciation across all assets',
            type: 'warning'
          }}>
            <Card className="glass-theme">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Depreciation</CardTitle>
                <TrendingDown className="h-4 w-4" style={dynamicColors.textPrimary} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{formatCurrency(kpis.totalDepreciation)}</div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  {Math.round(kpis.averageDepreciationRate)}% average rate
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Annual Depreciation',
            description: 'Current year depreciation expense',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">This Year</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(kpis.depreciationThisYear)}</div>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  Annual expense
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Average Asset Age',
            description: 'Mean age of assets in the portfolio',
            type: 'info'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Age</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{kpis.averageAge.toFixed(1)} yrs</div>
                <p className="text-xs text-muted-foreground">
                  Portfolio average
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Remaining Useful Life',
            description: 'Average remaining useful life across all assets',
            type: 'performance'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Remaining Life</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{kpis.averageRemainingLife.toFixed(1)} yrs</div>
                <p className="text-xs text-muted-foreground">
                  Average remaining
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>

          <ContextTooltip content={{
            title: 'Assets Needing Replacement',
            description: 'Assets with 2 years or less remaining useful life',
            type: 'warning'
          }}>
            <Card className="glass-card">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Need Replacement</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{kpis.assetsNearingReplacement}</div>
                <p className="text-xs text-muted-foreground">
                  Within 2 years
                </p>
              </CardContent>
            </Card>
          </ContextTooltip>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Depreciation Overview</TabsTrigger>
            <TabsTrigger value="schedule">Asset Schedule</TabsTrigger>
            <TabsTrigger value="methods">Method Analysis</TabsTrigger>
            <TabsTrigger value="compliance">Compliance & Reports</TabsTrigger>
          </TabsList>

          {/* Depreciation Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Depreciation Trend Chart */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingDown className="h-5 w-5" />
                    Depreciation Trend
                  </CardTitle>
                  <CardDescription>
                    10-year depreciation and book value analysis
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={depreciationTrend}>
                        <defs>
                          <linearGradient id="depreciationGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={CHART_COLORS.warning} stopOpacity={0.3} />
                            <stop offset="95%" stopColor={CHART_COLORS.warning} stopOpacity={0.1} />
                          </linearGradient>
                          <linearGradient id="bookValueGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.3} />
                            <stop offset="95%" stopColor={CHART_COLORS.primary} stopOpacity={0.1} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis
                          dataKey="year"
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="bookValue"
                          stroke={CHART_COLORS.primary}
                          fillOpacity={1}
                          fill="url(#bookValueGradient)"
                          strokeWidth={2}
                          name="Book Value"
                        />
                        <Area
                          type="monotone"
                          dataKey="depreciation"
                          stroke={CHART_COLORS.warning}
                          fillOpacity={1}
                          fill="url(#depreciationGradient)"
                          strokeWidth={2}
                          name="Annual Depreciation"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Category Analysis Chart */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Depreciation by Category
                  </CardTitle>
                  <CardDescription>
                    Asset depreciation breakdown by category
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={categoryAnalysis}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis
                          dataKey="category"
                          className="text-xs"
                          tick={{ fontSize: 10 }}
                          angle={-45}
                          textAnchor="end"
                          height={80}
                        />
                        <YAxis
                          className="text-xs"
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Bar
                          dataKey="totalDepreciation"
                          fill={CHART_COLORS.warning}
                          name="Total Depreciation"
                          radius={[4, 4, 0, 0]}
                        />
                        <Bar
                          dataKey="totalCurrentValue"
                          fill={CHART_COLORS.primary}
                          name="Current Value"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Financial Insights */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Financial Insights & Recommendations
                </CardTitle>
                <CardDescription>
                  AI-powered depreciation analysis and optimization recommendations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center gap-2 mb-2">
                      <Award className="h-5 w-5 text-blue-600" />
                      <span className="font-semibold text-blue-800 dark:text-blue-200">Tax Optimization</span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Consider accelerated depreciation for {Math.round(kpis.assetCount * 0.3)} assets to maximize tax benefits.
                    </p>
                  </div>

                  <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                      <span className="font-semibold text-yellow-800 dark:text-yellow-200">Replacement Planning</span>
                    </div>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      {kpis.assetsNearingReplacement} assets require replacement planning within 24 months.
                    </p>
                  </div>

                  <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-2 mb-2">
                      <Zap className="h-5 w-5 text-green-600" />
                      <span className="font-semibold text-green-800 dark:text-green-200">Cost Savings</span>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Optimizing depreciation methods could save ${Math.round(kpis.totalAnnualDepreciation * 0.08).toLocaleString()} annually.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Asset Schedule Tab */}
          <TabsContent value="schedule" className="space-y-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Detailed Asset Depreciation Schedule
                </CardTitle>
                <CardDescription>
                  Individual asset depreciation calculations and schedules
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {depreciationData.slice(0, 10).map((asset) => (
                    <div key={asset.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                          <TrendingDown className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold">{asset.name}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{asset.assetId}</span>
                            <span>•</span>
                            <Badge variant="outline" className="text-xs">
                              {asset.depreciationMethod}
                            </Badge>
                            <span>•</span>
                            <Badge variant="outline" className="text-xs">
                              {asset.category}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6">
                        <div className="text-center">
                          <div className="text-sm font-medium">{formatCurrency(asset.purchaseCost)}</div>
                          <div className="text-xs text-muted-foreground">Original Cost</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium">{formatCurrency(asset.bookValue)}</div>
                          <div className="text-xs text-muted-foreground">Book Value</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium">{formatCurrency(asset.annualDepreciation)}</div>
                          <div className="text-xs text-muted-foreground">Annual Depreciation</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium">{asset.remainingLife.toFixed(1)} yrs</div>
                          <div className="text-xs text-muted-foreground">Remaining Life</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium">{Math.round(asset.depreciationPercentage)}%</div>
                          <div className="text-xs text-muted-foreground">Depreciated</div>
                        </div>
                        <Button variant="ghost" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Method Analysis Tab */}
          <TabsContent value="methods" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Method Comparison Chart */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Depreciation Method Comparison
                  </CardTitle>
                  <CardDescription>
                    Analysis of different depreciation methods in use
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={methodAnalysis.map((method, index) => ({
                            ...method,
                            fill: CATEGORY_COLORS[index % CATEGORY_COLORS.length]
                          }))}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ method, assetCount }) => `${method}: ${assetCount}`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="assetCount"
                        >
                          {methodAnalysis.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={CATEGORY_COLORS[index % CATEGORY_COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Method Performance */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Method Performance Analysis
                  </CardTitle>
                  <CardDescription>
                    Financial impact of each depreciation method
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {methodAnalysis.map((method, index) => (
                      <div key={method.method} className="p-4 border border-border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{method.method}</h4>
                          <Badge variant="outline" className="text-xs">
                            {method.assetCount} assets
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Total Value:</span>
                            <span className="ml-2 font-medium">{formatCurrency(method.totalCurrentValue)}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Depreciation:</span>
                            <span className="ml-2 font-medium">{formatCurrency(method.totalDepreciation)}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Depreciation %:</span>
                            <span className="ml-2 font-medium">{Math.round(method.depreciationPercentage)}%</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Tax Impact:</span>
                            <span className="ml-2 font-medium text-green-600">
                              {formatCurrency(method.totalDepreciation * 0.25)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Compliance & Reports Tab */}
          <TabsContent value="compliance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Compliance Dashboard */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Compliance Dashboard
                  </CardTitle>
                  <CardDescription>
                    Financial reporting and audit compliance status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                      <div className="flex items-center gap-2">
                        <Award className="h-5 w-5 text-green-600" />
                        <span className="font-medium text-green-800 dark:text-green-200">GAAP Compliance</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Compliant
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                      <div className="flex items-center gap-2">
                        <Award className="h-5 w-5 text-green-600" />
                        <span className="font-medium text-green-800 dark:text-green-200">IFRS Standards</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Compliant
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        <span className="font-medium text-yellow-800 dark:text-yellow-200">Tax Depreciation</span>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        Review Required
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-blue-600" />
                        <span className="font-medium text-blue-800 dark:text-blue-200">Audit Trail</span>
                      </div>
                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        Complete
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Report Generation */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    Report Generation
                  </CardTitle>
                  <CardDescription>
                    Generate financial and compliance reports
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button className="w-full justify-start gap-2" variant="outline">
                      <FileText className="h-4 w-4" />
                      Depreciation Schedule Report
                    </Button>

                    <Button className="w-full justify-start gap-2" variant="outline">
                      <BarChart3 className="h-4 w-4" />
                      Financial Impact Analysis
                    </Button>

                    <Button className="w-full justify-start gap-2" variant="outline">
                      <Target className="h-4 w-4" />
                      Tax Depreciation Summary
                    </Button>

                    <Button className="w-full justify-start gap-2" variant="outline">
                      <Award className="h-4 w-4" />
                      Compliance Audit Report
                    </Button>

                    <Button className="w-full justify-start gap-2" variant="outline">
                      <TrendingUp className="h-4 w-4" />
                      Asset Replacement Forecast
                    </Button>

                    <div className="pt-4 border-t border-border">
                      <h4 className="font-medium mb-2">Custom Report</h4>
                      <div className="space-y-2">
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select report type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="detailed">Detailed Analysis</SelectItem>
                            <SelectItem value="summary">Executive Summary</SelectItem>
                            <SelectItem value="comparison">Method Comparison</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button className="w-full gap-2">
                          <Download className="h-4 w-4" />
                          Generate Custom Report
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
};

export default AssetDepreciation;
