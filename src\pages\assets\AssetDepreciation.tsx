
import { AppLayout } from "@/components/layout/AppLayout";
import { StatCard } from "@/components/dashboard/StatCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TrendingDown, Calculator, Search, Filter, DollarSign, Calendar } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const AssetDepreciation = () => {
  const depreciationSchedule = [
    { id: "AST-001", name: "Pump Station Alpha", method: "Straight Line", originalValue: 250000, currentValue: 187500, yearlyDepreciation: 25000, remainingLife: 7.5 },
    { id: "AST-002", name: "Generator Unit 1", method: "Declining Balance", originalValue: 180000, currentValue: 108000, yearlyDepreciation: 36000, remainingLife: 3 },
    { id: "AST-003", name: "Control Panel CP-01", method: "Straight Line", originalValue: 45000, currentValue: 36000, yearlyDepreciation: 4500, remainingLife: 8 },
    { id: "AST-004", name: "Valve Assembly V-12", method: "Units of Production", originalValue: 12000, currentValue: 3600, yearlyDepreciation: 2800, remainingLife: 1.3 },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Asset Depreciation</h1>
            <p className="text-muted-foreground">Manage depreciation schedules and calculations</p>
          </div>
          <Button className="gap-2">
            <Calculator className="h-4 w-4" />
            Calculate Depreciation
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard 
            title="Total Depreciation"
            value="$2.4M"
            icon={<TrendingDown className="text-red-600 h-4 w-4" />}
            description="This year"
          />
          <StatCard 
            title="Current Asset Value"
            value="$9.8M"
            icon={<DollarSign className="text-primary h-4 w-4" />}
            trend={{ value: -8, isPositive: false }}
          />
          <StatCard 
            title="Avg Remaining Life"
            value="5.2 years"
            icon={<Calendar className="text-blue-600 h-4 w-4" />}
            description="Across all assets"
          />
          <StatCard 
            title="Assets to Replace"
            value="34"
            icon={<TrendingDown className="text-orange-600 h-4 w-4" />}
            description="Next 2 years"
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Depreciation Analysis</CardTitle>
            <CardDescription>Search and analyze asset depreciation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search assets..." className="pl-10" />
                </div>
              </div>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Depreciation Schedule</CardTitle>
            <CardDescription>Asset depreciation details and calculations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {depreciationSchedule.map((asset) => (
                <div key={asset.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <TrendingDown className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{asset.name}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{asset.id}</span>
                        <span>•</span>
                        <Badge variant="outline">{asset.method}</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-6">
                    <div className="text-right">
                      <div className="text-sm font-medium">Original: ${asset.originalValue.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Current: ${asset.currentValue.toLocaleString()}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">Yearly: ${asset.yearlyDepreciation.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Life: {asset.remainingLife} years</div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default AssetDepreciation;
