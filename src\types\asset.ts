// Asset Management Type Definitions
export interface Asset {
  id: string;
  assetId: string; // Auto-generated unique identifier (e.g., "AST-2024-001")
  name: string;
  category: AssetCategory;
  subCategory: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  location: AssetLocation;
  department: string;
  assignedUser?: AssignedUser;
  purchaseDate: Date;
  purchaseCost: number;
  currentValue: number;
  warrantyExpiration?: Date;
  status: AssetStatus;
  condition: AssetCondition;
  tags: string[];
  notes: string;
  attachments: AssetAttachment[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastModifiedBy: string;
  qrCode: string;
  depreciationMethod: DepreciationMethod;
  depreciationRate: number;
  salvageValue: number;
  usefulLife: number; // in years
  maintenanceSchedule?: MaintenanceSchedule;
  utilizationRate?: number;
  lastMaintenanceDate?: Date;
  nextMaintenanceDate?: Date;
}

export interface AssetLocation {
  site: string;
  building: string;
  floor: string;
  room: string;
  coordinates?: {
    x: number;
    y: number;
  };
}

export interface AssignedUser {
  id: string;
  name: string;
  email: string;
  department: string;
  assignedDate: Date;
  expectedReturnDate?: Date;
}

export interface AssetAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
}

export interface MaintenanceSchedule {
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'annually';
  lastMaintenance?: Date;
  nextMaintenance: Date;
  maintenanceCost: number;
  maintenanceType: 'preventive' | 'corrective' | 'predictive';
}

export interface AssetMovement {
  id: string;
  assetId: string;
  fromLocation: AssetLocation;
  toLocation: AssetLocation;
  movedBy: string;
  movedAt: Date;
  reason: string;
  approvedBy?: string;
  status: 'pending' | 'approved' | 'completed' | 'rejected';
}

export interface AssetCheckout {
  id: string;
  assetId: string;
  checkedOutBy: AssignedUser;
  checkedOutAt: Date;
  expectedReturnDate: Date;
  actualReturnDate?: Date;
  purpose: string;
  status: 'checked-out' | 'overdue' | 'returned';
  condition: AssetCondition;
  notes?: string;
}

export interface DepreciationRecord {
  id: string;
  assetId: string;
  year: number;
  month: number;
  depreciationAmount: number;
  accumulatedDepreciation: number;
  bookValue: number;
  method: DepreciationMethod;
  calculatedAt: Date;
}

export interface AssetPerformanceMetrics {
  assetId: string;
  utilizationRate: number;
  efficiency: number;
  roi: number;
  totalCostOfOwnership: number;
  maintenanceCostRatio: number;
  downtimeHours: number;
  availabilityPercentage: number;
  performanceScore: number;
  benchmarkComparison: number;
  lastUpdated: Date;
}

// Enums and Types
export type AssetCategory = 
  | 'IT Equipment'
  | 'Machinery'
  | 'Vehicles'
  | 'Furniture'
  | 'Tools'
  | 'Safety Equipment'
  | 'Office Equipment'
  | 'Medical Equipment'
  | 'Laboratory Equipment'
  | 'HVAC Systems'
  | 'Security Systems'
  | 'Communication Equipment'
  | 'Manufacturing Equipment'
  | 'Warehouse Equipment'
  | 'Facilities'
  | 'Software Licenses'
  | 'Other';

export type AssetStatus = 
  | 'Active'
  | 'Inactive'
  | 'Maintenance'
  | 'Disposed'
  | 'Lost'
  | 'Stolen'
  | 'Under Repair'
  | 'Reserved'
  | 'Retired';

export type AssetCondition = 
  | 'Excellent'
  | 'Good'
  | 'Fair'
  | 'Poor'
  | 'Critical';

export type DepreciationMethod = 
  | 'Straight Line'
  | 'Declining Balance'
  | 'Double Declining Balance'
  | 'Sum of Years Digits'
  | 'Units of Production'
  | 'Modified Accelerated Cost Recovery System (MACRS)';

// Filter and Search Types
export interface AssetFilters {
  search?: string;
  categories?: AssetCategory[];
  statuses?: AssetStatus[];
  conditions?: AssetCondition[];
  departments?: string[];
  locations?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  valueRange?: {
    min: number;
    max: number;
  };
  assignedUsers?: string[];
  tags?: string[];
}

export interface AssetSortOptions {
  field: keyof Asset;
  direction: 'asc' | 'desc';
}

export interface AssetTableColumn {
  key: keyof Asset | string;
  label: string;
  sortable: boolean;
  width?: number;
  render?: (value: any, asset: Asset) => React.ReactNode;
}

// API Response Types
export interface AssetListResponse {
  assets: Asset[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  filters: AssetFilters;
}

export interface AssetStatsResponse {
  totalAssets: number;
  totalValue: number;
  averageAge: number;
  utilizationRate: number;
  maintenanceCosts: number;
  depreciationThisYear: number;
  assetsByCategory: Record<AssetCategory, number>;
  assetsByStatus: Record<AssetStatus, number>;
  assetsByCondition: Record<AssetCondition, number>;
  assetsByLocation: Record<string, number>;
}

// Form Types
export interface AssetFormData extends Omit<Asset, 'id' | 'assetId' | 'createdAt' | 'updatedAt' | 'createdBy' | 'lastModifiedBy' | 'qrCode'> {
  id?: string;
}

export interface BulkAssetOperation {
  operation: 'update' | 'delete' | 'export' | 'move' | 'assign';
  assetIds: string[];
  data?: Partial<Asset>;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: any;
}

export interface TimeSeriesDataPoint {
  date: Date;
  value: number;
  category?: string;
}

export interface AssetAnalyticsData {
  utilizationTrend: TimeSeriesDataPoint[];
  costAnalysis: ChartDataPoint[];
  performanceMetrics: ChartDataPoint[];
  depreciationForecast: TimeSeriesDataPoint[];
  maintenanceCosts: TimeSeriesDataPoint[];
  assetDistribution: ChartDataPoint[];
}

// Export Types
export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  fields: (keyof Asset)[];
  filters?: AssetFilters;
  includeImages?: boolean;
  includeAttachments?: boolean;
}

// Import Types
export interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: ImportError[];
  duplicates: Asset[];
}

export interface ImportError {
  row: number;
  field: string;
  message: string;
  value: any;
}

// Validation Types
export interface ValidationRule {
  field: keyof Asset;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any, asset: AssetFormData) => string | null;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Notification Types for Asset Management
export interface AssetNotification {
  id: string;
  type: 'maintenance_due' | 'warranty_expiring' | 'checkout_overdue' | 'value_threshold' | 'location_change';
  assetId: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  createdAt: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}
