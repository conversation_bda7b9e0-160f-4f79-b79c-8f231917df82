@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 224 71% 4%;

    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 20% 98%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 220 9% 46%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 220 14% 96%;
    --accent-foreground: 220 9% 46%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;

    --radius: 0.75rem;

    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 216 34% 17%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 216 34% 17%;
    --sidebar-ring: 262 83% 58%;

    --chart-1: 262 83% 58%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Enhanced Accent Colors */
    --accent-pink: 320 100% 65%;
    --accent-cyan: 190 100% 60%;
    --accent-orange: 30 100% 60%;
    --accent-emerald: 160 84% 39%;
    --accent-violet: 262 83% 58%;
    --accent-amber: 43 96% 56%;
    --accent-rose: 351 83% 61%;
    --accent-indigo: 239 84% 67%;
    --accent-teal: 173 80% 40%;
    --accent-lime: 84 81% 44%;

    /* Advanced Gradients */
    --gradient-primary: 262 83% 58%, 190 100% 60%, 320 100% 65%;
    --gradient-secondary: 43 74% 66%, 30 100% 60%, 197 37% 24%;
    --gradient-success: 160 84% 39%, 84 81% 44%, 173 80% 40%;
    --gradient-warning: 43 96% 56%, 30 100% 60%, 27 87% 67%;
    --gradient-danger: 0 84% 60%, 351 83% 61%, 320 100% 65%;
    --gradient-info: 239 84% 67%, 190 100% 60%, 173 80% 40%;

    /* Dynamic Theme Colors */
    --primary-light: 262 100% 85%;
    --primary-medium: 262 100% 70%;
    --primary-dark: 262 100% 55%;
    --primary-subtle: 262 100% 95%;
    --current-gradient: linear-gradient(135deg, hsl(262 100% 85%), hsl(262 100% 55%));

    /* Glass Effects */
    --glass-bg: 0 0% 100% / 0.8;
    --glass-border: 220 13% 91% / 0.3;
    --glass-shadow: 0 0% 0% / 0.1;

    /* Neumorphism */
    --neu-shadow-light: 220 13% 91%;
    --neu-shadow-dark: 0 0% 100%;
    --neu-inset-light: 0 0% 100%;
    --neu-inset-dark: 220 13% 91%;
  }

  .dark {
    /* Deep Dark Backgrounds (#0a0a0a, #1a1a1a) */
    --background: 0 0% 4%;
    /* #0a0a0a */
    --foreground: 210 20% 98%;

    --card: 0 0% 10%;
    /* #1a1a1a */
    --card-foreground: 210 20% 98%;

    --popover: 0 0% 8%;
    /* Slightly lighter for popover */
    --popover-foreground: 210 20% 98%;

    --primary: 262 100% 70%;
    --primary-foreground: 0 0% 4%;

    --secondary: 0 0% 15%;
    /* Subtle secondary */
    --secondary-foreground: 210 20% 98%;

    --muted: 0 0% 12%;
    /* Muted backgrounds */
    --muted-foreground: 220 9% 65%;
    /* Higher contrast for WCAG AA */

    --accent: 190 100% 60%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;

    --border: 0 0% 18%;
    /* Subtle borders */
    --input: 0 0% 12%;
    /* Input backgrounds */
    --ring: 262 100% 70%;

    --sidebar-background: 0 0% 6%;
    /* Deeper sidebar */
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 262 100% 70%;
    --sidebar-primary-foreground: 0 0% 4%;
    --sidebar-accent: 190 100% 60%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 262 100% 70%;

    --chart-1: 262 100% 70%;
    --chart-2: 190 100% 60%;
    --chart-3: 320 100% 65%;
    --chart-4: 43 74% 66%;
    --chart-5: 30 100% 60%;

    /* Enhanced Accent Colors for Dark Mode */
    --accent-pink: 320 100% 70%;
    --accent-cyan: 190 100% 65%;
    --accent-orange: 30 100% 65%;
    --accent-emerald: 160 84% 45%;
    --accent-violet: 262 100% 70%;
    --accent-amber: 43 96% 60%;
    --accent-rose: 351 83% 65%;
    --accent-indigo: 239 84% 70%;
    --accent-teal: 173 80% 45%;
    --accent-lime: 84 81% 50%;

    /* Advanced Gradients for Dark Mode */
    --gradient-primary: 262 100% 70%, 190 100% 65%, 320 100% 70%;
    --gradient-secondary: 43 74% 66%, 30 100% 65%, 0 0% 15%;
    --gradient-success: 160 84% 45%, 84 81% 50%, 173 80% 45%;
    --gradient-warning: 43 96% 60%, 30 100% 65%, 27 87% 70%;
    --gradient-danger: 0 84% 60%, 351 83% 65%, 320 100% 70%;
    --gradient-info: 239 84% 70%, 190 100% 65%, 173 80% 45%;

    /* Glass Effects for Dark Mode */
    --glass-bg: 0 0% 10% / 0.8;
    --glass-border: 0 0% 25% / 0.3;
    --glass-shadow: 0 0% 0% / 0.3;

    /* Neumorphism for Dark Mode */
    --neu-shadow-light: 0 0% 20%;
    --neu-shadow-dark: 0 0% 2%;
    --neu-inset-light: 0 0% 15%;
    --neu-inset-dark: 0 0% 5%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: normal;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
}

@layer components {
  .glass-card {
    background: linear-gradient(135deg, hsl(var(--card) / 0.7), hsl(var(--accent-cyan) / 0.3));
    box-shadow: 0 8px 32px 0 hsl(var(--background) / 0.25);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border) / 0.3);
    transition: box-shadow 0.3s, background 0.3s;
  }

  .glass-card:hover {
    box-shadow: 0 12px 40px 0 hsl(var(--primary) / 0.25);
    background: linear-gradient(135deg, hsl(var(--card) / 0.85), hsl(var(--accent-cyan) / 0.5));
  }

  .gradient-bg {
    background: linear-gradient(120deg, hsl(var(--gradient-1)), hsl(var(--gradient-2)));
    animation: gradientMove 8s ease-in-out infinite;
    background-size: 200% 200%;
  }

  /* Dynamic Theme Color Classes */
  .bg-theme-primary {
    background-color: hsl(var(--primary));
  }

  .bg-theme-primary-light {
    background-color: hsl(var(--primary-light));
  }

  .bg-theme-primary-dark {
    background-color: hsl(var(--primary-dark));
  }

  .bg-theme-primary-subtle {
    background-color: hsl(var(--primary-subtle));
  }

  .bg-theme-primary-5 {
    background-color: hsl(var(--primary) / 0.05);
  }

  .bg-theme-primary-8 {
    background-color: hsl(var(--primary) / 0.08);
  }

  .bg-theme-primary-10 {
    background-color: hsl(var(--primary) / 0.1);
  }

  .bg-theme-primary-20 {
    background-color: hsl(var(--primary) / 0.2);
  }

  .bg-theme-primary-30 {
    background-color: hsl(var(--primary) / 0.3);
  }

  .border-theme-primary {
    border-color: hsl(var(--primary));
  }

  .border-theme-primary-12 {
    border-color: hsl(var(--primary) / 0.12);
  }

  .border-theme-primary-20 {
    border-color: hsl(var(--primary) / 0.2);
  }

  .border-theme-primary-30 {
    border-color: hsl(var(--primary) / 0.3);
  }

  .text-theme-primary {
    color: hsl(var(--primary));
  }

  .text-theme-primary-light {
    color: hsl(var(--primary-light));
  }

  .text-theme-primary-dark {
    color: hsl(var(--primary-dark));
  }

  .gradient-theme-primary {
    background: linear-gradient(135deg, hsl(var(--primary-light)), hsl(var(--primary-dark)));
  }

  .gradient-theme-subtle {
    background: linear-gradient(135deg, hsl(var(--primary-subtle)), hsl(var(--primary-light)));
  }

  .gradient-theme-current {
    background: var(--current-gradient);
  }

  /* Enhanced Glassmorphism with Theme Colors */
  .glass-theme {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.05), hsl(var(--primary-light) / 0.03));
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid hsl(var(--primary) / 0.12);
    box-shadow: 0 8px 32px 0 hsl(var(--primary) / 0.1);
  }

  .glass-theme:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.08), hsl(var(--primary-light) / 0.05));
    box-shadow: 0 12px 40px 0 hsl(var(--primary) / 0.15);
  }

  @keyframes gradientMove {
    0% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  .gradient-text {
    background: linear-gradient(to right,
        hsl(var(--primary)),
        hsl(var(--chart-2)),
        hsl(var(--chart-3)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .stat-card {
    background: linear-gradient(120deg, hsl(var(--card) / 0.9), hsl(var(--primary) / 0.08));
    box-shadow: 0 4px 24px 0 hsl(var(--primary) / 0.10);
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border) / 0.2);
    transition: box-shadow 0.3s, background 0.3s, transform 0.2s;
  }

  .stat-card:hover {
    box-shadow: 0 8px 32px 0 hsl(var(--primary) / 0.18);
    background: linear-gradient(120deg, hsl(var(--card) / 1), hsl(var(--primary) / 0.15));
    transform: translateY(-4px) scale(1.02);
  }

  .stat-value {
    @apply text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent;
  }

  .stat-label {
    @apply text-sm font-medium text-muted-foreground;
  }

  .sidebar-link {
    @apply flex items-center gap-3 px-4 py-2.5 rounded-lg transition-all duration-300 relative overflow-hidden;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .sidebar-link::before {
    content: '';
    @apply absolute inset-0 scale-x-0 transition-all duration-300 origin-left rounded-lg;
    background: linear-gradient(135deg, hsl(var(--primary) / 0.1), hsl(var(--primary-light) / 0.05));
    border: 1px solid hsl(var(--primary) / 0.15);
  }

  .sidebar-link:hover::before {
    @apply scale-x-100;
    box-shadow: 0 4px 16px 0 hsl(var(--primary) / 0.1);
  }

  .sidebar-link:hover {
    transform: translateX(4px);
    box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.08);
  }

  .sidebar-link.active {
    @apply font-medium;
    background: linear-gradient(135deg, hsl(var(--primary) / 0.12), hsl(var(--primary-light) / 0.08));
    border: 1px solid hsl(var(--primary) / 0.2);
    box-shadow: 0 4px 16px 0 hsl(var(--primary) / 0.15);
    transform: translateX(6px);
  }

  .sidebar-link.active::after {
    content: '';
    @apply absolute right-0 top-1/2 -translate-y-1/2 w-1 h-6 rounded-l-full;
    background: linear-gradient(to bottom, hsl(var(--primary)), hsl(var(--primary-dark)));
    box-shadow: 0 0 8px hsl(var(--primary) / 0.5);
  }

  .sidebar-section {
    @apply py-1 space-y-1;
  }

  .sidebar-heading {
    @apply text-xs uppercase tracking-wider text-sidebar-foreground/60 px-4 py-2 font-semibold;
  }

  /* Enhanced Sidebar Animations */
  .sidebar-icon-bounce {
    animation: iconBounce 0.6s ease-in-out;
  }

  .sidebar-badge-pulse {
    animation: badgePulse 2s ease-in-out infinite;
  }

  .sidebar-gradient-shift {
    background: linear-gradient(45deg, hsl(var(--primary) / 0.1), hsl(var(--primary-light) / 0.05), hsl(var(--primary-dark) / 0.08));
    background-size: 300% 300%;
    animation: gradientShift 4s ease infinite;
  }

  /* Sidebar Glassmorphism Enhancement */
  .sidebar-glass-enhanced {
    background: linear-gradient(135deg,
        hsl(var(--sidebar-background) / 0.95),
        hsl(var(--primary) / 0.03));
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-right: 1px solid hsl(var(--primary) / 0.1);
    box-shadow:
      4px 0 24px 0 hsl(var(--primary) / 0.08),
      inset 1px 0 0 hsl(var(--primary) / 0.1);
  }

  /* Sidebar Menu Item Enhancements */
  .sidebar-menu-item {
    position: relative;
    overflow: hidden;
  }

  .sidebar-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.1), transparent);
    transition: left 0.5s ease;
  }

  .sidebar-menu-item:hover::before {
    left: 100%;
  }

  /* Status Indicator Enhancements */
  .sidebar-status-live {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    box-shadow: 0 0 12px hsl(var(--primary) / 0.4);
    animation: statusGlow 2s ease-in-out infinite alternate;
  }

  .sidebar-status-soon {
    background: linear-gradient(135deg, hsl(30 100% 60%), hsl(43 96% 56%));
    box-shadow: 0 0 12px hsl(30 100% 60% / 0.4);
    animation: statusGlow 2s ease-in-out infinite alternate;
  }

  .animated-counter {
    @apply transition-all duration-700 ease-out;
  }

  .chart-container {
    background: linear-gradient(120deg, hsl(var(--card) / 0.85), hsl(var(--accent-pink) / 0.08));
    box-shadow: 0 2px 12px 0 hsl(var(--primary) / 0.08);
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border) / 0.15);
    transition: box-shadow 0.3s, background 0.3s;
  }

  .chart-container:hover {
    box-shadow: 0 6px 24px 0 hsl(var(--primary) / 0.15);
    background: linear-gradient(120deg, hsl(var(--card) / 0.95), hsl(var(--accent-pink) / 0.15));
  }

  .floating-action {
    @apply fixed bottom-6 right-6 z-50 rounded-full bg-primary text-primary-foreground shadow-lg shadow-primary/25 hover:shadow-xl hover:shadow-primary/30 transition-all duration-300 hover:scale-110;
  }

  .nav-item {
    @apply relative group cursor-pointer;
  }

  .nav-item::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full;
  }

  .pulse-dot {
    @apply relative;
  }

  .pulse-dot::before {
    content: '';
    @apply absolute inset-0 rounded-full bg-current animate-ping opacity-75;
  }

  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::after {
    content: '';
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer;
  }
}

@layer utilities {
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .animate-gradient {
    animation: gradient 8s ease infinite;
    background-size: 400% 400%;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes iconBounce {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

@keyframes badgePulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes statusGlow {
  0% {
    box-shadow: 0 0 8px hsl(var(--primary) / 0.3);
  }

  100% {
    box-shadow: 0 0 16px hsl(var(--primary) / 0.6);
  }
}

@keyframes glow {

  0%,
  100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.5);
  }

  50% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.8), 0 0 30px hsl(var(--primary) / 0.6);
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes morphing {

  0%,
  100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }

  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

@keyframes typing {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

/* Advanced Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, hsl(var(--border)), hsl(var(--muted-foreground) / 0.5));
  border-radius: 4px;
  border: 1px solid hsl(var(--background));
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.6), hsl(var(--accent) / 0.4));
  transform: scale(1.1);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

/* Responsive grid utilities */
.responsive-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.responsive-grid-2 {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.animate-fade-in {
  animation: fadeIn 0.2s ease;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.3s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-morphing {
  animation: morphing 8s ease-in-out infinite;
}

.animate-typing {
  animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
  overflow: hidden;
  white-space: nowrap;
  border-right: 3px solid hsl(var(--primary));
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Advanced Component Styles */
.neumorphism {
  background: hsl(var(--card));
  box-shadow:
    8px 8px 16px hsl(var(--neu-shadow-dark)),
    -8px -8px 16px hsl(var(--neu-shadow-light));
  border-radius: var(--radius);
  transition: all 0.3s ease;
}

.neumorphism:hover {
  box-shadow:
    12px 12px 24px hsl(var(--neu-shadow-dark)),
    -12px -12px 24px hsl(var(--neu-shadow-light));
}

.neumorphism-inset {
  background: hsl(var(--card));
  box-shadow:
    inset 4px 4px 8px hsl(var(--neu-inset-dark)),
    inset -4px -4px 8px hsl(var(--neu-inset-light));
  border-radius: var(--radius);
}

.glass-morphism {
  background: linear-gradient(135deg, hsl(var(--glass-bg)), hsl(var(--card) / 0.6));
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid hsl(var(--glass-border));
  box-shadow: 0 8px 32px hsl(var(--glass-shadow));
  border-radius: var(--radius);
  transition: all 0.3s ease;
}

.glass-morphism:hover {
  background: linear-gradient(135deg, hsl(var(--glass-bg)), hsl(var(--card) / 0.8));
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
  box-shadow: 0 12px 40px hsl(var(--glass-shadow));
}

.frosted-glass {
  background: hsl(var(--card) / 0.7);
  backdrop-filter: blur(16px) brightness(1.1);
  -webkit-backdrop-filter: blur(16px) brightness(1.1);
  border: 1px solid hsl(var(--border) / 0.2);
  box-shadow:
    0 4px 24px hsl(var(--background) / 0.1),
    inset 0 1px 0 hsl(var(--foreground) / 0.1);
}

.gradient-border {
  position: relative;
  background: hsl(var(--card));
  border-radius: var(--radius);
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
}

.holographic {
  background: linear-gradient(45deg,
      hsl(var(--primary) / 0.8),
      hsl(var(--accent-cyan) / 0.6),
      hsl(var(--accent-pink) / 0.8),
      hsl(var(--accent-violet) / 0.6));
  background-size: 400% 400%;
  animation: gradient 8s ease infinite;
  position: relative;
  overflow: hidden;
}

.holographic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      hsl(var(--foreground) / 0.1),
      transparent);
  animation: shimmer 2s infinite;
}

.magnetic-hover {
  transition: transform 0.2s ease;
  cursor: pointer;
}

.magnetic-hover:hover {
  transform: scale(1.05) rotate(1deg);
}

.tilt-hover {
  transition: transform 0.3s ease;
  transform-style: preserve-3d;
}

.tilt-hover:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.02);
}

.floating-shadow {
  box-shadow:
    0 10px 25px hsl(var(--primary) / 0.1),
    0 20px 48px hsl(var(--primary) / 0.1),
    0 24px 48px hsl(var(--background) / 0.1);
  transition: all 0.3s ease;
}

.floating-shadow:hover {
  transform: translateY(-8px);
  box-shadow:
    0 20px 40px hsl(var(--primary) / 0.15),
    0 30px 60px hsl(var(--primary) / 0.15),
    0 40px 80px hsl(var(--background) / 0.15);
}

/* Enhanced Header Styles */
.header-enhanced {
  background: linear-gradient(135deg, hsl(var(--background) / 0.95), hsl(var(--card) / 0.8));
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid hsl(var(--border) / 0.5);
  box-shadow:
    0 4px 24px hsl(var(--background) / 0.1),
    0 8px 48px hsl(var(--primary) / 0.05);
}

.header-logo {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent-cyan)), hsl(var(--accent-pink)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.header-search {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.5), hsl(var(--card) / 0.3));
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid hsl(var(--border) / 0.3);
  transition: all 0.3s ease;
}

.header-search:focus-within {
  background: linear-gradient(135deg, hsl(var(--background) / 0.9), hsl(var(--card) / 0.6));
  border-color: hsl(var(--primary) / 0.5);
  box-shadow:
    0 0 0 3px hsl(var(--primary) / 0.1),
    0 4px 12px hsl(var(--primary) / 0.15);
}

.header-button {
  background: hsl(var(--background) / 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid hsl(var(--border) / 0.3);
  transition: all 0.2s ease;
}

.header-button:hover {
  background: hsl(var(--accent) / 0.8);
  border-color: hsl(var(--primary) / 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.15);
}

.header-notification-badge {
  background: linear-gradient(135deg, hsl(var(--destructive)), hsl(var(--accent-rose)));
  box-shadow: 0 2px 8px hsl(var(--destructive) / 0.3);
  animation: pulse 2s infinite;
}

.header-user-avatar {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent-violet)));
  box-shadow:
    0 2px 8px hsl(var(--primary) / 0.3),
    0 0 0 2px hsl(var(--background)),
    0 0 0 3px hsl(var(--primary) / 0.2);
  transition: all 0.3s ease;
}

.header-user-avatar:hover {
  transform: scale(1.05);
  box-shadow:
    0 4px 16px hsl(var(--primary) / 0.4),
    0 0 0 2px hsl(var(--background)),
    0 0 0 4px hsl(var(--primary) / 0.3);
}

.header-status-indicator {
  background: linear-gradient(135deg, hsl(var(--accent-emerald)), hsl(var(--accent-lime)));
  box-shadow:
    0 0 0 2px hsl(var(--background)),
    0 2px 4px hsl(var(--accent-emerald) / 0.3);
  animation: pulse 3s infinite;
}

/* Theme Preview Mode */
.theme-preview-mode {
  position: relative;
}

.theme-preview-mode::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
      hsl(var(--primary) / 0.05),
      hsl(var(--accent-cyan) / 0.05),
      hsl(var(--accent-pink) / 0.05),
      hsl(var(--accent-violet) / 0.05));
  background-size: 400% 400%;
  animation: gradient 8s ease infinite;
  pointer-events: none;
  z-index: -1;
}

/* High Contrast Mode */
.high-contrast {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 0 0% 5%;
  --border: 0 0% 30%;
  --primary: 210 100% 70%;
  --accent: 190 100% 70%;
}

.high-contrast * {
  text-shadow: none !important;
  box-shadow: 0 0 0 1px hsl(var(--border)) !important;
}

/* Focus Indicators */
.focus-enhanced *:focus {
  outline: 3px solid hsl(var(--primary));
  outline-offset: 2px;
  box-shadow: 0 0 0 6px hsl(var(--primary) / 0.2);
}

.focus-standard *:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 1px;
}