@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 224 71% 4%;

    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 262 83% 58%;
    --primary-foreground: 210 20% 98%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 220 9% 46%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 220 14% 96%;
    --accent-foreground: 220 9% 46%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;

    --radius: 0.75rem;

    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 216 34% 17%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 216 34% 17%;
    --sidebar-ring: 262 83% 58%;

    --chart-1: 262 83% 58%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --accent-pink: 320 100% 65%;
    --accent-cyan: 190 100% 60%;
    --accent-orange: 30 100% 60%;
    --gradient-1: 262 83% 58%, 190 100% 60%, 320 100% 65%;
    --gradient-2: 43 74% 66%, 30 100% 60%, 197 37% 24%;
  }

  .dark {
    --background: 0 0% 4%;
    --foreground: 210 20% 98%;

    --card: 0 0% 6%;
    --card-foreground: 210 20% 98%;

    --popover: 0 0% 6%;
    --popover-foreground: 210 20% 98%;

    --primary: 262 100% 70%;
    --primary-foreground: 0 0% 4%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 210 20% 98%;

    --muted: 0 0% 10%;
    --muted-foreground: 220 9% 60%;

    --accent: 190 100% 60%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;

    --border: 0 0% 12%;
    --input: 0 0% 8%;
    --ring: 262 100% 70%;

    --sidebar-background: 0 0% 3%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 262 100% 70%;
    --sidebar-primary-foreground: 0 0% 4%;
    --sidebar-accent: 190 100% 60%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 0 0% 8%;
    --sidebar-ring: 262 100% 70%;

    --chart-1: 262 100% 70%;
    --chart-2: 190 100% 60%;
    --chart-3: 320 100% 65%;
    --chart-4: 43 74% 66%;
    --chart-5: 30 100% 60%;
    --accent-pink: 320 100% 65%;
    --accent-cyan: 190 100% 60%;
    --accent-orange: 30 100% 60%;
    --accent-emerald: 160 84% 39%;
    --accent-violet: 262 83% 58%;
    --gradient-1: 262 100% 70%, 190 100% 60%, 320 100% 65%;
    --gradient-2: 43 74% 66%, 30 100% 60%, 0 0% 6%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: normal;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
}

@layer components {
  .glass-card {
    background: linear-gradient(135deg,
        hsl(var(--card) / 0.8),
        hsl(var(--accent-cyan) / 0.1),
        hsl(var(--accent-pink) / 0.05));
    box-shadow:
      0 8px 32px 0 hsl(var(--background) / 0.3),
      inset 0 1px 0 hsl(var(--foreground) / 0.1);
    backdrop-filter: blur(20px) saturate(180%) brightness(110%);
    -webkit-backdrop-filter: blur(20px) saturate(180%) brightness(110%);
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border) / 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        hsl(var(--primary) / 0.3),
        transparent);
  }

  .glass-card:hover {
    box-shadow:
      0 16px 48px 0 hsl(var(--primary) / 0.2),
      0 4px 16px 0 hsl(var(--accent-cyan) / 0.1),
      inset 0 1px 0 hsl(var(--foreground) / 0.15);
    background: linear-gradient(135deg,
        hsl(var(--card) / 0.9),
        hsl(var(--accent-cyan) / 0.15),
        hsl(var(--accent-pink) / 0.1));
    transform: translateY(-2px);
  }

  .gradient-bg {
    background: linear-gradient(120deg, hsl(var(--gradient-1)), hsl(var(--gradient-2)));
    animation: gradientMove 8s ease-in-out infinite;
    background-size: 200% 200%;
  }

  @keyframes gradientMove {
    0% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }
  }

  .gradient-text {
    background: linear-gradient(to right,
        hsl(var(--primary)),
        hsl(var(--chart-2)),
        hsl(var(--chart-3)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .stat-card {
    background: linear-gradient(145deg,
        hsl(var(--card) / 0.95),
        hsl(var(--card) / 0.8));
    box-shadow:
      0 4px 24px 0 hsl(var(--primary) / 0.08),
      inset 0 1px 0 hsl(var(--foreground) / 0.05),
      0 1px 3px 0 hsl(var(--background) / 0.1);
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border) / 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        hsl(var(--primary) / 0.03),
        transparent 50%,
        hsl(var(--accent-cyan) / 0.02));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .stat-card:hover {
    box-shadow:
      0 12px 40px 0 hsl(var(--primary) / 0.15),
      0 4px 16px 0 hsl(var(--accent-cyan) / 0.08),
      inset 0 1px 0 hsl(var(--foreground) / 0.1);
    background: linear-gradient(145deg,
        hsl(var(--card) / 1),
        hsl(var(--card) / 0.9));
    transform: translateY(-6px) scale(1.02);
    border-color: hsl(var(--primary) / 0.2);
  }

  .stat-card:hover::before {
    opacity: 1;
  }

  /* Neumorphism effect for buttons */
  .neuro-button {
    background: linear-gradient(145deg,
        hsl(var(--card) / 0.9),
        hsl(var(--muted) / 0.5));
    box-shadow:
      8px 8px 16px hsl(var(--background) / 0.3),
      -8px -8px 16px hsl(var(--foreground) / 0.05);
    border: none;
    transition: all 0.2s ease;
  }

  .neuro-button:hover {
    box-shadow:
      4px 4px 8px hsl(var(--background) / 0.4),
      -4px -4px 8px hsl(var(--foreground) / 0.08);
    transform: translateY(-1px);
  }

  .neuro-button:active {
    box-shadow:
      inset 4px 4px 8px hsl(var(--background) / 0.3),
      inset -4px -4px 8px hsl(var(--foreground) / 0.05);
    transform: translateY(0);
  }

  .stat-value {
    @apply text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent;
  }

  .stat-label {
    @apply text-sm font-medium text-muted-foreground;
  }

  .sidebar-link {
    @apply flex items-center gap-3 px-4 py-2.5 rounded-lg transition-all duration-200 hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground relative overflow-hidden;
  }

  .sidebar-link::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-sidebar-primary/20 to-transparent scale-x-0 transition-transform duration-300 origin-left;
  }

  .sidebar-link:hover::before {
    @apply scale-x-100;
  }

  .sidebar-link.active {
    @apply bg-sidebar-primary/10 text-sidebar-primary font-medium;
  }

  .sidebar-link.active::after {
    content: '';
    @apply absolute right-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-sidebar-primary rounded-l-full;
  }

  .sidebar-section {
    @apply py-1;
  }

  .sidebar-heading {
    @apply text-xs uppercase tracking-wider text-sidebar-foreground/60 px-4 py-2 font-semibold;
  }

  .animated-counter {
    @apply transition-all duration-700 ease-out;
  }

  .chart-container {
    background: linear-gradient(120deg, hsl(var(--card) / 0.85), hsl(var(--accent-pink) / 0.08));
    box-shadow: 0 2px 12px 0 hsl(var(--primary) / 0.08);
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border) / 0.15);
    transition: box-shadow 0.3s, background 0.3s;
  }

  .chart-container:hover {
    box-shadow: 0 6px 24px 0 hsl(var(--primary) / 0.15);
    background: linear-gradient(120deg, hsl(var(--card) / 0.95), hsl(var(--accent-pink) / 0.15));
  }

  .floating-action {
    @apply fixed bottom-6 right-6 z-50 rounded-full bg-primary text-primary-foreground transition-all duration-300;
    box-shadow:
      0 8px 32px hsl(var(--primary) / 0.3),
      0 4px 16px hsl(var(--primary) / 0.2),
      inset 0 1px 0 hsl(var(--foreground) / 0.1);
    backdrop-filter: blur(10px);
  }

  .floating-action:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow:
      0 16px 48px hsl(var(--primary) / 0.4),
      0 8px 24px hsl(var(--primary) / 0.3),
      inset 0 1px 0 hsl(var(--foreground) / 0.15);
  }

  .floating-action:active {
    transform: translateY(-2px) scale(1.05);
  }

  /* Frosted glass modal overlay */
  .frosted-overlay {
    background: hsl(var(--background) / 0.8);
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
  }

  /* Enhanced button with micro-interactions */
  .micro-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .micro-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, hsl(var(--primary-foreground) / 0.3) 0%, transparent 70%);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
  }

  .micro-button:hover::before {
    width: 300px;
    height: 300px;
  }

  .micro-button:active {
    transform: scale(0.98);
  }

  .nav-item {
    @apply relative group cursor-pointer;
  }

  .nav-item::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full;
  }

  .pulse-dot {
    @apply relative;
  }

  .pulse-dot::before {
    content: '';
    @apply absolute inset-0 rounded-full bg-current animate-ping opacity-75;
  }

  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::after {
    content: '';
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer;
  }
}

@layer utilities {
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .animate-gradient {
    animation: gradient 8s ease infinite;
    background-size: 400% 400%;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-rotate-slow {
    animation: rotateSlow 8s linear infinite;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .text-glow {
    text-shadow: 0 0 10px hsl(var(--primary) / 0.5);
  }

  .backdrop-blur-strong {
    backdrop-filter: blur(24px) saturate(180%) brightness(110%);
    -webkit-backdrop-filter: blur(24px) saturate(180%) brightness(110%);
  }

  .glass-border {
    border: 1px solid hsl(var(--border) / 0.2);
    background: linear-gradient(135deg,
        hsl(var(--background) / 0.8),
        hsl(var(--background) / 0.4));
  }

  .neuro-inset {
    box-shadow:
      inset 4px 4px 8px hsl(var(--background) / 0.3),
      inset -4px -4px 8px hsl(var(--foreground) / 0.05);
  }

  .neuro-raised {
    box-shadow:
      4px 4px 8px hsl(var(--background) / 0.3),
      -4px -4px 8px hsl(var(--foreground) / 0.05);
  }

  .gradient-border {
    position: relative;
    background: linear-gradient(hsl(var(--card)), hsl(var(--card))) padding-box,
      linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent-cyan))) border-box;
    border: 1px solid transparent;
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.2);
  }

  100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.6), 0 0 30px hsl(var(--primary) / 0.4);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateSlow {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

/* Responsive grid utilities */
.responsive-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.responsive-grid-2 {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.animate-fade-in {
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Compact Mode Styles */
.compact-mode {
  --spacing-unit: 0.75rem;
}

.compact-mode .stat-card {
  padding: 0.75rem;
}

.compact-mode .sidebar-link {
  padding: 0.5rem 0.75rem;
}

.compact-mode h1 {
  font-size: 1.5rem;
}

.compact-mode h2 {
  font-size: 1.25rem;
}

.compact-mode h3 {
  font-size: 1.125rem;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .glass-card {
    background: hsl(var(--card));
    backdrop-filter: none;
    border: 2px solid hsl(var(--border));
  }

  .stat-card {
    border: 2px solid hsl(var(--border));
  }
}

/* Print Styles */
@media print {

  .floating-action,
  .command-palette,
  .notification-system,
  .theme-switcher {
    display: none !important;
  }

  .glass-card,
  .stat-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}