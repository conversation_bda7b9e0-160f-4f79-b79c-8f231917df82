import {
  Home,
  Package,
  Wrench,
  DollarSign,
  Users,
  Boxes,
  ClipboardList,
  Truck,
  Shield,
  Settings,
  BarChart3,
  Building,
  Monitor,
  PieChart,
  Calendar,
  FileText,
  UserCheck,
  Zap,
  Layers,
  Clock,
  CheckCircle
} from "lucide-react";

export interface MenuItem {
  title: string;
  icon: React.ElementType;
  path?: string;
  children?: MenuItem[];
  badge?: string;
  color?: string;
  available?: boolean;
  comingSoon?: boolean;
}

export const menuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: Home,
    path: "/",
    color: "text-blue-500",
    available: true
  },
  {
    title: "Asset Management",
    icon: Package,
    color: "text-green-500",
    available: true,
    children: [
      { title: "Asset Registry", icon: Building, path: "/assets/registry", available: true },
      { title: "Asset Tracking", icon: Monitor, path: "/assets/tracking", available: true },
      { title: "Performance", icon: PieChart, path: "/assets/performance", available: true },
      { title: "Depreciation", icon: BarChart3, path: "/assets/depreciation", available: true }
    ]
  },
  {
    title: "Maintenance (CMMS)",
    icon: Wrench,
    color: "text-orange-500",
    badge: "Soon",
    comingSoon: true,
    children: [
      { title: "Work Orders", icon: ClipboardList, path: "/maintenance/work-orders", comingSoon: true },
      { title: "Preventive", icon: Calendar, path: "/maintenance/preventive", comingSoon: true },
      { title: "Predictive", icon: Monitor, path: "/maintenance/predictive", comingSoon: true },
      { title: "Spare Parts", icon: Boxes, path: "/maintenance/spare-parts", comingSoon: true }
    ]
  },
  {
    title: "Financial (ERP)",
    icon: DollarSign,
    color: "text-purple-500",
    available: true,
    children: [
      { title: "Dashboard", icon: PieChart, path: "/financial", available: true },
      { title: "General Ledger", icon: FileText, path: "/financial/gl", comingSoon: true },
      { title: "Accounts Payable", icon: DollarSign, path: "/financial/ap", comingSoon: true },
      { title: "Accounts Receivable", icon: BarChart3, path: "/financial/ar", comingSoon: true },
      { title: "Budgeting", icon: PieChart, path: "/financial/budgeting", comingSoon: true }
    ]
  },
  {
    title: "Human Resources",
    icon: Users,
    color: "text-pink-500",
    comingSoon: true,
    children: [
      { title: "Employees", icon: Users, path: "/hr/employees", comingSoon: true },
      { title: "Payroll", icon: DollarSign, path: "/hr/payroll", comingSoon: true },
      { title: "Time & Attendance", icon: Calendar, path: "/hr/attendance", comingSoon: true },
      { title: "Performance", icon: UserCheck, path: "/hr/performance", comingSoon: true },
      { title: "Training", icon: FileText, path: "/hr/training", comingSoon: true }
    ]
  },
  {
    title: "Inventory",
    icon: Boxes,
    color: "text-cyan-500",
    comingSoon: true,
    children: [
      { title: "Stock Management", icon: Boxes, path: "/inventory/stock", comingSoon: true },
      { title: "Procurement", icon: Truck, path: "/inventory/procurement", comingSoon: true },
      { title: "Warehouses", icon: Building, path: "/inventory/warehouses", comingSoon: true },
      { title: "Suppliers", icon: Users, path: "/inventory/suppliers", comingSoon: true }
    ]
  },
  {
    title: "Projects",
    icon: ClipboardList,
    color: "text-indigo-500",
    comingSoon: true,
    children: [
      { title: "Project List", icon: ClipboardList, path: "/projects/list", comingSoon: true },
      { title: "Planning", icon: Calendar, path: "/projects/planning", comingSoon: true },
      { title: "Resources", icon: Users, path: "/projects/resources", comingSoon: true },
      { title: "Timeline", icon: BarChart3, path: "/projects/timeline", comingSoon: true }
    ]
  },
  {
    title: "Fleet Management",
    icon: Truck,
    color: "text-red-500",
    comingSoon: true,
    children: [
      { title: "Vehicles", icon: Truck, path: "/fleet/vehicles", comingSoon: true },
      { title: "Maintenance", icon: Wrench, path: "/fleet/maintenance", comingSoon: true },
      { title: "Fuel Management", icon: DollarSign, path: "/fleet/fuel", comingSoon: true },
      { title: "Driver Management", icon: Users, path: "/fleet/drivers", comingSoon: true }
    ]
  },
  {
    title: "HSE & Safety",
    icon: Shield,
    color: "text-yellow-500",
    comingSoon: true,
    children: [
      { title: "Incidents", icon: FileText, path: "/hse/incidents", comingSoon: true },
      { title: "Safety Audits", icon: ClipboardList, path: "/hse/audits", comingSoon: true },
      { title: "Compliance", icon: Shield, path: "/hse/compliance", comingSoon: true },
      { title: "Training", icon: Users, path: "/hse/training", comingSoon: true }
    ]
  },
  {
    title: "Engineering",
    icon: Settings,
    color: "text-teal-500",
    comingSoon: true,
    children: [
      { title: "Mechanical", icon: Settings, path: "/engineering/mechanical", comingSoon: true },
      { title: "Electrical", icon: Zap, path: "/engineering/electrical", comingSoon: true },
      { title: "Civil", icon: Building, path: "/engineering/civil", comingSoon: true },
      { title: "Instrumentation", icon: Monitor, path: "/engineering/instrumentation", comingSoon: true }
    ]
  },
  {
    title: "Analytics & Reports",
    icon: BarChart3,
    color: "text-violet-500",
    comingSoon: true,
    children: [
      { title: "Dashboards", icon: Layers, path: "/analytics/dashboards", comingSoon: true },
      { title: "Financial Reports", icon: DollarSign, path: "/analytics/financial", comingSoon: true },
      { title: "Asset Reports", icon: Package, path: "/analytics/assets", comingSoon: true },
      { title: "Custom Reports", icon: FileText, path: "/analytics/custom", comingSoon: true }
    ]
  }
];
