import {
    Home,
    Package,
    Wrench,
    DollarSign,
    Users,
    Boxes,
    ClipboardList,
    Truck,
    Shield,
    Settings,
    BarChart3,
    Building,
    Monitor,
    PieChart,
    Calendar,
    FileText,
    UserCheck,
    Zap,
    Layers,
    Clock,
    CheckCircle,
    LayoutDashboard
} from "lucide-react";

export interface MenuItem {
    title: string;
    icon: React.ElementType;
    path?: string;
    children?: MenuItem[];
    badge?: string;
    color?: string;
    available?: boolean;
    comingSoon?: boolean;
    category?: string;
    priority?: number; // For mobile navigation ordering
}

export interface BottomNavItem {
    id: string;
    title: string;
    icon: React.ElementType;
    path: string;
    badge?: number | string;
    available?: boolean;
    comingSoon?: boolean;
}

export const menuItems: MenuItem[] = [
    {
        title: "Dashboard",
        icon: Home,
        path: "/",
        color: "text-blue-500",
        available: true,
        category: "core",
        priority: 1
    },
    {
        title: "Asset Management",
        icon: Package,
        color: "text-green-500",
        available: true,
        category: "operations",
        priority: 2,
        children: [
            { title: "Asset Registry", icon: Building, path: "/assets/registry", available: true },
            { title: "Asset Tracking", icon: Monitor, path: "/assets/tracking", available: true },
            { title: "Performance", icon: PieChart, path: "/assets/performance", available: true },
            { title: "Depreciation", icon: BarChart3, path: "/assets/depreciation", available: true }
        ]
    },
    {
        title: "Maintenance (CMMS)",
        icon: Wrench,
        color: "text-orange-500",
        badge: "Soon",
        comingSoon: true,
        category: "operations",
        priority: 6,
        children: [
            { title: "Work Orders", icon: ClipboardList, path: "/maintenance/work-orders", comingSoon: true },
            { title: "Preventive", icon: Calendar, path: "/maintenance/preventive", comingSoon: true },
            { title: "Predictive", icon: Monitor, path: "/maintenance/predictive", comingSoon: true },
            { title: "Spare Parts", icon: Boxes, path: "/maintenance/spare-parts", comingSoon: true }
        ]
    },
    {
        title: "Financial (ERP)",
        icon: DollarSign,
        color: "text-purple-500",
        available: true,
        category: "financial",
        priority: 3,
        children: [
            { title: "Dashboard", icon: PieChart, path: "/financial", available: true },
            { title: "General Ledger", icon: FileText, path: "/financial/gl", comingSoon: true },
            { title: "Accounts Payable", icon: DollarSign, path: "/financial/ap", comingSoon: true },
            { title: "Accounts Receivable", icon: BarChart3, path: "/financial/ar", comingSoon: true },
            { title: "Budgeting", icon: PieChart, path: "/financial/budgeting", comingSoon: true }
        ]
    },
    {
        title: "Human Resources",
        icon: Users,
        color: "text-pink-500",
        comingSoon: true,
        category: "management",
        priority: 7,
        children: [
            { title: "Employees", icon: Users, path: "/hr/employees", comingSoon: true },
            { title: "Payroll", icon: DollarSign, path: "/hr/payroll", comingSoon: true },
            { title: "Time & Attendance", icon: Calendar, path: "/hr/attendance", comingSoon: true },
            { title: "Performance", icon: UserCheck, path: "/hr/performance", comingSoon: true },
            { title: "Training", icon: FileText, path: "/hr/training", comingSoon: true }
        ]
    },
    {
        title: "Inventory",
        icon: Boxes,
        color: "text-cyan-500",
        comingSoon: true,
        category: "operations",
        priority: 8,
        children: [
            { title: "Stock Management", icon: Boxes, path: "/inventory/stock", comingSoon: true },
            { title: "Procurement", icon: Truck, path: "/inventory/procurement", comingSoon: true },
            { title: "Warehouses", icon: Building, path: "/inventory/warehouses", comingSoon: true },
            { title: "Suppliers", icon: Users, path: "/inventory/suppliers", comingSoon: true }
        ]
    },
    {
        title: "Projects",
        icon: ClipboardList,
        color: "text-indigo-500",
        comingSoon: true,
        category: "management",
        priority: 9,
        children: [
            { title: "Project List", icon: ClipboardList, path: "/projects/list", comingSoon: true },
            { title: "Planning", icon: Calendar, path: "/projects/planning", comingSoon: true },
            { title: "Resources", icon: Users, path: "/projects/resources", comingSoon: true },
            { title: "Timeline", icon: BarChart3, path: "/projects/timeline", comingSoon: true }
        ]
    },
    {
        title: "Fleet Management",
        icon: Truck,
        color: "text-red-500",
        comingSoon: true,
        category: "operations",
        priority: 10,
        children: [
            { title: "Vehicles", icon: Truck, path: "/fleet/vehicles", comingSoon: true },
            { title: "Maintenance", icon: Wrench, path: "/fleet/maintenance", comingSoon: true },
            { title: "Fuel Management", icon: DollarSign, path: "/fleet/fuel", comingSoon: true },
            { title: "Driver Management", icon: Users, path: "/fleet/drivers", comingSoon: true }
        ]
    },
    {
        title: "HSE & Safety",
        icon: Shield,
        color: "text-yellow-500",
        comingSoon: true,
        category: "compliance",
        priority: 11,
        children: [
            { title: "Incidents", icon: FileText, path: "/hse/incidents", comingSoon: true },
            { title: "Safety Audits", icon: ClipboardList, path: "/hse/audits", comingSoon: true },
            { title: "Compliance", icon: Shield, path: "/hse/compliance", comingSoon: true },
            { title: "Training", icon: Users, path: "/hse/training", comingSoon: true }
        ]
    },
    {
        title: "Engineering",
        icon: Settings,
        color: "text-teal-500",
        comingSoon: true,
        category: "technical",
        priority: 5,
        children: [
            { title: "Mechanical", icon: Settings, path: "/engineering/mechanical", comingSoon: true },
            { title: "Electrical", icon: Zap, path: "/engineering/electrical", comingSoon: true },
            { title: "Civil", icon: Building, path: "/engineering/civil", comingSoon: true },
            { title: "Instrumentation", icon: Monitor, path: "/engineering/instrumentation", comingSoon: true }
        ]
    },
    {
        title: "Analytics & Reports",
        icon: BarChart3,
        color: "text-violet-500",
        comingSoon: true,
        category: "analytics",
        priority: 4,
        children: [
            { title: "Dashboards", icon: LayoutDashboard, path: "/analytics/dashboards", comingSoon: true },
            { title: "Financial Reports", icon: DollarSign, path: "/analytics/financial", comingSoon: true },
            { title: "Asset Reports", icon: Package, path: "/analytics/assets", comingSoon: true },
            { title: "Custom Reports", icon: FileText, path: "/analytics/custom", comingSoon: true }
        ]
    }
];

// Category definitions for mobile navigation
export const menuCategories = {
    core: { title: "Core", icon: Home, color: "text-blue-500" },
    operations: { title: "Operations", icon: Package, color: "text-green-500" },
    financial: { title: "Financial", icon: DollarSign, color: "text-purple-500" },
    analytics: { title: "Analytics", icon: BarChart3, color: "text-violet-500" },
    management: { title: "Management", icon: Users, color: "text-pink-500" },
    technical: { title: "Technical", icon: Settings, color: "text-teal-500" },
    compliance: { title: "Compliance", icon: Shield, color: "text-yellow-500" }
};

// Primary navigation items for bottom tabs (most important/frequently used)
export const primaryNavItems: BottomNavItem[] = [
    { id: 'dashboard', title: 'Dashboard', icon: Home, path: '/', available: true },
    { id: 'assets', title: 'Assets', icon: Package, path: '/assets/registry', available: true },
    { id: 'financial', title: 'Financial', icon: DollarSign, path: '/financial', available: true },
    { id: 'analytics', title: 'Analytics', icon: BarChart3, path: '/analytics/dashboards', comingSoon: true }
];

// Get all menu items grouped by category
export const getMenuItemsByCategory = () => {
    const grouped: Record<string, MenuItem[]> = {};

    menuItems.forEach(item => {
        const category = item.category || 'other';
        if (!grouped[category]) {
            grouped[category] = [];
        }
        grouped[category].push(item);
    });

    return grouped;
};

// Search functionality
export const searchMenuItems = (query: string): MenuItem[] => {
    if (!query.trim()) return [];

    const results: MenuItem[] = [];
    const queryLower = query.toLowerCase();

    menuItems.forEach(item => {
        // Search in main item
        if (item.title.toLowerCase().includes(queryLower)) {
            results.push(item);
        }

        // Search in children
        if (item.children) {
            item.children.forEach(child => {
                if (child.title.toLowerCase().includes(queryLower)) {
                    results.push({ ...child, category: item.title });
                }
            });
        }
    });

    return results;
};

// Get recent items (mock implementation - in real app would use localStorage/state)
export const getRecentItems = (): MenuItem[] => {
    return [
        { title: "Asset Registry", icon: Building, path: "/assets/registry", available: true },
        { title: "Financial Dashboard", icon: PieChart, path: "/financial", available: true },
        { title: "Asset Performance", icon: PieChart, path: "/assets/performance", available: true }
    ];
};
