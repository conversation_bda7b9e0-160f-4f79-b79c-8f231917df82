import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/hooks/use-theme";
import { PersonalizationProvider } from "@/hooks/use-personalization";
import { CommandPaletteProvider } from "@/hooks/use-command-palette";
import { NotificationProvider } from "@/components/ui/NotificationSystem";
import { CommandPalette, useCommandPalette } from "@/components/ui/CommandPalette";
import { FloatingActionButton } from "@/components/ui/FloatingActionButton";
import { Command, Wrench, HelpCircle } from "lucide-react";
import Index from "./pages/Index";
import Financial from "./pages/Financial";
import NotFound from "./pages/NotFound";


// Note: Additional pages will be imported as they are created

const queryClient = new QueryClient();

// Component that uses command palette
const AppWithProviders = () => {
  const { isOpen, close } = useCommandPalette();

  return (
    <>
      <CommandPalette isOpen={isOpen} onClose={close} />
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/financial" element={<Financial />} />
        <Route path="*" element={<NotFound />} />
      </Routes>

      {/* Floating Action Button */}
      <FloatingActionButton
        position="bottom-right"
        variant="extended"
        actions={[
          {
            id: 'command-palette',
            label: 'Command Palette',
            icon: Command,
            onClick: () => { },
            shortcut: 'Ctrl+K',
            category: 'Navigation'
          },
          {
            id: 'new-workorder',
            label: 'New Work Order',
            icon: Wrench,
            onClick: () => { },
            category: 'Quick Actions'
          },
          {
            id: 'help',
            label: 'Help & Support',
            icon: HelpCircle,
            onClick: () => { },
            category: 'Support'
          }
        ]}
      />
    </>
  );
};

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <PersonalizationProvider>
        <ThemeProvider defaultTheme="dark">
          <NotificationProvider>
            <CommandPaletteProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                  <AppWithProviders />
                </BrowserRouter>
              </TooltipProvider>
            </CommandPaletteProvider>
          </NotificationProvider>
        </ThemeProvider>
      </PersonalizationProvider>
    </QueryClientProvider>
  );
};

export default App;
