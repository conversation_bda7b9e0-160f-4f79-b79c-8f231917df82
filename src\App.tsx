import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/hooks/use-theme";
import { ThemeColorsProvider } from "@/hooks/use-theme-colors";

import Index from "./pages/Index";
import Financial from "./pages/Financial";
import NotFound from "./pages/NotFound";
import ComingSoon from "./pages/ComingSoon";
import AssetRegistry from "./pages/assets/AssetRegistry";
import AssetTracking from "./pages/assets/AssetTracking";
import AssetPerformance from "./pages/assets/AssetPerformance";
import AssetDepreciation from "./pages/assets/AssetDepreciation";
import { ThemeColorTest } from "@/components/debug/ThemeColorTest";


// Note: Additional pages will be imported as they are created

const queryClient = new QueryClient();



const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="dark">
        <ThemeColorsProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                {/* Main Pages */}
                <Route path="/" element={<Index />} />
                <Route path="/financial" element={<Financial />} />

                {/* Asset Management Routes */}
                <Route path="/assets/registry" element={<AssetRegistry />} />
                <Route path="/assets/tracking" element={<AssetTracking />} />
                <Route path="/assets/performance" element={<AssetPerformance />} />
                <Route path="/assets/depreciation" element={<AssetDepreciation />} />
                <Route path="/assets/*" element={<ComingSoon />} />

                {/* Maintenance Routes */}
                <Route path="/maintenance/*" element={<ComingSoon />} />

                {/* Financial Sub-routes */}
                <Route path="/financial/gl" element={<ComingSoon />} />
                <Route path="/financial/ap" element={<ComingSoon />} />
                <Route path="/financial/ar" element={<ComingSoon />} />
                <Route path="/financial/budgeting" element={<ComingSoon />} />

                {/* HR Routes */}
                <Route path="/hr/*" element={<ComingSoon />} />

                {/* Inventory Routes */}
                <Route path="/inventory/*" element={<ComingSoon />} />

                {/* Project Routes */}
                <Route path="/projects/*" element={<ComingSoon />} />

                {/* Fleet Routes */}
                <Route path="/fleet/*" element={<ComingSoon />} />

                {/* HSE Routes */}
                <Route path="/hse/*" element={<ComingSoon />} />

                {/* Engineering Routes */}
                <Route path="/engineering/*" element={<ComingSoon />} />

                {/* Analytics Routes */}
                <Route path="/analytics/*" element={<ComingSoon />} />

                {/* Debug Routes */}
                <Route path="/debug/theme-colors" element={<ThemeColorTest />} />

                {/* 404 Route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </ThemeColorsProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
