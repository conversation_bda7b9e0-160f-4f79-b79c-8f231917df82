import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

export interface ThemeColor {
  name: string;
  value: string; // HSL format like "262 100% 70%"
  preview: string; // Hex format for preview
  variants: {
    light: string;
    medium: string;
    dark: string;
    subtle: string;
  };
}

export const THEME_COLORS: ThemeColor[] = [
  {
    name: 'Violet',
    value: '262 100% 70%',
    preview: '#8B5CF6',
    variants: {
      light: '262 100% 85%',
      medium: '262 100% 70%',
      dark: '262 100% 55%',
      subtle: '262 100% 95%'
    }
  },
  {
    name: 'Cyan',
    value: '190 100% 60%',
    preview: '#06B6D4',
    variants: {
      light: '190 100% 75%',
      medium: '190 100% 60%',
      dark: '190 100% 45%',
      subtle: '190 100% 90%'
    }
  },
  {
    name: 'Pink',
    value: '320 100% 65%',
    preview: '#EC4899',
    variants: {
      light: '320 100% 80%',
      medium: '320 100% 65%',
      dark: '320 100% 50%',
      subtle: '320 100% 92%'
    }
  },
  {
    name: 'Orange',
    value: '30 100% 60%',
    preview: '#F97316',
    variants: {
      light: '30 100% 75%',
      medium: '30 100% 60%',
      dark: '30 100% 45%',
      subtle: '30 100% 90%'
    }
  },
  {
    name: 'Emerald',
    value: '160 84% 39%',
    preview: '#10B981',
    variants: {
      light: '160 84% 54%',
      medium: '160 84% 39%',
      dark: '160 84% 24%',
      subtle: '160 84% 85%'
    }
  },
  {
    name: 'Amber',
    value: '43 96% 56%',
    preview: '#F59E0B',
    variants: {
      light: '43 96% 71%',
      medium: '43 96% 56%',
      dark: '43 96% 41%',
      subtle: '43 96% 88%'
    }
  },
  {
    name: 'Rose',
    value: '351 83% 61%',
    preview: '#F43F5E',
    variants: {
      light: '351 83% 76%',
      medium: '351 83% 61%',
      dark: '351 83% 46%',
      subtle: '351 83% 90%'
    }
  },
  {
    name: 'Indigo',
    value: '239 84% 67%',
    preview: '#6366F1',
    variants: {
      light: '239 84% 82%',
      medium: '239 84% 67%',
      dark: '239 84% 52%',
      subtle: '239 84% 92%'
    }
  },
  {
    name: 'Teal',
    value: '173 80% 40%',
    preview: '#14B8A6',
    variants: {
      light: '173 80% 55%',
      medium: '173 80% 40%',
      dark: '173 80% 25%',
      subtle: '173 80% 85%'
    }
  },
  {
    name: 'Lime',
    value: '84 81% 44%',
    preview: '#84CC16',
    variants: {
      light: '84 81% 59%',
      medium: '84 81% 44%',
      dark: '84 81% 29%',
      subtle: '84 81% 85%'
    }
  }
];

export interface GradientTheme {
  name: string;
  colors: string[];
  preview: string[];
}

export const GRADIENT_THEMES: GradientTheme[] = [
  {
    name: 'Aurora',
    colors: ['262 100% 70%', '190 100% 60%', '320 100% 65%'],
    preview: ['#8B5CF6', '#06B6D4', '#EC4899']
  },
  {
    name: 'Sunset',
    colors: ['30 100% 60%', '351 83% 61%', '43 96% 56%'],
    preview: ['#F97316', '#F43F5E', '#F59E0B']
  },
  {
    name: 'Ocean',
    colors: ['239 84% 67%', '190 100% 60%', '173 80% 40%'],
    preview: ['#6366F1', '#06B6D4', '#14B8A6']
  },
  {
    name: 'Forest',
    colors: ['160 84% 39%', '84 81% 44%', '173 80% 40%'],
    preview: ['#10B981', '#84CC16', '#14B8A6']
  }
];

interface ThemeColorsContextType {
  currentColor: ThemeColor;
  currentGradient: GradientTheme | null;
  setThemeColor: (color: ThemeColor) => void;
  setGradientTheme: (gradient: GradientTheme | null) => void;
  applyColorToElement: (element: HTMLElement, variant?: keyof ThemeColor['variants']) => void;
  getColorClass: (variant?: keyof ThemeColor['variants'], opacity?: number) => string;
  getGradientClass: () => string;
}

const ThemeColorsContext = createContext<ThemeColorsContextType | undefined>(undefined);

const STORAGE_KEYS = {
  COLOR: 'ui-theme-color',
  GRADIENT: 'ui-theme-gradient'
};

export const ThemeColorsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentColor, setCurrentColor] = useState<ThemeColor>(() => {
    const stored = localStorage.getItem(STORAGE_KEYS.COLOR);
    if (stored) {
      const found = THEME_COLORS.find(c => c.value === stored);
      if (found) return found;
    }
    return THEME_COLORS[0]; // Default to Violet
  });

  const [currentGradient, setCurrentGradient] = useState<GradientTheme | null>(() => {
    const stored = localStorage.getItem(STORAGE_KEYS.GRADIENT);
    if (stored) {
      const found = GRADIENT_THEMES.find(g => g.name === stored);
      if (found) return found;
    }
    return null;
  });

  const setThemeColor = (color: ThemeColor) => {
    setCurrentColor(color);
    localStorage.setItem(STORAGE_KEYS.COLOR, color.value);
  };

  const setGradientTheme = (gradient: GradientTheme | null) => {
    setCurrentGradient(gradient);
    if (gradient) {
      localStorage.setItem(STORAGE_KEYS.GRADIENT, gradient.name);
    } else {
      localStorage.removeItem(STORAGE_KEYS.GRADIENT);
    }
  };

  const applyColorToElement = (element: HTMLElement, variant: keyof ThemeColor['variants'] = 'medium') => {
    const colorValue = currentColor.variants[variant];
    element.style.setProperty('--dynamic-color', colorValue);
  };

  const getColorClass = (variant: keyof ThemeColor['variants'] = 'medium', opacity?: number) => {
    const colorValue = currentColor.variants[variant];
    if (opacity !== undefined) {
      return `hsl(${colorValue} / ${opacity})`;
    }
    return `hsl(${colorValue})`;
  };

  const getGradientClass = () => {
    if (currentGradient) {
      return `linear-gradient(135deg, ${currentGradient.colors.map(c => `hsl(${c})`).join(', ')})`;
    }
    return `linear-gradient(135deg, hsl(${currentColor.variants.light}), hsl(${currentColor.variants.dark}))`;
  };

  // Apply theme colors to CSS custom properties
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply primary color and variants
    root.style.setProperty('--primary', currentColor.value);
    root.style.setProperty('--primary-light', currentColor.variants.light);
    root.style.setProperty('--primary-medium', currentColor.variants.medium);
    root.style.setProperty('--primary-dark', currentColor.variants.dark);
    root.style.setProperty('--primary-subtle', currentColor.variants.subtle);
    
    // Apply accent color (same as primary for consistency)
    root.style.setProperty('--accent', currentColor.value);
    
    // Apply gradient if selected
    if (currentGradient) {
      root.style.setProperty('--gradient-primary', currentGradient.colors.join(', '));
      root.style.setProperty('--current-gradient', `linear-gradient(135deg, ${currentGradient.colors.map(c => `hsl(${c})`).join(', ')})`);
    } else {
      root.style.setProperty('--current-gradient', `linear-gradient(135deg, hsl(${currentColor.variants.light}), hsl(${currentColor.variants.dark}))`);
    }

    // Apply theme color classes to body for global access
    root.setAttribute('data-theme-color', currentColor.name.toLowerCase());
    if (currentGradient) {
      root.setAttribute('data-theme-gradient', currentGradient.name.toLowerCase());
    } else {
      root.removeAttribute('data-theme-gradient');
    }

  }, [currentColor, currentGradient]);

  const value: ThemeColorsContextType = {
    currentColor,
    currentGradient,
    setThemeColor,
    setGradientTheme,
    applyColorToElement,
    getColorClass,
    getGradientClass
  };

  return (
    <ThemeColorsContext.Provider value={value}>
      {children}
    </ThemeColorsContext.Provider>
  );
};

export const useThemeColors = (): ThemeColorsContextType => {
  const context = useContext(ThemeColorsContext);
  if (!context) {
    throw new Error('useThemeColors must be used within a ThemeColorsProvider');
  }
  return context;
};

// Utility hook for getting dynamic color classes
export const useDynamicColors = () => {
  const { getColorClass, getGradientClass, currentColor } = useThemeColors();
  
  return {
    // Background colors with glassmorphism
    bgPrimary: (opacity = 0.1) => `bg-[hsl(var(--primary)/${opacity})]`,
    bgPrimaryLight: (opacity = 0.05) => `bg-[hsl(var(--primary-light)/${opacity})]`,
    bgPrimarySubtle: (opacity = 0.03) => `bg-[hsl(var(--primary-subtle)/${opacity})]`,
    
    // Border colors
    borderPrimary: (opacity = 0.2) => `border-[hsl(var(--primary)/${opacity})]`,
    borderPrimaryLight: (opacity = 0.15) => `border-[hsl(var(--primary-light)/${opacity})]`,
    
    // Text colors
    textPrimary: 'text-[hsl(var(--primary))]',
    textPrimaryLight: 'text-[hsl(var(--primary-light))]',
    textPrimaryDark: 'text-[hsl(var(--primary-dark))]',
    
    // Gradient backgrounds
    gradientPrimary: 'bg-gradient-to-r from-[hsl(var(--primary-light))] to-[hsl(var(--primary-dark))]',
    gradientSubtle: 'bg-gradient-to-r from-[hsl(var(--primary-subtle))] to-[hsl(var(--primary-light))]',
    
    // Utility functions
    getColorClass,
    getGradientClass,
    currentColor
  };
};
