
import { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'stable';
  className?: string;
}

export function StatCard({ title, value, icon, description, trend, className }: StatCardProps) {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '↗';
      case 'down': return '↘';
      case 'stable': return '→';
      default: return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-500';
      case 'down': return 'text-red-500';
      case 'stable': return 'text-yellow-500';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <Card className={cn("stat-card group cursor-pointer", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
          {title}
        </CardTitle>
        {icon && (
          <div className="text-muted-foreground group-hover:text-primary transition-all duration-200 group-hover:scale-110">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="stat-value animated-counter group-hover:scale-105 transition-transform duration-200">
          {value}
        </div>
        {description && (
          <p className="stat-label mt-1 group-hover:text-muted-foreground/80 transition-colors">
            {description}
          </p>
        )}
        {trend && (
          <div className="flex items-center gap-2 mt-3">
            <div className={cn(
              "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium transition-all duration-200",
              "bg-muted/50 group-hover:bg-muted",
              getTrendColor()
            )}>
              <span className="animate-pulse">{getTrendIcon()}</span>
              <span className="capitalize">{trend}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
