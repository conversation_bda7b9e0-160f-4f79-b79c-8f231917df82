import React, { useState } from 'react';
import { 
  Plus, 
  Wrench, 
  Users, 
  FileText, 
  AlertTriangle, 
  Package,
  Calculator,
  MessageCircle,
  Zap,
  X
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from './button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

interface FloatingAction {
  id: string;
  label: string;
  icon: React.ElementType;
  action: () => void;
  color: string;
  shortcut?: string;
}

export const FloatingActionButton: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();

  const actions: FloatingAction[] = [
    {
      id: 'new-workorder',
      label: 'Create Work Order',
      icon: Wrench,
      action: () => navigate('/maintenance/work-orders'),
      color: 'bg-orange-500 hover:bg-orange-600',
      shortcut: 'W'
    },
    {
      id: 'add-employee',
      label: 'Add Employee',
      icon: Users,
      action: () => navigate('/hr/employees'),
      color: 'bg-blue-500 hover:bg-blue-600',
      shortcut: 'E'
    },
    {
      id: 'report-incident',
      label: 'Report Incident',
      icon: AlertTriangle,
      action: () => navigate('/hse/incidents'),
      color: 'bg-red-500 hover:bg-red-600',
      shortcut: 'I'
    },
    {
      id: 'add-asset',
      label: 'Register Asset',
      icon: Package,
      action: () => navigate('/assets/registry'),
      color: 'bg-green-500 hover:bg-green-600',
      shortcut: 'A'
    },
    {
      id: 'financial-entry',
      label: 'Financial Entry',
      icon: Calculator,
      action: () => navigate('/financial/gl'),
      color: 'bg-purple-500 hover:bg-purple-600',
      shortcut: 'F'
    },
    {
      id: 'quick-note',
      label: 'Quick Note',
      icon: FileText,
      action: () => {
        // Open quick note modal
        console.log('Quick note');
      },
      color: 'bg-indigo-500 hover:bg-indigo-600',
      shortcut: 'N'
    }
  ];

  const handleMainClick = () => {
    setIsExpanded(!isExpanded);
  };

  const handleActionClick = (action: FloatingAction) => {
    action.action();
    setIsExpanded(false);
  };

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey && e.shiftKey) {
        const action = actions.find(a => a.shortcut?.toLowerCase() === e.key.toLowerCase());
        if (action) {
          e.preventDefault();
          action.action();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <TooltipProvider>
      <div className="fixed bottom-6 right-6 z-50">
        {/* Action Items */}
        <div className={`flex flex-col-reverse gap-3 mb-3 transition-all duration-300 ${
          isExpanded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
        }`}>
          {actions.map((action, index) => (
            <Tooltip key={action.id}>
              <TooltipTrigger asChild>
                <Button
                  size="lg"
                  className={`
                    w-14 h-14 rounded-full shadow-lg transition-all duration-300 hover:scale-110 text-white
                    ${action.color}
                    animate-slide-up
                  `}
                  style={{ 
                    animationDelay: `${index * 50}ms`,
                    animationFillMode: 'both'
                  }}
                  onClick={() => handleActionClick(action)}
                >
                  <action.icon className="w-6 h-6" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left" className="glass-card backdrop-blur-strong">
                <div className="flex items-center gap-2">
                  <span>{action.label}</span>
                  {action.shortcut && (
                    <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">
                      Alt+Shift+{action.shortcut}
                    </kbd>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Main FAB */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="lg"
              className={`
                floating-action w-16 h-16 rounded-full transition-all duration-300
                ${isExpanded ? 'rotate-45' : 'rotate-0'}
              `}
              onClick={handleMainClick}
            >
              {isExpanded ? (
                <X className="w-6 h-6" />
              ) : (
                <Plus className="w-6 h-6" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left" className="glass-card backdrop-blur-strong">
            <span>{isExpanded ? 'Close menu' : 'Quick actions'}</span>
          </TooltipContent>
        </Tooltip>

        {/* Speed dial backdrop */}
        {isExpanded && (
          <div 
            className="fixed inset-0 -z-10"
            onClick={() => setIsExpanded(false)}
          />
        )}
      </div>
    </TooltipProvider>
  );
};

// Alternative compact version for mobile
export const CompactFloatingActionButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const quickActions = [
    { label: 'Work Order', icon: Wrench, action: () => navigate('/maintenance/work-orders') },
    { label: 'Incident', icon: AlertTriangle, action: () => navigate('/hse/incidents') },
    { label: 'Employee', icon: Users, action: () => navigate('/hr/employees') },
  ];

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isOpen && (
        <div className="absolute bottom-16 right-0 glass-card backdrop-blur-strong border border-border/20 rounded-xl shadow-2xl p-2 animate-scale-in">
          <div className="flex flex-col gap-1">
            {quickActions.map(action => (
              <Button
                key={action.label}
                variant="ghost"
                size="sm"
                className="justify-start gap-2 h-10"
                onClick={() => {
                  action.action();
                  setIsOpen(false);
                }}
              >
                <action.icon className="w-4 h-4" />
                {action.label}
              </Button>
            ))}
          </div>
        </div>
      )}

      <Button
        size="lg"
        className="floating-action w-14 h-14 rounded-full"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="w-5 h-5" /> : <Zap className="w-5 h-5" />}
      </Button>
    </div>
  );
};
