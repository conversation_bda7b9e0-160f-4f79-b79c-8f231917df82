import React, { useState, useRef, useEffect } from 'react';
import { 
  Plus, 
  Zap, 
  MessageCircle, 
  Settings, 
  HelpCircle,
  Search,
  Command,
  Bell,
  Bookmark,
  Share2,
  Download,
  Upload,
  RefreshCw,
  ChevronUp,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface FABAction {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  onClick: () => void;
  color?: string;
  badge?: string | number;
  shortcut?: string;
  category?: string;
}

interface FloatingActionButtonProps {
  actions?: FABAction[];
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'extended';
  primaryAction?: {
    icon: React.ComponentType<any>;
    onClick: () => void;
    label?: string;
  };
  className?: string;
  disabled?: boolean;
}

const defaultActions: FABAction[] = [
  {
    id: 'search',
    label: 'Quick Search',
    icon: Search,
    onClick: () => {},
    shortcut: 'Ctrl+K',
    category: 'Navigation'
  },
  {
    id: 'command',
    label: 'Command Palette',
    icon: Command,
    onClick: () => {},
    shortcut: 'Ctrl+Shift+P',
    category: 'Navigation'
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    onClick: () => {},
    badge: 3,
    category: 'System'
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: HelpCircle,
    onClick: () => {},
    category: 'Support'
  },
  {
    id: 'settings',
    label: 'Quick Settings',
    icon: Settings,
    onClick: () => {},
    category: 'System'
  },
];

const positionClasses = {
  'bottom-right': 'bottom-6 right-6',
  'bottom-left': 'bottom-6 left-6',
  'top-right': 'top-6 right-6',
  'top-left': 'top-6 left-6',
};

const sizeClasses = {
  sm: { main: 'w-12 h-12', action: 'w-10 h-10', icon: 'h-4 w-4' },
  md: { main: 'w-14 h-14', action: 'w-12 h-12', icon: 'h-5 w-5' },
  lg: { main: 'w-16 h-16', action: 'w-14 h-14', icon: 'h-6 w-6' },
};

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  actions = defaultActions,
  position = 'bottom-right',
  size = 'md',
  variant = 'default',
  primaryAction,
  className = '',
  disabled = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [hoveredAction, setHoveredAction] = useState<string | null>(null);
  const fabRef = useRef<HTMLDivElement>(null);
  const sizeConfig = sizeClasses[size];

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fabRef.current && !fabRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isExpanded]);

  // Close on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => document.removeEventListener('keydown', handleEscape);
  }, [isExpanded]);

  const handleMainClick = () => {
    if (primaryAction) {
      primaryAction.onClick();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleActionClick = (action: FABAction) => {
    action.onClick();
    setIsExpanded(false);
  };

  const getActionPosition = (index: number) => {
    const isBottom = position.includes('bottom');
    const isRight = position.includes('right');
    
    const spacing = size === 'sm' ? 60 : size === 'md' ? 70 : 80;
    const offset = (index + 1) * spacing;
    
    if (isBottom) {
      return { bottom: offset };
    } else {
      return { top: offset };
    }
  };

  const MainIcon = primaryAction?.icon || (isExpanded ? X : Plus);

  // Group actions by category for better organization
  const groupedActions = actions.reduce((acc, action) => {
    const category = action.category || 'General';
    if (!acc[category]) acc[category] = [];
    acc[category].push(action);
    return acc;
  }, {} as Record<string, FABAction[]>);

  if (disabled) return null;

  return (
    <div
      ref={fabRef}
      className={`fixed ${positionClasses[position]} z-50 ${className}`}
    >
      {/* Action Items */}
      {isExpanded && (
        <div className="absolute bottom-0 right-0 space-y-3 mb-20">
          {variant === 'extended' ? (
            // Extended view with categories
            <div className="bg-card/95 backdrop-blur-xl border border-border rounded-xl shadow-2xl p-4 min-w-64 max-w-sm animate-fade-in">
              <div className="space-y-4">
                {Object.entries(groupedActions).map(([category, categoryActions]) => (
                  <div key={category}>
                    <div className="text-xs font-semibold text-muted-foreground mb-2 px-2">
                      {category}
                    </div>
                    <div className="space-y-1">
                      {categoryActions.map((action) => (
                        <button
                          key={action.id}
                          onClick={() => handleActionClick(action)}
                          onMouseEnter={() => setHoveredAction(action.id)}
                          onMouseLeave={() => setHoveredAction(null)}
                          className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-all duration-200 text-left group"
                        >
                          <div className={`${sizeConfig.action} rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors`}>
                            <action.icon className={`${sizeConfig.icon} text-primary`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm">{action.label}</div>
                            {action.shortcut && (
                              <div className="text-xs text-muted-foreground font-mono">
                                {action.shortcut}
                              </div>
                            )}
                          </div>
                          {action.badge && (
                            <Badge variant="secondary" className="text-xs">
                              {action.badge}
                            </Badge>
                          )}
                        </button>
                      ))}
                    </div>
                    {Object.keys(groupedActions).indexOf(category) < Object.keys(groupedActions).length - 1 && (
                      <Separator className="my-3" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // Compact floating buttons
            actions.map((action, index) => (
              <div
                key={action.id}
                className="relative animate-fade-in"
                style={{
                  animationDelay: `${index * 50}ms`,
                  ...getActionPosition(index),
                }}
              >
                {/* Tooltip */}
                {hoveredAction === action.id && (
                  <div className="absolute right-16 top-1/2 -translate-y-1/2 bg-card/95 backdrop-blur-sm border border-border rounded-lg px-3 py-2 shadow-lg whitespace-nowrap animate-fade-in">
                    <div className="text-sm font-medium">{action.label}</div>
                    {action.shortcut && (
                      <div className="text-xs text-muted-foreground font-mono">
                        {action.shortcut}
                      </div>
                    )}
                  </div>
                )}
                
                <Button
                  size="icon"
                  onClick={() => handleActionClick(action)}
                  onMouseEnter={() => setHoveredAction(action.id)}
                  onMouseLeave={() => setHoveredAction(null)}
                  className={`${sizeConfig.action} rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 bg-card/90 backdrop-blur-sm border border-border/50 hover:bg-card relative`}
                  style={{ backgroundColor: action.color }}
                >
                  <action.icon className={`${sizeConfig.icon} text-foreground`} />
                  {action.badge && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                    >
                      {action.badge}
                    </Badge>
                  )}
                </Button>
              </div>
            ))
          )}
        </div>
      )}

      {/* Main FAB */}
      <Button
        size="icon"
        onClick={handleMainClick}
        className={`${sizeConfig.main} rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 bg-primary text-primary-foreground relative group ${
          isExpanded ? 'rotate-45' : ''
        }`}
      >
        <MainIcon className={`${sizeConfig.icon} transition-transform duration-300`} />
        
        {/* Pulse animation for attention */}
        {!isExpanded && (
          <div className="absolute inset-0 rounded-full bg-primary animate-ping opacity-20"></div>
        )}
        
        {/* Extended label */}
        {variant === 'extended' && primaryAction?.label && !isExpanded && (
          <div className="absolute right-full mr-3 bg-card/95 backdrop-blur-sm border border-border rounded-lg px-3 py-2 shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="text-sm font-medium">{primaryAction.label}</div>
          </div>
        )}
      </Button>

      {/* Speed dial indicator */}
      {!primaryAction && !isExpanded && actions.length > 0 && (
        <div className="absolute -top-2 -right-2">
          <Badge variant="secondary" className="h-5 w-5 p-0 flex items-center justify-center text-xs">
            {actions.length}
          </Badge>
        </div>
      )}
    </div>
  );
};

// Predefined FAB configurations
export const fabConfigs = {
  dashboard: {
    primaryAction: {
      icon: Plus,
      onClick: () => {},
      label: 'Quick Actions'
    },
    actions: [
      {
        id: 'new-workorder',
        label: 'New Work Order',
        icon: Plus,
        onClick: () => {},
        color: '#F97316',
        category: 'Create'
      },
      {
        id: 'search',
        label: 'Search',
        icon: Search,
        onClick: () => {},
        shortcut: 'Ctrl+K',
        category: 'Navigation'
      },
      {
        id: 'notifications',
        label: 'Notifications',
        icon: Bell,
        onClick: () => {},
        badge: 3,
        category: 'System'
      },
    ]
  },
  
  financial: {
    primaryAction: {
      icon: Download,
      onClick: () => {},
      label: 'Export Report'
    },
    actions: [
      {
        id: 'refresh',
        label: 'Refresh Data',
        icon: RefreshCw,
        onClick: () => {},
        category: 'Data'
      },
      {
        id: 'share',
        label: 'Share Report',
        icon: Share2,
        onClick: () => {},
        category: 'Actions'
      },
      {
        id: 'bookmark',
        label: 'Bookmark View',
        icon: Bookmark,
        onClick: () => {},
        category: 'Actions'
      },
    ]
  },
  
  minimal: {
    primaryAction: {
      icon: MessageCircle,
      onClick: () => {},
      label: 'Help'
    },
    actions: []
  }
};

// Helper hook for FAB state management
export const useFloatingActionButton = (initialExpanded = false) => {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const [actions, setActions] = useState<FABAction[]>([]);

  const addAction = (action: FABAction) => {
    setActions(prev => [...prev, action]);
  };

  const removeAction = (id: string) => {
    setActions(prev => prev.filter(action => action.id !== id));
  };

  const updateAction = (id: string, updates: Partial<FABAction>) => {
    setActions(prev => prev.map(action => 
      action.id === id ? { ...action, ...updates } : action
    ));
  };

  return {
    isExpanded,
    setIsExpanded,
    actions,
    addAction,
    removeAction,
    updateAction,
  };
};
