import React, { useState, useRef, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Home, Package, BarChart3, Settings, Plus, 
  ChevronUp, Search, Bell, User, Menu
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDynamicColors } from '@/hooks/use-theme-colors';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface BottomNavItem {
  id: string;
  title: string;
  icon: React.ElementType;
  path: string;
  badge?: number;
  isActive?: boolean;
}

interface BottomNavigationProps {
  onMenuToggle: () => void;
}

const primaryNavItems: BottomNavItem[] = [
  { id: 'dashboard', title: 'Dashboard', icon: Home, path: '/' },
  { id: 'assets', title: 'Assets', icon: Package, path: '/assets/registry' },
  { id: 'analytics', title: 'Analytics', icon: BarChart3, path: '/analytics' },
  { id: 'settings', title: 'Settings', icon: Settings, path: '/settings' }
];

export const BottomNavigation: React.FC<BottomNavigationProps> = ({ onMenuToggle }) => {
  const dynamicColors = useDynamicColors();
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeRipple, setActiveRipple] = useState<{ x: number; y: number; id: string } | null>(null);
  const bottomNavRef = useRef<HTMLDivElement>(null);

  const handleItemClick = (event: React.MouseEvent, item: BottomNavItem) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setActiveRipple({ x, y, id: item.id });
    
    // Clear ripple after animation
    setTimeout(() => setActiveRipple(null), 600);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Haptic feedback simulation
  const triggerHaptic = () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (bottomNavRef.current && !bottomNavRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  return (
    <>
      {/* Backdrop for expanded state */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-all duration-300"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* Bottom Navigation Container */}
      <div 
        ref={bottomNavRef}
        className="fixed bottom-0 left-0 right-0 z-50 pb-safe"
      >
        {/* Expanded Menu Drawer */}
        {isExpanded && (
          <div 
            className="absolute bottom-full left-4 right-4 mb-4 rounded-2xl border shadow-2xl overflow-hidden"
            style={{
              background: `linear-gradient(135deg, 
                hsl(var(--background) / 0.95), 
                ${dynamicColors.getColorHsl('subtle', 0.1)}
              )`,
              backdropFilter: 'blur(24px) saturate(180%)',
              ...dynamicColors.borderPrimary(0.15)
            }}
          >
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-lg" style={dynamicColors.textPrimary}>
                  Quick Access
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleExpanded}
                  className="rounded-full"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="grid grid-cols-4 gap-4">
                {[
                  { icon: Search, label: 'Search', action: () => {} },
                  { icon: Bell, label: 'Notifications', badge: 3, action: () => {} },
                  { icon: User, label: 'Profile', action: () => {} },
                  { icon: Menu, label: 'More', action: onMenuToggle }
                ].map((item, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      item.action();
                      triggerHaptic();
                      setIsExpanded(false);
                    }}
                    className="flex flex-col items-center gap-2 p-3 rounded-xl transition-all duration-200 hover:scale-105"
                    style={{
                      background: `linear-gradient(135deg, 
                        ${dynamicColors.getColorHsl('light', 0.1)}, 
                        ${dynamicColors.getColorHsl('subtle', 0.05)}
                      )`
                    }}
                  >
                    <div className="relative">
                      <item.icon className="h-5 w-5" style={dynamicColors.textPrimary} />
                      {item.badge && (
                        <Badge 
                          className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs border"
                          style={{
                            ...dynamicColors.bgPrimary(0.9),
                            color: 'white',
                            ...dynamicColors.borderPrimary(0.3)
                          }}
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs font-medium">{item.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Main Bottom Navigation Bar */}
        <div 
          className="mx-4 mb-4 rounded-2xl border shadow-2xl overflow-hidden"
          style={{
            background: `linear-gradient(135deg, 
              hsl(var(--background) / 0.95), 
              ${dynamicColors.getColorHsl('subtle', 0.08)}
            )`,
            backdropFilter: 'blur(24px) saturate(180%)',
            ...dynamicColors.borderPrimary(0.15),
            boxShadow: `
              0 20px 40px -12px ${dynamicColors.getColorHsl('medium', 0.25)},
              0 8px 16px -8px ${dynamicColors.getColorHsl('medium', 0.15)},
              inset 0 1px 0 ${dynamicColors.getColorHsl('light', 0.1)}
            `
          }}
        >
          <div className="flex items-center justify-between px-2 py-2">
            {primaryNavItems.map((item, index) => (
              <NavLink
                key={item.id}
                to={item.path}
                onClick={(e) => {
                  handleItemClick(e, item);
                  triggerHaptic();
                }}
                className={({ isActive }) => cn(
                  "relative flex flex-col items-center gap-1 px-4 py-3 rounded-xl transition-all duration-300 overflow-hidden",
                  "min-w-[64px] min-h-[64px] justify-center",
                  isActive && "scale-105"
                )}
                style={({ isActive }) => isActive ? {
                  background: `linear-gradient(135deg, 
                    ${dynamicColors.getColorHsl('medium', 0.15)}, 
                    ${dynamicColors.getColorHsl('light', 0.1)}
                  )`,
                  boxShadow: `
                    0 4px 12px ${dynamicColors.getColorHsl('medium', 0.2)},
                    inset 0 1px 0 ${dynamicColors.getColorHsl('light', 0.2)}
                  `
                } : {}}
              >
                {({ isActive }) => (
                  <>
                    {/* Ripple Effect */}
                    {activeRipple && activeRipple.id === item.id && (
                      <div
                        className="absolute inset-0 pointer-events-none"
                        style={{
                          background: `radial-gradient(circle at ${activeRipple.x}px ${activeRipple.y}px, 
                            ${dynamicColors.getColorHsl('medium', 0.3)} 0%, 
                            transparent 70%
                          )`,
                          animation: 'ripple 0.6s ease-out'
                        }}
                      />
                    )}
                    
                    <div className="relative">
                      <item.icon 
                        className={cn(
                          "h-6 w-6 transition-all duration-300",
                          isActive && "scale-110"
                        )}
                        style={isActive ? dynamicColors.textPrimary : { color: 'hsl(var(--muted-foreground))' }}
                      />
                      {item.badge && (
                        <Badge 
                          className="absolute -top-2 -right-2 h-4 w-4 p-0 text-xs border"
                          style={{
                            ...dynamicColors.bgPrimary(0.9),
                            color: 'white',
                            ...dynamicColors.borderPrimary(0.3)
                          }}
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    
                    <span 
                      className={cn(
                        "text-xs font-medium transition-all duration-300",
                        isActive ? "opacity-100" : "opacity-70"
                      )}
                      style={isActive ? dynamicColors.textPrimary : { color: 'hsl(var(--muted-foreground))' }}
                    >
                      {item.title}
                    </span>
                  </>
                )}
              </NavLink>
            ))}

            {/* Floating Action Button */}
            <button
              onClick={() => {
                toggleExpanded();
                triggerHaptic();
              }}
              className={cn(
                "relative flex items-center justify-center w-14 h-14 rounded-2xl transition-all duration-300",
                "shadow-lg hover:scale-110 active:scale-95",
                isExpanded && "rotate-45"
              )}
              style={{
                background: dynamicColors.gradientPrimary,
                boxShadow: `
                  0 8px 24px ${dynamicColors.getColorHsl('medium', 0.3)},
                  0 4px 8px ${dynamicColors.getColorHsl('medium', 0.2)},
                  inset 0 1px 0 ${dynamicColors.getColorHsl('light', 0.2)}
                `
              }}
            >
              <Plus className="h-6 w-6 text-white drop-shadow-sm" />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
