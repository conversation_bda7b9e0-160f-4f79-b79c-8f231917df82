import React, { useState, useRef, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Home, Package, BarChart3, DollarSign, Plus, 
  ChevronUp, Search, ChevronDown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDynamicColors } from '@/hooks/use-theme-colors';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  primaryNavItems, 
  menuCategories, 
  getMenuItemsByCategory, 
  searchMenuItems, 
  getRecentItems 
} from '@/data/navigationData';

interface BottomNavigationProps {
  onMenuToggle: () => void;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({ onMenuToggle }) => {
  const dynamicColors = useDynamicColors();
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeRipple, setActiveRipple] = useState<{ x: number; y: number; id: string } | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const bottomNavRef = useRef<HTMLDivElement>(null);

  // Get categorized menu items
  const categorizedMenuItems = getMenuItemsByCategory();
  const recentItems = getRecentItems();
  const searchResults = searchMenuItems(searchQuery);

  const handleItemClick = (event: React.MouseEvent, item: any) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setActiveRipple({ x, y, id: item.id });
    setTimeout(() => setActiveRipple(null), 600);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const triggerHaptic = () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (bottomNavRef.current && !bottomNavRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  return (
    <>
      {/* Backdrop for expanded state */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-all duration-300"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* Bottom Navigation Container */}
      <div
        ref={bottomNavRef}
        className="fixed bottom-0 left-0 right-0 z-50 pb-safe"
      >
        {/* Expanded Menu Drawer */}
        {isExpanded && (
          <div
            className="absolute bottom-full left-4 right-4 mb-4 rounded-2xl border shadow-2xl overflow-hidden max-h-[60vh] slide-up-spring"
            style={{
              background: `linear-gradient(135deg, 
                hsl(var(--background) / 0.95), 
                ${dynamicColors.getColorHsl('subtle', 0.1)}
              )`,
              backdropFilter: 'blur(24px) saturate(180%)',
              ...dynamicColors.borderPrimary(0.15)
            }}
          >
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-lg" style={dynamicColors.textPrimary}>
                  All Features
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleExpanded}
                  className="rounded-full"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>

              {/* Search Bar */}
              <div className="relative">
                <div
                  className="flex items-center gap-2 px-3 py-2 rounded-xl border"
                  style={{
                    background: `linear-gradient(135deg, 
                      ${dynamicColors.getColorHsl('subtle', 0.2)}, 
                      ${dynamicColors.getColorHsl('light', 0.1)}
                    )`,
                    ...dynamicColors.borderPrimary(0.2)
                  }}
                >
                  <Search className="h-4 w-4" style={dynamicColors.textPrimary} />
                  <input
                    type="text"
                    placeholder="Search all features..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent border-none outline-none text-sm"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="overflow-y-auto max-h-[40vh] space-y-4">
                {searchQuery ? (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Search Results</h4>
                    {searchResults.length > 0 ? (
                      searchResults.map((item, index) => (
                        <NavLink
                          key={index}
                          to={item.path || "#"}
                          onClick={() => {
                            triggerHaptic();
                            setIsExpanded(false);
                            setSearchQuery('');
                          }}
                          className="flex items-center justify-between p-3 rounded-xl transition-all duration-200 hover:scale-105"
                          style={{
                            background: `linear-gradient(135deg, 
                              ${dynamicColors.getColorHsl('light', 0.1)}, 
                              ${dynamicColors.getColorHsl('subtle', 0.05)}
                            )`
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <item.icon className="h-5 w-5" style={dynamicColors.textPrimary} />
                            <div>
                              <span className="text-sm font-medium">{item.title}</span>
                              {item.category && (
                                <div className="text-xs text-muted-foreground">{item.category}</div>
                              )}
                            </div>
                          </div>
                          {item.comingSoon && (
                            <span className="text-xs text-orange-500">Soon</span>
                          )}
                          {item.available && !item.comingSoon && (
                            <span className="text-xs text-green-500">✓</span>
                          )}
                        </NavLink>
                      ))
                    ) : (
                      <div className="text-center text-sm text-muted-foreground py-4">
                        No features found matching "{searchQuery}"
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Recent Items */}
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-2">Recent</h4>
                      <div className="space-y-2">
                        {recentItems.map((item, index) => (
                          <NavLink
                            key={index}
                            to={item.path || "#"}
                            onClick={() => {
                              triggerHaptic();
                              setIsExpanded(false);
                            }}
                            className="flex items-center gap-3 p-3 rounded-xl transition-all duration-200 hover:scale-105"
                            style={{
                              background: `linear-gradient(135deg, 
                                ${dynamicColors.getColorHsl('light', 0.1)}, 
                                ${dynamicColors.getColorHsl('subtle', 0.05)}
                              )`
                            }}
                          >
                            <item.icon className="h-5 w-5" style={dynamicColors.textPrimary} />
                            <span className="text-sm font-medium">{item.title}</span>
                          </NavLink>
                        ))}
                      </div>
                    </div>

                    {/* All Menu Categories */}
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-2">All Features</h4>
                      <div className="space-y-2">
                        {Object.entries(categorizedMenuItems).map(([categoryKey, items]) => {
                          const category = menuCategories[categoryKey as keyof typeof menuCategories];
                          if (!category) return null;
                          
                          return (
                            <div key={categoryKey}>
                              <button
                                onClick={() => {
                                  setExpandedCategories(prev => ({
                                    ...prev,
                                    [categoryKey]: !prev[categoryKey]
                                  }));
                                }}
                                className="flex items-center justify-between w-full p-2 rounded-lg transition-all duration-200"
                                style={{
                                  background: `linear-gradient(135deg, 
                                    ${dynamicColors.getColorHsl('light', 0.05)}, 
                                    ${dynamicColors.getColorHsl('subtle', 0.02)}
                                  )`
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  <category.icon className="h-4 w-4" style={dynamicColors.textPrimary} />
                                  <span className="text-sm font-medium">{category.title}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {items.length}
                                  </Badge>
                                </div>
                                <ChevronDown 
                                  className={cn(
                                    "h-4 w-4 transition-transform duration-200",
                                    expandedCategories[categoryKey] && "rotate-180"
                                  )}
                                />
                              </button>
                              
                              {expandedCategories[categoryKey] && (
                                <div className="ml-6 mt-1 space-y-1">
                                  {items.map((item, itemIndex) => (
                                    <NavLink
                                      key={itemIndex}
                                      to={item.path || "#"}
                                      onClick={() => {
                                        triggerHaptic();
                                        setIsExpanded(false);
                                      }}
                                      className="flex items-center justify-between p-2 rounded-lg transition-all duration-200 hover:scale-105"
                                      style={{
                                        background: `linear-gradient(135deg, 
                                          ${dynamicColors.getColorHsl('light', 0.08)}, 
                                          ${dynamicColors.getColorHsl('subtle', 0.04)}
                                        )`
                                      }}
                                    >
                                      <div className="flex items-center gap-2">
                                        <item.icon className="h-4 w-4" style={dynamicColors.textPrimary} />
                                        <span className="text-xs font-medium">{item.title}</span>
                                      </div>
                                      {item.comingSoon && (
                                        <span className="text-xs text-orange-500">Soon</span>
                                      )}
                                      {item.available && !item.comingSoon && (
                                        <span className="text-xs text-green-500">✓</span>
                                      )}
                                    </NavLink>
                                  ))}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Main Bottom Navigation Bar */}
        <div 
          className="mx-4 mb-4 rounded-2xl border shadow-2xl overflow-hidden"
          style={{
            background: `linear-gradient(135deg, 
              hsl(var(--background) / 0.95), 
              ${dynamicColors.getColorHsl('subtle', 0.08)}
            )`,
            backdropFilter: 'blur(24px) saturate(180%)',
            ...dynamicColors.borderPrimary(0.15),
            boxShadow: `
              0 20px 40px -12px ${dynamicColors.getColorHsl('medium', 0.25)},
              0 8px 16px -8px ${dynamicColors.getColorHsl('medium', 0.15)},
              inset 0 1px 0 ${dynamicColors.getColorHsl('light', 0.1)}
            `
          }}
        >
          <div className="flex items-center justify-between px-2 py-2">
            {primaryNavItems.map((item, index) => (
              <NavLink
                key={item.id}
                to={item.path}
                onClick={(e) => {
                  handleItemClick(e, item);
                  triggerHaptic();
                }}
                className={({ isActive }) => cn(
                  "relative flex flex-col items-center gap-1 px-4 py-3 rounded-xl transition-all duration-300 overflow-hidden",
                  "min-w-[64px] min-h-[64px] justify-center",
                  isActive && "scale-105"
                )}
                style={({ isActive }) => isActive ? {
                  background: `linear-gradient(135deg, 
                    ${dynamicColors.getColorHsl('medium', 0.15)}, 
                    ${dynamicColors.getColorHsl('light', 0.1)}
                  )`,
                  boxShadow: `
                    0 4px 12px ${dynamicColors.getColorHsl('medium', 0.2)},
                    inset 0 1px 0 ${dynamicColors.getColorHsl('light', 0.2)}
                  `
                } : {}}
              >
                {({ isActive }) => (
                  <>
                    {/* Ripple Effect */}
                    {activeRipple && activeRipple.id === item.id && (
                      <div
                        className="absolute inset-0 pointer-events-none"
                        style={{
                          background: `radial-gradient(circle at ${activeRipple.x}px ${activeRipple.y}px, 
                            ${dynamicColors.getColorHsl('medium', 0.3)} 0%, 
                            transparent 70%
                          )`,
                          animation: 'ripple 0.6s ease-out'
                        }}
                      />
                    )}
                    
                    <div className="relative">
                      <item.icon 
                        className={cn(
                          "h-6 w-6 transition-all duration-300",
                          isActive && "scale-110"
                        )}
                        style={isActive ? dynamicColors.textPrimary : { color: 'hsl(var(--muted-foreground))' }}
                      />
                      {item.badge && (
                        <Badge 
                          className="absolute -top-2 -right-2 h-4 w-4 p-0 text-xs border"
                          style={{
                            ...dynamicColors.bgPrimary(0.9),
                            color: 'white',
                            ...dynamicColors.borderPrimary(0.3)
                          }}
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    
                    <span 
                      className={cn(
                        "text-xs font-medium transition-all duration-300",
                        isActive ? "opacity-100" : "opacity-70"
                      )}
                      style={isActive ? dynamicColors.textPrimary : { color: 'hsl(var(--muted-foreground))' }}
                    >
                      {item.title}
                    </span>
                  </>
                )}
              </NavLink>
            ))}

            {/* Floating Action Button */}
            <button
              onClick={() => {
                toggleExpanded();
                triggerHaptic();
              }}
              className={cn(
                "relative flex items-center justify-center w-14 h-14 rounded-2xl transition-all duration-300",
                "shadow-lg hover:scale-110 active:scale-95",
                isExpanded && "rotate-45"
              )}
              style={{
                background: dynamicColors.gradientPrimary,
                boxShadow: `
                  0 8px 24px ${dynamicColors.getColorHsl('medium', 0.3)},
                  0 4px 8px ${dynamicColors.getColorHsl('medium', 0.2)},
                  inset 0 1px 0 ${dynamicColors.getColorHsl('light', 0.2)}
                `
              }}
            >
              <Plus className="h-6 w-6 text-white drop-shadow-sm" />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
