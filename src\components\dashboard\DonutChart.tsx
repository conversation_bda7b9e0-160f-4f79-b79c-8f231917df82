
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>, Pie, Pie<PERSON>hart as Recharts<PERSON>ie<PERSON><PERSON>, ResponsiveContainer, Tooltip } from "recharts";
import { cn } from "@/lib/utils";

interface DonutChartProps {
  title: string;
  description?: string;
  data: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  className?: string;
  height?: number;
}

export function DonutChart({
  title,
  description,
  data,
  className,
  height = 300,
}: DonutChartProps) {
  const [chartData, setChartData] = useState(data.map(item => ({ ...item, value: 0 })));
  
  useEffect(() => {
    // Animate data loading
    const animateData = () => {
      setChartData(data);
    };
    
    const timer = setTimeout(animateData, 300);
    return () => clearTimeout(timer);
  }, [data]);
  
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center">
        <div className="relative w-full" style={{ height }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                paddingAngle={4}
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </RechartsPieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Total</p>
              <p className="text-2xl font-bold">{total.toLocaleString()}</p>
            </div>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4 w-full">
          {data.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: item.color }} 
              />
              <span className="text-sm text-muted-foreground">{item.name}</span>
              <span className="text-sm font-medium ml-auto">
                {((item.value / total) * 100).toFixed(0)}%
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
