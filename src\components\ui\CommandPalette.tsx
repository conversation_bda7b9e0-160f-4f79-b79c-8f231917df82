import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Search, 
  Command, 
  ArrowRight, 
  Clock, 
  Star, 
  Zap,
  FileText,
  Users,
  Settings,
  Calculator,
  BarChart3,
  Package,
  Wrench,
  Shield,
  Truck,
  Building
} from 'lucide-react';
import { Button } from './button';
import { Input } from './input';

interface CommandItem {
  id: string;
  title: string;
  description?: string;
  icon: React.ElementType;
  action: () => void;
  category: string;
  keywords: string[];
  shortcut?: string;
}

const RECENT_KEY = 'command-palette-recent';
const FAVORITES_KEY = 'command-palette-favorites';

export const CommandPalette: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentCommands, setRecentCommands] = useState<string[]>(() => 
    JSON.parse(localStorage.getItem(RECENT_KEY) || '[]')
  );
  const [favorites, setFavorites] = useState<string[]>(() => 
    JSON.parse(localStorage.getItem(FAVORITES_KEY) || '[]')
  );
  
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const commands: CommandItem[] = [
    // Navigation
    { id: 'nav-dashboard', title: 'Dashboard', description: 'Go to main dashboard', icon: BarChart3, action: () => navigate('/'), category: 'Navigation', keywords: ['home', 'main', 'overview'] },
    { id: 'nav-financial', title: 'Financial Dashboard', description: 'View financial reports', icon: Calculator, action: () => navigate('/financial'), category: 'Navigation', keywords: ['money', 'finance', 'budget'] },
    { id: 'nav-assets', title: 'Asset Registry', description: 'Manage company assets', icon: Package, action: () => navigate('/assets/registry'), category: 'Navigation', keywords: ['equipment', 'inventory'] },
    { id: 'nav-maintenance', title: 'Work Orders', description: 'View maintenance tasks', icon: Wrench, action: () => navigate('/maintenance/work-orders'), category: 'Navigation', keywords: ['repair', 'fix', 'service'] },
    { id: 'nav-hr', title: 'Employee Management', description: 'Manage staff and HR', icon: Users, action: () => navigate('/hr/employees'), category: 'Navigation', keywords: ['staff', 'people', 'human resources'] },
    { id: 'nav-safety', title: 'Safety & Compliance', description: 'HSE management', icon: Shield, action: () => navigate('/hse/incidents'), category: 'Navigation', keywords: ['safety', 'health', 'compliance'] },
    { id: 'nav-fleet', title: 'Fleet Management', description: 'Manage vehicles', icon: Truck, action: () => navigate('/fleet/vehicles'), category: 'Navigation', keywords: ['vehicles', 'transport'] },
    { id: 'nav-projects', title: 'Project Management', description: 'View project status', icon: Building, action: () => navigate('/projects/list'), category: 'Navigation', keywords: ['projects', 'tasks'] },
    
    // Quick Actions
    { id: 'action-new-workorder', title: 'Create Work Order', description: 'Create new maintenance task', icon: Wrench, action: () => navigate('/maintenance/work-orders'), category: 'Quick Actions', keywords: ['new', 'create', 'maintenance'] },
    { id: 'action-add-employee', title: 'Add Employee', description: 'Register new staff member', icon: Users, action: () => navigate('/hr/employees'), category: 'Quick Actions', keywords: ['new', 'hire', 'staff'] },
    { id: 'action-report-incident', title: 'Report Incident', description: 'Log safety incident', icon: Shield, action: () => navigate('/hse/incidents'), category: 'Quick Actions', keywords: ['safety', 'report', 'incident'] },
    
    // Reports
    { id: 'report-financial', title: 'Financial Reports', description: 'Generate financial analytics', icon: FileText, action: () => navigate('/analytics/financial'), category: 'Reports', keywords: ['report', 'analytics', 'finance'] },
    { id: 'report-assets', title: 'Asset Reports', description: 'Asset performance analytics', icon: BarChart3, action: () => navigate('/analytics/assets'), category: 'Reports', keywords: ['report', 'assets', 'performance'] },
    
    // Settings
    { id: 'settings-theme', title: 'Theme Settings', description: 'Customize appearance', icon: Settings, action: () => {}, category: 'Settings', keywords: ['theme', 'appearance', 'customize'] },
  ];

  const filteredCommands = commands.filter(command => {
    if (!query) return true;
    const searchTerm = query.toLowerCase();
    return (
      command.title.toLowerCase().includes(searchTerm) ||
      command.description?.toLowerCase().includes(searchTerm) ||
      command.keywords.some(keyword => keyword.includes(searchTerm))
    );
  });

  const groupedCommands = filteredCommands.reduce((acc, command) => {
    if (!acc[command.category]) acc[command.category] = [];
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, CommandItem[]>);

  const executeCommand = (command: CommandItem) => {
    // Add to recent commands
    const newRecent = [command.id, ...recentCommands.filter(id => id !== command.id)].slice(0, 5);
    setRecentCommands(newRecent);
    localStorage.setItem(RECENT_KEY, JSON.stringify(newRecent));
    
    command.action();
    setIsOpen(false);
    setQuery('');
    setSelectedIndex(0);
  };

  const toggleFavorite = (commandId: string) => {
    const newFavorites = favorites.includes(commandId)
      ? favorites.filter(id => id !== commandId)
      : [...favorites, commandId];
    setFavorites(newFavorites);
    localStorage.setItem(FAVORITES_KEY, JSON.stringify(newFavorites));
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(true);
      }
      
      if (isOpen) {
        if (e.key === 'Escape') {
          setIsOpen(false);
          setQuery('');
          setSelectedIndex(0);
        } else if (e.key === 'ArrowDown') {
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, filteredCommands.length - 1));
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
        } else if (e.key === 'Enter') {
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            executeCommand(filteredCommands[selectedIndex]);
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, filteredCommands, selectedIndex]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Reset selection when query changes
  useEffect(() => {
    setSelectedIndex(0);
  }, [query]);

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="glass-border backdrop-blur-strong hover:scale-105 transition-all duration-200"
        onClick={() => setIsOpen(true)}
      >
        <Search className="w-4 h-4 mr-2" />
        Search
        <kbd className="ml-2 px-1.5 py-0.5 text-xs bg-muted rounded border">
          <Command className="w-3 h-3 inline mr-1" />K
        </kbd>
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-20">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 frosted-overlay"
        onClick={() => setIsOpen(false)}
      />
      
      {/* Command Palette */}
      <div className="relative w-full max-w-2xl mx-4 glass-card backdrop-blur-strong border border-border/20 rounded-xl shadow-2xl animate-scale-in overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-border/10">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search commands, pages, and actions..."
              className="pl-10 pr-4 bg-transparent border-none focus:ring-0 text-lg"
            />
          </div>
        </div>

        {/* Content */}
        <div ref={listRef} className="max-h-96 overflow-y-auto">
          {!query && recentCommands.length > 0 && (
            <div className="p-2">
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground flex items-center gap-1">
                <Clock className="w-3 h-3" />
                Recent
              </div>
              {recentCommands.slice(0, 3).map(commandId => {
                const command = commands.find(c => c.id === commandId);
                if (!command) return null;
                return (
                  <CommandItem
                    key={command.id}
                    command={command}
                    isSelected={false}
                    isFavorite={favorites.includes(command.id)}
                    onExecute={() => executeCommand(command)}
                    onToggleFavorite={() => toggleFavorite(command.id)}
                  />
                );
              })}
            </div>
          )}

          {Object.entries(groupedCommands).map(([category, categoryCommands]) => (
            <div key={category} className="p-2">
              <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                {category}
              </div>
              {categoryCommands.map((command, index) => {
                const globalIndex = filteredCommands.indexOf(command);
                return (
                  <CommandItem
                    key={command.id}
                    command={command}
                    isSelected={globalIndex === selectedIndex}
                    isFavorite={favorites.includes(command.id)}
                    onExecute={() => executeCommand(command)}
                    onToggleFavorite={() => toggleFavorite(command.id)}
                  />
                );
              })}
            </div>
          ))}

          {filteredCommands.length === 0 && query && (
            <div className="p-8 text-center text-muted-foreground">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No commands found for "{query}"</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-border/10 bg-muted/20">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <kbd className="px-1 py-0.5 bg-background rounded border">↑↓</kbd>
                Navigate
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-1 py-0.5 bg-background rounded border">↵</kbd>
                Select
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-1 py-0.5 bg-background rounded border">Esc</kbd>
                Close
              </span>
            </div>
            <span className="flex items-center gap-1">
              <Zap className="w-3 h-3" />
              Quick Actions
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

interface CommandItemProps {
  command: CommandItem;
  isSelected: boolean;
  isFavorite: boolean;
  onExecute: () => void;
  onToggleFavorite: () => void;
}

const CommandItem: React.FC<CommandItemProps> = ({
  command,
  isSelected,
  isFavorite,
  onExecute,
  onToggleFavorite
}) => {
  return (
    <div
      className={`flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 group ${
        isSelected ? 'bg-primary/10 text-primary' : 'hover:bg-accent/50'
      }`}
      onClick={onExecute}
    >
      <command.icon className="w-4 h-4 flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm truncate">{command.title}</div>
        {command.description && (
          <div className="text-xs text-muted-foreground truncate">{command.description}</div>
        )}
      </div>
      <div className="flex items-center gap-1">
        <button
          className={`p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity ${
            isFavorite ? 'text-yellow-500' : 'text-muted-foreground hover:text-foreground'
          }`}
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite();
          }}
        >
          <Star className="w-3 h-3" fill={isFavorite ? 'currentColor' : 'none'} />
        </button>
        {isSelected && <ArrowRight className="w-3 h-3 text-muted-foreground" />}
      </div>
    </div>
  );
};
