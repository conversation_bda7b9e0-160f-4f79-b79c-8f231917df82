import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Search, 
  Command, 
  ArrowRight, 
  Clock, 
  Star, 
  Zap,
  Calculator,
  Settings,
  Users,
  Package,
  DollarSign,
  Wrench,
  Shield,
  BarChart3,
  FileText,
  Calendar,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface CommandItem {
  id: string;
  title: string;
  description?: string;
  icon: React.ComponentType<any>;
  action: () => void;
  category: string;
  keywords: string[];
  shortcut?: string;
  priority?: number;
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentCommands, setRecentCommands] = useState<string[]>([]);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Command definitions
  const commands: CommandItem[] = [
    // Navigation
    { id: 'nav-dashboard', title: 'Dashboard', description: 'Go to main dashboard', icon: BarChart3, action: () => navigate('/'), category: 'Navigation', keywords: ['home', 'main', 'overview'] },
    { id: 'nav-financial', title: 'Financial Overview', description: 'View financial dashboard', icon: DollarSign, action: () => navigate('/financial'), category: 'Navigation', keywords: ['money', 'finance', 'revenue'] },
    { id: 'nav-assets', title: 'Asset Management', description: 'Manage company assets', icon: Package, action: () => navigate('/assets/registry'), category: 'Navigation', keywords: ['equipment', 'inventory'] },
    { id: 'nav-hr', title: 'Human Resources', description: 'Employee management', icon: Users, action: () => navigate('/hr/employees'), category: 'Navigation', keywords: ['staff', 'people', 'employees'] },
    { id: 'nav-maintenance', title: 'Maintenance', description: 'Work orders and maintenance', icon: Wrench, action: () => navigate('/maintenance/work-orders'), category: 'Navigation', keywords: ['repair', 'service'] },
    { id: 'nav-safety', title: 'Safety & Compliance', description: 'HSE management', icon: Shield, action: () => navigate('/hse/incidents'), category: 'Navigation', keywords: ['health', 'safety', 'compliance'] },
    
    // Quick Actions
    { id: 'action-new-workorder', title: 'Create Work Order', description: 'Start a new maintenance request', icon: Wrench, action: () => navigate('/maintenance/work-orders'), category: 'Quick Actions', keywords: ['new', 'create', 'maintenance'], priority: 1 },
    { id: 'action-add-employee', title: 'Add Employee', description: 'Register new team member', icon: Users, action: () => navigate('/hr/employees'), category: 'Quick Actions', keywords: ['new', 'hire', 'staff'], priority: 1 },
    { id: 'action-financial-report', title: 'Generate Financial Report', description: 'Create financial analysis', icon: FileText, action: () => navigate('/analytics/financial'), category: 'Quick Actions', keywords: ['report', 'analysis'], priority: 1 },
    
    // Settings & Tools
    { id: 'tool-calculator', title: 'Calculator', description: 'Quick calculations', icon: Calculator, action: () => {}, category: 'Tools', keywords: ['math', 'calculate'] },
    { id: 'tool-calendar', title: 'Calendar', description: 'View schedule', icon: Calendar, action: () => {}, category: 'Tools', keywords: ['date', 'schedule', 'events'] },
    { id: 'settings-preferences', title: 'Preferences', description: 'App settings and configuration', icon: Settings, action: () => {}, category: 'Settings', keywords: ['config', 'options'] },
  ];

  // Filter commands based on query
  const filteredCommands = commands.filter(command => {
    if (!query) return true;
    const searchTerm = query.toLowerCase();
    return (
      command.title.toLowerCase().includes(searchTerm) ||
      command.description?.toLowerCase().includes(searchTerm) ||
      command.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm)) ||
      command.category.toLowerCase().includes(searchTerm)
    );
  }).sort((a, b) => (b.priority || 0) - (a.priority || 0));

  // Group commands by category
  const groupedCommands = filteredCommands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = [];
    }
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, CommandItem[]>);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, filteredCommands.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            executeCommand(filteredCommands[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredCommands, onClose]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
      setQuery('');
      setSelectedIndex(0);
    }
  }, [isOpen]);

  // Execute command
  const executeCommand = (command: CommandItem) => {
    command.action();
    
    // Add to recent commands
    setRecentCommands(prev => {
      const updated = [command.id, ...prev.filter(id => id !== command.id)].slice(0, 5);
      localStorage.setItem('command-palette-recent', JSON.stringify(updated));
      return updated;
    });
    
    onClose();
  };

  // Load recent commands
  useEffect(() => {
    const saved = localStorage.getItem('command-palette-recent');
    if (saved) {
      setRecentCommands(JSON.parse(saved));
    }
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm animate-fade-in">
      <div className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl">
        <div className="mx-4 bg-card/95 backdrop-blur-xl border border-border rounded-xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="flex items-center gap-3 p-4 border-b border-border">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Command className="h-5 w-5" />
              <span className="text-sm font-medium">Command Palette</span>
            </div>
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                ref={inputRef}
                type="text"
                placeholder="Search commands, pages, and actions..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 text-sm"
              />
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <span className="text-xs">ESC</span>
            </Button>
          </div>

          {/* Content */}
          <div ref={listRef} className="max-h-96 overflow-y-auto">
            {!query && recentCommands.length > 0 && (
              <div className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">Recent</span>
                </div>
                <div className="space-y-1">
                  {recentCommands.slice(0, 3).map((commandId) => {
                    const command = commands.find(c => c.id === commandId);
                    if (!command) return null;
                    return (
                      <button
                        key={command.id}
                        onClick={() => executeCommand(command)}
                        className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors text-left"
                      >
                        <command.icon className="h-4 w-4 text-muted-foreground" />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{command.title}</div>
                          {command.description && (
                            <div className="text-xs text-muted-foreground">{command.description}</div>
                          )}
                        </div>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </button>
                    );
                  })}
                </div>
                <Separator className="my-4" />
              </div>
            )}

            {Object.entries(groupedCommands).map(([category, categoryCommands], categoryIndex) => (
              <div key={category} className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-sm font-medium text-muted-foreground">{category}</span>
                  <Badge variant="secondary" className="text-xs">{categoryCommands.length}</Badge>
                </div>
                <div className="space-y-1">
                  {categoryCommands.map((command, index) => {
                    const globalIndex = filteredCommands.indexOf(command);
                    const isSelected = globalIndex === selectedIndex;
                    
                    return (
                      <button
                        key={command.id}
                        onClick={() => executeCommand(command)}
                        className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 text-left ${
                          isSelected 
                            ? 'bg-primary/10 border border-primary/20 scale-[1.02]' 
                            : 'hover:bg-accent/50'
                        }`}
                      >
                        <command.icon className={`h-4 w-4 ${isSelected ? 'text-primary' : 'text-muted-foreground'}`} />
                        <div className="flex-1">
                          <div className={`font-medium text-sm ${isSelected ? 'text-primary' : ''}`}>
                            {command.title}
                          </div>
                          {command.description && (
                            <div className="text-xs text-muted-foreground">{command.description}</div>
                          )}
                        </div>
                        {command.shortcut && (
                          <Badge variant="outline" className="text-xs">{command.shortcut}</Badge>
                        )}
                        <ArrowRight className={`h-4 w-4 ${isSelected ? 'text-primary' : 'text-muted-foreground'}`} />
                      </button>
                    );
                  })}
                </div>
              </div>
            ))}

            {filteredCommands.length === 0 && query && (
              <div className="p-8 text-center">
                <Search className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
                <div className="text-sm font-medium mb-1">No commands found</div>
                <div className="text-xs text-muted-foreground">
                  Try searching for "dashboard", "financial", or "assets"
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-border bg-muted/30">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-4">
                <span>↑↓ Navigate</span>
                <span>↵ Select</span>
                <span>ESC Close</span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                <span>Quick Actions</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook for command palette
export const useCommandPalette = () => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    isOpen,
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  };
};
