
import { useState } from "react";
import { Sidebar } from "./Sidebar";
import { Navbar } from "./Navbar";
import { useAdvancedResponsive } from "@/hooks/use-advanced-responsive";
import { cn } from "@/lib/utils";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const responsive = useAdvancedResponsive();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className={cn(
      "flex h-screen overflow-hidden bg-background transition-all duration-300",
      responsive.isMobile && "pb-safe"
    )}>
      {/* Sidebar - handles its own responsive behavior */}
      <Sidebar
        open={sidebarOpen && !responsive.isMobile}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content Area */}
      <div className={cn(
        "flex-1 flex flex-col overflow-hidden transition-all duration-300",
        responsive.isMobile && "pb-20" // Add bottom padding for mobile navigation
      )}>
        {/* Navbar - hidden on mobile when bottom nav is active */}
        {!responsive.isMobile && (
          <Navbar toggleSidebar={toggleSidebar} />
        )}

        {/* Main Content */}
        <main className={cn(
          "flex-1 overflow-y-auto transition-all duration-300",
          responsive.isMobile ? "p-4" : "p-4 md:p-6"
        )}>
          <div className={cn(
            "mx-auto animate-fade-in transition-all duration-300",
            responsive.isMobile ? "max-w-full" : "max-w-7xl"
          )}>
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
