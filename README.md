# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/3cdc0dc6-72a7-4f9b-8fef-0d3696d67961

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/3cdc0dc6-72a7-4f9b-8fef-0d3696d67961) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Main Frontend Component Structure

```mermaid
flowchart TD
  A["App.tsx"] --> B["Providers"]
  B --> B1["QueryClientProvider"]
  B --> B2["ThemeProvider"]
  B --> B3["TooltipProvider"]
  B --> B4["Toaster/Sonner"]
  A --> C["BrowserRouter"]
  C --> D["Routes"]
  D --> D1["Index (Home)"]
  D --> D2["Financial"]
  D --> D3["HR"]
  D --> D4["Maintenance"]
  D --> D5["Inventory"]
  D --> D6["Projects"]
  D --> D7["Fleet"]
  D --> D8["HSE"]
  D --> D9["Engineering"]
  D --> D10["Analytics"]
  D --> D11["NotFound"]
  subgraph "Layout"
    E1["Sidebar"]
    E2["Navbar"]
    E3["AppLayout"]
  end
  D1 --> E1
  D1 --> E2
  D1 --> E3
  D2 --> E1
  D2 --> E2
  D2 --> E3
  subgraph "UI Components"
    F1["Button"]
    F2["Card"]
    F3["Dialog"]
    F4["Chart"]
    F5["Table"]
    F6["Form"]
    F7["Toast"]
  end
  E1 --> F1
  E2 --> F2
  E3 --> F3
  D2 --> F4
  D2 --> F5
  D2 --> F6
  D2 --> F7
  subgraph "Dashboard Components"
    G1["StatCard"]
    G2["AreaChart"]
    G3["BarChart"]
    G4["DonutChart"]
    G5["ProgressCard"]
  end
  D2 --> G1
  D2 --> G2
  D2 --> G3
  D2 --> G4
  D2 --> G5
```

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/3cdc0dc6-72a7-4f9b-8fef-0d3696d67961) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
